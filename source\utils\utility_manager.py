#!/usr/bin/env python3
"""
UtilityManager - Common utility functions for ClipsMore application.

This module provides static utility methods for common operations like
entry length limiting, tooltip creation, dropdown auto-sizing, and
timestamp formatting.

Features:
- Entry field length limiting
- Tooltip creation and management
- Dropdown auto-sizing based on content
- Timestamp formatting utilities
- Widget validation helpers

Author: ClipsMore Development Team
"""

import tkinter as tk
from tkinter import ttk
from typing import List, Optional
import datetime


class UtilityManager:
    """
    Static utility methods for common UI operations.
    
    This class provides reusable utility functions that can be used
    throughout the application for common tasks.
    """
    
    @staticmethod
    def limit_entry_length(var: tk.StringVar, max_length: int):
        """
        Limit the length of text in a StringVar.
        
        Args:
            var: The StringVar to limit
            max_length: Maximum allowed length
        """
        print(f'[DEBUG] UtilityManager.limit_entry_length called with max_length={max_length}')
        value = var.get()
        if len(value) > max_length:
            var.set(value[:max_length])
    
    @staticmethod
    def create_tooltip(widget: tk.Widget, text: str, theme_manager=None):
        """
        Create a tooltip for a widget.
        
        Args:
            widget: The widget to attach the tooltip to
            text: The tooltip text to display
            theme_manager: Optional theme manager for color coordination
        """
        print(f'[DEBUG] UtilityManager.create_tooltip called for widget with text: {text[:50]}...')
        
        def on_enter(event):
            tooltip = tk.Toplevel()
            tooltip.wm_overrideredirect(True)
            tooltip.wm_geometry(f"+{event.x_root+10}+{event.y_root+10}")
            
            # Use theme colors if theme_manager is provided
            if theme_manager:
                tooltip_bg, tooltip_fg = theme_manager.get_tooltip_colors()
            else:
                # Fallback colors
                tooltip_bg = "#ffffcc"
                tooltip_fg = "#000000"
            
            label = tk.Label(tooltip, text=text, background=tooltip_bg, foreground=tooltip_fg,
                           relief="solid", borderwidth=1, font=("Arial", 9))
            label.pack()
            widget.tooltip = tooltip

        def on_leave(event):
            if hasattr(widget, 'tooltip'):
                widget.tooltip.destroy()
                del widget.tooltip

        widget.bind("<Enter>", on_enter)
        widget.bind("<Leave>", on_leave)
    
    @staticmethod
    def auto_size_dropdown(combo: ttk.Combobox, options: List[str], min_width: int = 25, max_width: int = 50):
        """
        Auto-size a combobox based on its content.
        
        Args:
            combo: The combobox to resize
            options: List of options in the combobox
            min_width: Minimum width in characters
            max_width: Maximum width in characters
        """
        print(f'[DEBUG] UtilityManager.auto_size_dropdown called with {len(options)} options')
        
        if not options:
            combo.configure(width=min_width)
            return
        
        # Find the longest option
        max_length = max(len(str(option)) for option in options)
        
        # Add padding and constrain to min/max bounds
        optimal_width = max(min_width, min(max_width, max_length + 3))
        combo.configure(width=optimal_width)
        
        print(f'[DEBUG] Set combobox width to {optimal_width} characters')
    
    @staticmethod
    def format_timestamp(timestamp: str, format_type: str = "readable") -> str:
        """
        Format a timestamp string for display.
        
        Args:
            timestamp: The timestamp string to format
            format_type: Type of formatting ("readable", "short", "iso")
            
        Returns:
            Formatted timestamp string
        """
        print(f'[DEBUG] UtilityManager.format_timestamp called with format_type={format_type}')
        
        try:
            # Try to parse the timestamp
            if isinstance(timestamp, str):
                # Try different timestamp formats
                formats_to_try = [
                    "%Y-%m-%d %H:%M:%S",
                    "%Y-%m-%d %H:%M:%S.%f",
                    "%Y-%m-%dT%H:%M:%S",
                    "%Y-%m-%dT%H:%M:%S.%f",
                    "%Y-%m-%d",
                ]
                
                dt = None
                for fmt in formats_to_try:
                    try:
                        dt = datetime.datetime.strptime(timestamp, fmt)
                        break
                    except ValueError:
                        continue
                
                if dt is None:
                    # If parsing fails, return original
                    return timestamp
            else:
                dt = timestamp
            
            # Format based on type
            if format_type == "readable":
                return dt.strftime("%B %d, %Y at %I:%M %p")
            elif format_type == "short":
                return dt.strftime("%m/%d/%Y %H:%M")
            elif format_type == "iso":
                return dt.isoformat()
            else:
                return dt.strftime("%Y-%m-%d %H:%M:%S")
                
        except Exception as e:
            print(f'[ERROR] Failed to format timestamp: {e}')
            return str(timestamp)
    
    @staticmethod
    def validate_text_input(text: str, max_length: int = None, allowed_chars: str = None) -> bool:
        """
        Validate text input based on criteria.
        
        Args:
            text: Text to validate
            max_length: Maximum allowed length
            allowed_chars: String of allowed characters (None for no restriction)
            
        Returns:
            True if text is valid
        """
        print(f'[DEBUG] UtilityManager.validate_text_input called for text: {text[:20]}...')
        
        if max_length and len(text) > max_length:
            return False
        
        if allowed_chars:
            for char in text:
                if char not in allowed_chars:
                    return False
        
        return True
    
    @staticmethod
    def truncate_text(text: str, max_length: int, suffix: str = "...") -> str:
        """
        Truncate text to a maximum length with optional suffix.
        
        Args:
            text: Text to truncate
            max_length: Maximum length including suffix
            suffix: Suffix to add when truncating
            
        Returns:
            Truncated text
        """
        print(f'[DEBUG] UtilityManager.truncate_text called with max_length={max_length}')
        
        if len(text) <= max_length:
            return text
        
        if len(suffix) >= max_length:
            return text[:max_length]
        
        return text[:max_length - len(suffix)] + suffix
    
    @staticmethod
    def center_window(window: tk.Toplevel, width: int = None, height: int = None):
        """
        Center a window on the screen.
        
        Args:
            window: The window to center
            width: Window width (uses current if None)
            height: Window height (uses current if None)
        """
        print('[DEBUG] UtilityManager.center_window called')
        
        # Update window to get current size if not specified
        window.update_idletasks()
        
        if width is None:
            width = window.winfo_width()
        if height is None:
            height = window.winfo_height()
        
        # Get screen dimensions
        screen_width = window.winfo_screenwidth()
        screen_height = window.winfo_screenheight()
        
        # Calculate position
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        
        window.geometry(f"{width}x{height}+{x}+{y}")
    
    @staticmethod
    def bind_entry_validation(entry: tk.Entry, var: tk.StringVar, max_length: int):
        """
        Bind real-time validation to an entry widget.
        
        Args:
            entry: The entry widget
            var: The StringVar associated with the entry
            max_length: Maximum allowed length
        """
        print(f'[DEBUG] UtilityManager.bind_entry_validation called with max_length={max_length}')
        
        def validate(*args):
            UtilityManager.limit_entry_length(var, max_length)
        
        var.trace_add('write', validate)
    
    @staticmethod
    def create_labeled_entry(parent: tk.Widget, label_text: str, var: tk.StringVar, 
                           width: int = 20, max_length: int = None, theme_manager=None) -> tuple[tk.Label, tk.Entry]:
        """
        Create a labeled entry widget with optional validation.
        
        Args:
            parent: Parent widget
            label_text: Text for the label
            var: StringVar for the entry
            width: Entry width
            max_length: Maximum text length (enables validation if set)
            theme_manager: Optional theme manager for styling
            
        Returns:
            Tuple of (label, entry) widgets
        """
        print(f'[DEBUG] UtilityManager.create_labeled_entry called for: {label_text}')
        
        # Get colors from theme manager if available
        if theme_manager:
            colors = theme_manager.get_theme_colors()
            bg_color = colors['bg_color']
            fg_color = colors['fg_color']
            entry_bg = colors['entry_bg']
            entry_fg = colors['entry_fg']
        else:
            # Fallback colors
            bg_color = "#f0f0f0"
            fg_color = "#000000"
            entry_bg = "#ffffff"
            entry_fg = "#000000"
        
        # Create label
        label = tk.Label(parent, text=label_text, bg=bg_color, fg=fg_color)
        
        # Create entry
        entry = tk.Entry(parent, textvariable=var, width=width,
                        bg=entry_bg, fg=entry_fg, insertbackground=entry_fg)
        
        # Add validation if max_length is specified
        if max_length:
            UtilityManager.bind_entry_validation(entry, var, max_length)
        
        return label, entry
    
    @staticmethod
    def safe_destroy_widget(widget: tk.Widget):
        """
        Safely destroy a widget with error handling.
        
        Args:
            widget: Widget to destroy
        """
        print('[DEBUG] UtilityManager.safe_destroy_widget called')
        
        try:
            if widget and widget.winfo_exists():
                widget.destroy()
        except tk.TclError:
            # Widget already destroyed or invalid
            pass
        except Exception as e:
            print(f'[ERROR] Failed to destroy widget: {e}')
    
    @staticmethod
    def get_widget_info(widget: tk.Widget) -> dict:
        """
        Get information about a widget for debugging.
        
        Args:
            widget: Widget to inspect
            
        Returns:
            Dictionary with widget information
        """
        print('[DEBUG] UtilityManager.get_widget_info called')
        
        try:
            return {
                'class': widget.__class__.__name__,
                'width': widget.winfo_width(),
                'height': widget.winfo_height(),
                'x': widget.winfo_x(),
                'y': widget.winfo_y(),
                'children_count': len(widget.winfo_children()),
                'exists': widget.winfo_exists()
            }
        except Exception as e:
            print(f'[ERROR] Failed to get widget info: {e}')
            return {'error': str(e)}
