import os, sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


import sqlite3

# NOTE: All new code should include debug print statements at the start of every function/method.

def setup_database(force_recreate=False):
    print('[DEBUG] setup_database called')
    """Set up the SQLite database with the required tables and views."""
    # Get the absolute path to the DB directory
    current_dir = os.path.dirname(os.path.abspath(__file__))
    db_path = os.path.join(current_dir, 'clipsmore_db.db')
    sql_path = os.path.join(current_dir, 'schema.sql')

    # Check if database already exists and has tables
    if os.path.exists(db_path) and not force_recreate:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        if tables:
            print("Database already exists with tables:", [table[0] for table in tables])
            conn.close()
            return
        conn.close()
    elif os.path.exists(db_path) and force_recreate:
        print("Force recreating database - removing existing file...")
        try:
            os.remove(db_path)
        except PermissionError:
            print("Warning: Could not remove existing database file (in use). Creating new schema anyway...")
            # Continue with recreation by dropping and recreating tables

    # Check if SQL file exists
    if not os.path.exists(sql_path):
        print(f"Error: SQL file not found at {sql_path}")
        return

    # Read SQL file
    print("Reading SQL schema file...")
    with open(sql_path, 'r', encoding='utf-8') as f:
        sql_content = f.read()

    # Create new database
    print("Setting up new database...")
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    # If force recreating, drop existing tables first
    if force_recreate:
        print("Dropping existing tables...")
        cursor.execute("DROP VIEW IF EXISTS clipsmore_vw")
        cursor.execute("DROP TABLE IF EXISTS clipsmore_tbl")
        cursor.execute("DROP TABLE IF EXISTS more_comp_tbl")
        cursor.execute("DROP TABLE IF EXISTS more_bus_tbl")
        cursor.execute("DROP TABLE IF EXISTS clips_tbl")
        cursor.execute("DROP TABLE IF EXISTS sqlite_stat1")
        conn.commit()

    print("Executing SQL schema...")  # Debug print

    # Execute the SQL file content
    # Split by semicolon and execute each statement
    sql_statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]

    for i, statement in enumerate(sql_statements):
        try:
            print(f"Executing statement {i+1}/{len(sql_statements)}: {statement[:50]}...")
            cursor.execute(statement)
        except sqlite3.Error as e:
            print(f"Error executing statement {i+1}: {e}")
            print(f"Statement: {statement}")
            # Continue with other statements

    # Verify tables were created
    print("\nVerifying tables...")  # Debug print
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = cursor.fetchall()
    print("Tables found:", [table[0] for table in tables])

    cursor.execute("SELECT name FROM sqlite_master WHERE type='view'")
    views = cursor.fetchall()
    print("Views found:", [view[0] for view in views])

    cursor.execute("SELECT name FROM sqlite_master WHERE type='index'")
    indexes = cursor.fetchall()
    print("Indexes found:", [index[0] for index in indexes])

    conn.commit()
    conn.close()
    print("\nDatabase setup complete!")  # Debug print

if __name__ == "__main__":
    import sys
    force_recreate = '--force' in sys.argv
    setup_database(force_recreate=force_recreate)