import tkinter as tk
from tkinter import ttk

class UIInitializationHelper:
    """Helper class for UI initialization tasks extracted from UIManager"""
    
    def __init__(self, root, theme_manager):
        print('[DEBUG] UIInitializationHelper.__init__ called')
        self.root = root
        self.theme_manager = theme_manager
    
    def create_top_level_debug_button(self, top_frame, debug_callback):
        """Create a top-level debug button for loading all test data."""
        print('[DEBUG] UIInitializationHelper.create_top_level_debug_button called')
        
        try:
            # Create debug frame in the top frame
            debug_frame = tk.Frame(top_frame, bg=self.theme_manager.bg_color)
            debug_frame.pack(side=tk.RIGHT, padx=10, pady=5)
            
            # Create the debug button
            debug_button = tk.Button(
                debug_frame,
                text="🔧 DEBUG: Load All Test Data",
                command=debug_callback,
                bg="#ff9800",
                fg="white",
                activebackground="#f57c00",
                activeforeground="white",
                font=('Arial', 10, 'bold'),
                relief=tk.RAISED,
                bd=2
            )
            debug_button.pack(side=tk.RIGHT, padx=5)
            
            print('[DEBUG] Top-level debug button created successfully')
            return debug_button
            
        except Exception as e:
            print(f'[ERROR] Failed to create top-level debug button: {e}')
            return None
    
    def create_export_backup_buttons(self, top_frame, export_callback, backup_callback, clear_all_callback):
        """Create export, backup, and clear all data buttons in the top frame."""
        print('[DEBUG] UIInitializationHelper.create_export_backup_buttons called')
        
        try:
            # Create a frame for export and backup buttons in the top frame
            export_backup_frame = tk.Frame(top_frame, bg=self.theme_manager.bg_color)
            export_backup_frame.pack(side=tk.LEFT, padx=10, pady=5)
            
            # Create Export button
            export_button = tk.Button(
                export_backup_frame,
                text="📤 Export",
                command=export_callback,
                bg="#4caf50",
                fg="white",
                activebackground="#388e3c",
                activeforeground="white",
                font=('Arial', 10, 'bold'),
                relief=tk.RAISED,
                bd=2,
                cursor='hand2'
            )
            export_button.pack(side=tk.LEFT, padx=5)
            
            # Create Backup button
            backup_button = tk.Button(
                export_backup_frame,
                text="💾 Backup",
                command=backup_callback,
                bg="#2196f3",
                fg="white",
                activebackground="#1976d2",
                activeforeground="white",
                font=('Arial', 10, 'bold'),
                relief=tk.RAISED,
                bd=2,
                cursor='hand2'
            )
            backup_button.pack(side=tk.LEFT, padx=5)
            
            # Create Clear All Data button
            clear_all_button = tk.Button(
                export_backup_frame,
                text="🗑️ Clear All Data",
                command=clear_all_callback,
                bg="#d32f2f",
                fg="white",
                activebackground="#b71c1c",
                activeforeground="white",
                font=('Arial', 10, 'bold'),
                relief=tk.RAISED,
                bd=2,
                cursor='hand2'
            )
            clear_all_button.pack(side=tk.LEFT, padx=5)
            
            print('[DEBUG] Export, backup, and clear all buttons created successfully')
            return {
                'export_button': export_button,
                'backup_button': backup_button,
                'clear_all_button': clear_all_button
            }
            
        except Exception as e:
            print(f'[ERROR] Failed to create export and backup buttons: {e}')
            return None
    
    def initialize_managers_sequence(self, manager_configs):
        """Initialize managers in the correct sequence with proper dependencies."""
        print('[DEBUG] UIInitializationHelper.initialize_managers_sequence called')
        
        initialized_managers = {}
        
        try:
            for config in manager_configs:
                manager_name = config['name']
                manager_class = config['class']
                init_args = config.get('args', [])
                init_kwargs = config.get('kwargs', {})
                
                print(f'[DEBUG] Initializing {manager_name}')
                
                # Replace placeholders in args/kwargs with actual manager references
                processed_args = self._process_manager_references(init_args, initialized_managers)
                processed_kwargs = self._process_manager_references(init_kwargs, initialized_managers)
                
                # Initialize the manager
                manager_instance = manager_class(*processed_args, **processed_kwargs)
                initialized_managers[manager_name] = manager_instance
                
                print(f'[DEBUG] {manager_name} initialized successfully')
            
            return initialized_managers
            
        except Exception as e:
            print(f'[ERROR] Failed to initialize managers sequence: {e}')
            return {}
    
    def _process_manager_references(self, data, managers):
        """Process manager references in initialization parameters."""
        if isinstance(data, dict):
            return {k: self._process_manager_references(v, managers) for k, v in data.items()}
        elif isinstance(data, list):
            return [self._process_manager_references(item, managers) for item in data]
        elif isinstance(data, str) and data.startswith('$'):
            # Manager reference placeholder
            manager_name = data[1:]  # Remove $ prefix
            return managers.get(manager_name)
        else:
            return data
