# 🗄️ ClipsMore Database Entity-Relationship Diagram

🏠 [ClipsMore](../../../README.md) > 🏗️ [Technical Documentation](../README.md) > 🗄️ Database Schema

## 📊 Overview
This document provides a comprehensive Entity-Relationship (ER) diagram and documentation for the ClipsMore database schema. The database is designed to support clipboard management with hierarchical business case organization and clip assignment functionality. 🏗️

## 📋 Database Schema Version
- **🔢 Current Version**: 2.0
- **📅 Migration Date**: 2025-06-06
- **🔧 Database Engine**: SQLite 3.x
- **🔗 Foreign Key Support**: Enabled

## 📈 Entity-Relationship Diagram

```mermaid
erDiagram
    clips_tbl {
        INTEGER clip_id PK "Primary Key, Auto-increment"
        BLOB clip "Clipboard content (text, binary)"
        TEXT alias "Optional user-defined alias"
        DATETIME timestamp "Creation timestamp"
    }
    
    more_bus_tbl {
        INTEGER more_bus_id PK "Primary Key, Auto-increment"
        TEXT bus_case "Business case name (max 255 chars)"
    }
    
    more_comp_tbl {
        INTEGER more_comp_id PK "Primary Key, Auto-increment"
        INTEGER more_bus_id FK "Foreign Key to more_bus_tbl"
        TEXT bus_component "Component name (max 255 chars)"
    }
    
    clipsmore_tbl {
        INTEGER transaction_id PK "Primary Key, Auto-increment"
        INTEGER clip_id FK "Foreign Key to clips_tbl"
        TEXT alias "Unique alias for clip assignment"
        INTEGER more_bus_id FK "Foreign Key to more_bus_tbl"
        INTEGER more_comp_id FK "Foreign Key to more_comp_tbl (nullable)"
        INTEGER tree_position "Position in tree hierarchy"
        TIMESTAMP created_date "Assignment creation date"
        TIMESTAMP modified_date "Last modification date"
    }
    
    clipsmore_vw {
        INTEGER transaction_id "From clipsmore_tbl"
        INTEGER clip_id "From clipsmore_tbl"
        TEXT alias "From clipsmore_tbl"
        INTEGER tree_position "From clipsmore_tbl"
        TIMESTAMP created_date "From clipsmore_tbl"
        TIMESTAMP modified_date "From clipsmore_tbl"
        BLOB clip_content "From clips_tbl.clip"
        DATETIME clip_timestamp "From clips_tbl.timestamp"
        TEXT business_case_name "From more_bus_tbl.bus_case"
        INTEGER more_bus_id "From clipsmore_tbl"
        TEXT component_name "From more_comp_tbl.bus_component"
        INTEGER more_comp_id "From clipsmore_tbl"
    }

    %% Relationships
    more_bus_tbl ||--o{ more_comp_tbl : "has components"
    clips_tbl ||--o{ clipsmore_tbl : "can be assigned"
    more_bus_tbl ||--o{ clipsmore_tbl : "contains assignments"
    more_comp_tbl ||--o{ clipsmore_tbl : "contains assignments"
    
    %% View relationships (dotted lines for derived data)
    clipsmore_tbl ||..|| clipsmore_vw : "denormalizes"
    clips_tbl ||..|| clipsmore_vw : "joins"
    more_bus_tbl ||..|| clipsmore_vw : "joins"
    more_comp_tbl ||..|| clipsmore_vw : "joins"
```

## 🏗️ Entity Descriptions

### 📋 clips_tbl (Clips Table)
**🎯 Purpose**: Stores clipboard content and metadata
- **🔑 Primary Key**: `clip_id` (INTEGER, AUTO_INCREMENT)
- **📋 Business Rules**:
  - ✅ Each clip must have content (clip field cannot be NULL)
  - ⏰ Timestamp is automatically set on creation
  - 🏷️ Alias is optional and user-defined
  - 📄 Content can be text or binary data

### 🏢 more_bus_tbl (Business Cases Table)
**🎯 Purpose**: Stores business case definitions for organizational hierarchy
- **🔑 Primary Key**: `more_bus_id` (INTEGER, AUTO_INCREMENT)
- **📋 Business Rules**:
  - ✨ Business case names must be unique
  - 📏 Names are limited to 255 characters
  - 🚫 Cannot be deleted if components exist

### 🧩 more_comp_tbl (Components Table)
**🎯 Purpose**: Stores component definitions within business cases
- **🔑 Primary Key**: `more_comp_id` (INTEGER, AUTO_INCREMENT)
- **🔗 Foreign Keys**: `more_bus_id` → `more_bus_tbl.more_bus_id`
- **📋 Business Rules**:
  - 🏢 Components must belong to a business case
  - ✨ Component names must be unique within a business case
  - 🗑️ Cascade delete when parent business case is deleted

### 🔗 clipsmore_tbl (Clip-Business Assignment Table)
**🎯 Purpose**: Transaction table linking clips to business cases/components with aliases
- **🔑 Primary Key**: `transaction_id` (INTEGER, AUTO_INCREMENT)
- **🔗 Foreign Keys**:
  - `clip_id` → `clips_tbl.clip_id` (CASCADE DELETE)
  - `more_bus_id` → `more_bus_tbl.more_bus_id` (CASCADE DELETE)
  - `more_comp_id` → `more_comp_tbl.more_comp_id` (SET NULL)
- **📋 Business Rules**:
  - ✨ Aliases must be globally unique
  - 🔄 Each clip can have multiple assignments with different aliases
  - ❓ Component assignment is optional (business case only assignment allowed)
  - 🌳 Tree position determines display order in UI
  - ⏰ Timestamps track creation and modification

### 📊 clipsmore_vw (Denormalized View)
**🎯 Purpose**: Read-only view providing denormalized data for efficient queries
- **📋 Type**: SQL VIEW (not a physical table)
- **🎯 Usage**: Optimized for reporting and UI display
- **⚡ Performance**: Reduces JOIN operations in application code

## 🔗 Relationships and Constraints

### 1️⃣➡️🔢 One-to-Many Relationships
1. **🏢 more_bus_tbl → 🧩 more_comp_tbl**: One business case can have many components
2. **📋 clips_tbl → 🔗 clipsmore_tbl**: One clip can have many assignments
3. **🏢 more_bus_tbl → 🔗 clipsmore_tbl**: One business case can have many clip assignments
4. **🧩 more_comp_tbl → 🔗 clipsmore_tbl**: One component can have many clip assignments

### 🛡️ Referential Integrity Rules
- **🗑️ CASCADE DELETE**: When a clip is deleted, all its assignments are deleted
- **🗑️ CASCADE DELETE**: When a business case is deleted, all its assignments are deleted
- **🔄 SET NULL**: When a component is deleted, assignments revert to business case only
- **🚫 RESTRICT**: Cannot delete business case if components exist (enforced by application)

### ✨ Unique Constraints
- `clipsmore_tbl.alias` - Global alias uniqueness
- `clipsmore_tbl(clip_id, more_bus_id, more_comp_id)` - Prevents duplicate assignments

## 📊 Indexes and Performance

### 🔑 Primary Indexes
- 🔑 All primary keys have automatic indexes
- 🔗 Foreign key columns have automatic indexes in SQLite

### 🎯 Custom Indexes
```sql
CREATE INDEX idx_clipsmore_alias ON clipsmore_tbl(alias);
CREATE INDEX idx_clipsmore_tree_pos ON clipsmore_tbl(tree_position);
CREATE INDEX idx_clipsmore_bus_id ON clipsmore_tbl(more_bus_id);
CREATE INDEX idx_clipsmore_comp_id ON clipsmore_tbl(more_comp_id);
```

### ⚡ Performance Considerations
- 📊 `clipsmore_vw` eliminates need for complex JOINs in application
- 🌳 Tree position index optimizes UI display ordering
- 🏷️ Alias index supports fast uniqueness validation
- 🔗 Foreign key indexes optimize relationship queries

## 💾 Data Types and Storage

### 📋 Field Specifications
| Table | Field | Type | Size | Nullable | Default |
|-------|-------|------|------|----------|---------|
| clips_tbl | clip_id | INTEGER | - | NO | AUTO_INCREMENT |
| clips_tbl | clip | BLOB | - | NO | - |
| clips_tbl | alias | TEXT | 255 | YES | NULL |
| clips_tbl | timestamp | DATETIME | - | NO | CURRENT_TIMESTAMP |
| more_bus_tbl | more_bus_id | INTEGER | - | NO | AUTO_INCREMENT |
| more_bus_tbl | bus_case | TEXT | 255 | NO | - |
| more_comp_tbl | more_comp_id | INTEGER | - | NO | AUTO_INCREMENT |
| more_comp_tbl | more_bus_id | INTEGER | - | NO | - |
| more_comp_tbl | bus_component | TEXT | 255 | NO | - |
| clipsmore_tbl | transaction_id | INTEGER | - | NO | AUTO_INCREMENT |
| clipsmore_tbl | clip_id | INTEGER | - | NO | - |
| clipsmore_tbl | alias | TEXT | 255 | NO | - |
| clipsmore_tbl | more_bus_id | INTEGER | - | NO | - |
| clipsmore_tbl | more_comp_id | INTEGER | - | YES | NULL |
| clipsmore_tbl | tree_position | INTEGER | - | NO | 0 |
| clipsmore_tbl | created_date | TIMESTAMP | - | NO | CURRENT_TIMESTAMP |
| clipsmore_tbl | modified_date | TIMESTAMP | - | NO | CURRENT_TIMESTAMP |

## 🔄 Migration History

### 📈 Version 1.0 → 2.0 (2025-06-06)
**🔧 Changes Made**:
- ➕ Added `transaction_id` as primary key to `clipsmore_tbl`
- 🏷️ Added `alias` field with unique constraint
- 🌳 Added `tree_position` for UI ordering
- ⏰ Added `created_date` and `modified_date` timestamps
- 📊 Created `clipsmore_vw` denormalized view
- 📈 Added comprehensive indexing strategy
- 🔗 Implemented proper foreign key constraints

**🔧 Migration Script**: `DB/migration_v2.py`
**💾 Backup Location**: `DB/clipsmore_db.db.backup_YYYYMMDD_HHMMSS`

## 📋 Business Rules Summary

1. **📋 Clip Management**:
   - 🔒 Clips are immutable once created (content cannot be changed)
   - 🔄 Clips can be assigned to multiple business contexts with different aliases
   - 🗑️ Deleting a clip removes all its assignments

2. **🏢 Hierarchy Management**:
   - 🏢 Business cases are top-level organizational units
   - 🧩 Components are optional sub-units within business cases
   - 🚫 Components cannot exist without a parent business case

3. **🔗 Assignment Management**:
   - ✨ Each assignment must have a unique alias
   - 🎯 Assignments can be business case only or business case + component
   - 🌳 Tree position determines display order in UI
   - ⏰ Assignment history is tracked with timestamps

4. **🛡️ Data Integrity**:
   - 🔗 All foreign key relationships are enforced
   - 🗑️ Cascade deletes maintain referential integrity
   - ✨ Unique constraints prevent data duplication
   - 📊 Timestamps provide audit trail

## See Also

- **🏛️ [System Architecture](../architecture/System_Architecture.md)** - Overall system design and component architecture
- **📐 [UML Class Diagrams](../uml/Class_Diagrams.md)** - Object-oriented design visualization
- **🔄 [UML Sequence Diagrams](../uml/Sequence_Diagrams.md)** - Interaction flows for key use cases
- **🌐 [C4 Model](../c4/C4_Model.md)** - Hierarchical system visualization
- **🔗 [Dependencies](../dependencies/Dependency_Analysis.md)** - Module and class dependency analysis
- **🏗️ [Technical Documentation Index](../README.md)** - All technical documentation
- **🏠 [Back to ClipsMore](../../../README.md)** - Main project overview

---

🏠 **[Back to ClipsMore](../../../README.md)** | 🏗️ **[Technical Documentation](../README.md)**
