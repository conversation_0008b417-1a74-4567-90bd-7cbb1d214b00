#!/usr/bin/env python3
"""
Backup Manager for ClipsMore
Handles backup operations for the ClipsMore database and configuration.
"""

import os
import sys
import sqlite3
import shutil
import gzip
import hashlib
import json
import zipfile
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Callable

# Add parent directories to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from DB.db_connection import ConnectionPoolManager


class BackupError(Exception):
    """Custom exception for backup operations."""
    pass


class BackupManager:
    """
    Core backup manager for ClipsMore application.
    Handles database backup, compression, verification, and restoration.
    """
    
    def __init__(self, database_manager=None):
        """Initialize the backup manager."""
        print('[DEBUG] BackupManager.__init__ called')
        self.database_manager = database_manager
        self.connection_pool = ConnectionPoolManager()
        self.progress_callback = None
        self.cancel_requested = False
        
        # Default backup configuration
        self.default_backup_config = {
            'compression': True,
            'verification': True,
            'include_config': True,
            'backup_type': 'full'
        }
    
    def set_progress_callback(self, callback: Callable[[int, str], None]):
        """Set callback function for progress updates."""
        print('[DEBUG] BackupManager.set_progress_callback called')
        self.progress_callback = callback
    
    def cancel_backup(self):
        """Cancel the current backup operation."""
        print('[DEBUG] BackupManager.cancel_backup called')
        self.cancel_requested = True
    
    def _update_progress(self, percentage: int, message: str = ""):
        """Update backup progress."""
        if self.progress_callback:
            self.progress_callback(percentage, message)
    
    def _check_cancellation(self):
        """Check if backup has been cancelled."""
        if self.cancel_requested:
            raise BackupError("Backup operation was cancelled")
    
    def create_backup(self, backup_path: str, backup_config: Dict[str, Any] = None) -> bool:
        """
        Create a backup of the ClipsMore database.
        
        Args:
            backup_path: Path where backup should be created
            backup_config: Backup configuration options
                - compression: Enable compression (default: True)
                - verification: Verify backup after creation (default: True)
                - include_config: Include application configuration (default: True)
                - backup_type: Type of backup ('full', 'incremental', 'selective')
        
        Returns:
            True if backup successful, False otherwise
        """
        print(f'[DEBUG] BackupManager.create_backup called for {backup_path}')
        
        try:
            self.cancel_requested = False
            config = {**self.default_backup_config, **(backup_config or {})}
            
            self._update_progress(0, "Starting backup...")
            
            # Validate backup path
            if not self._validate_backup_path(backup_path):
                raise BackupError(f"Invalid backup path: {backup_path}")
            
            # Create backup directory if needed
            backup_dir = os.path.dirname(backup_path)
            if backup_dir and not os.path.exists(backup_dir):
                os.makedirs(backup_dir, exist_ok=True)
            
            self._check_cancellation()
            self._update_progress(10, "Preparing backup...")
            
            # Determine backup file name with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"clipsmore_backup_{timestamp}.db"
            
            if backup_path.endswith('/') or backup_path.endswith('\\'):
                full_backup_path = os.path.join(backup_path, backup_filename)
            else:
                full_backup_path = backup_path
                if not full_backup_path.endswith('.db'):
                    full_backup_path += '.db'
            
            # Create the backup
            backup_info = self._create_database_backup(full_backup_path, config)
            
            self._check_cancellation()
            
            # Compress if requested
            if config.get('compression', True):
                self._update_progress(70, "Compressing backup...")
                compressed_path = self._compress_backup(full_backup_path)
                if compressed_path:
                    os.remove(full_backup_path)  # Remove uncompressed version
                    full_backup_path = compressed_path
                    backup_info['compressed'] = True
                    backup_info['compression_ratio'] = self._calculate_compression_ratio(
                        backup_info['file_size'], os.path.getsize(full_backup_path)
                    )
            
            self._check_cancellation()
            
            # Verify backup if requested
            if config.get('verification', True):
                self._update_progress(85, "Verifying backup...")
                verification_result = self._verify_backup(full_backup_path, config)
                backup_info['verification_status'] = 'passed' if verification_result else 'failed'
                
                if not verification_result:
                    raise BackupError("Backup verification failed")
            
            # Record backup in history
            self._record_backup_history(full_backup_path, backup_info, config)
            
            self._update_progress(100, f"Backup completed: {full_backup_path}")
            return True
            
        except BackupError:
            raise
        except Exception as e:
            raise BackupError(f"Unexpected error during backup: {e}")
    
    def _validate_backup_path(self, backup_path: str) -> bool:
        """Validate that the backup path is writable."""
        print(f'[DEBUG] BackupManager._validate_backup_path called for {backup_path}')
        
        try:
            # Check if it's a directory or file path
            if backup_path.endswith('/') or backup_path.endswith('\\'):
                # Directory path
                directory = backup_path
            else:
                # File path
                directory = os.path.dirname(backup_path)
            
            if directory and not os.path.exists(directory):
                os.makedirs(directory, exist_ok=True)
            
            # Test write access
            test_file = os.path.join(directory or '.', 'backup_test.tmp')
            with open(test_file, 'w') as f:
                f.write('test')
            os.remove(test_file)
            
            return True
            
        except Exception as e:
            print(f'[ERROR] Backup path validation failed: {e}')
            return False
    
    def _create_database_backup(self, backup_path: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """Create the actual database backup."""
        print(f'[DEBUG] BackupManager._create_database_backup called')
        
        self._update_progress(20, "Creating database backup...")
        
        try:
            with self.connection_pool.get_connection() as source_conn:
                # Create backup database
                backup_conn = sqlite3.connect(backup_path)
                
                try:
                    # Use SQLite's backup API for atomic backup
                    source_conn.backup(backup_conn)
                    
                    self._check_cancellation()
                    self._update_progress(50, "Database backup created...")
                    
                    # Add backup metadata
                    self._add_backup_metadata(backup_conn, config)
                    
                    backup_conn.close()
                    
                    # Get file size
                    file_size = os.path.getsize(backup_path)
                    
                    # Calculate checksum
                    checksum = self._calculate_file_checksum(backup_path)
                    
                    return {
                        'file_size': file_size,
                        'checksum': checksum,
                        'backup_type': config.get('backup_type', 'full'),
                        'compressed': False,
                        'compression_ratio': 1.0
                    }
                    
                except Exception as e:
                    backup_conn.close()
                    if os.path.exists(backup_path):
                        os.remove(backup_path)
                    raise
                    
        except sqlite3.Error as e:
            raise BackupError(f"Database backup failed: {e}")
    
    def _add_backup_metadata(self, backup_conn: sqlite3.Connection, config: Dict[str, Any]):
        """Add metadata table to backup."""
        print('[DEBUG] BackupManager._add_backup_metadata called')
        
        cursor = backup_conn.cursor()
        
        # Create metadata table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS backup_metadata (
                key TEXT PRIMARY KEY,
                value TEXT
            )
        """)
        
        # Add metadata
        metadata = {
            'backup_date': datetime.now().isoformat(),
            'backup_type': config.get('backup_type', 'full'),
            'application_version': '2.0',
            'backup_config': json.dumps(config)
        }
        
        for key, value in metadata.items():
            cursor.execute("""
                INSERT OR REPLACE INTO backup_metadata (key, value)
                VALUES (?, ?)
            """, (key, value))
        
        backup_conn.commit()
    
    def _compress_backup(self, backup_path: str) -> Optional[str]:
        """Compress the backup file using gzip."""
        print(f'[DEBUG] BackupManager._compress_backup called')
        
        try:
            compressed_path = backup_path + '.gz'
            
            with open(backup_path, 'rb') as f_in:
                with gzip.open(compressed_path, 'wb') as f_out:
                    shutil.copyfileobj(f_in, f_out)
            
            return compressed_path
            
        except Exception as e:
            print(f'[ERROR] Backup compression failed: {e}')
            return None
    
    def _calculate_compression_ratio(self, original_size: int, compressed_size: int) -> float:
        """Calculate compression ratio."""
        if original_size == 0:
            return 1.0
        return compressed_size / original_size
    
    def _verify_backup(self, backup_path: str, config: Dict[str, Any]) -> bool:
        """Verify the integrity of the backup."""
        print(f'[DEBUG] BackupManager._verify_backup called')
        
        try:
            # Check if file is compressed
            is_compressed = backup_path.endswith('.gz')
            
            if is_compressed:
                # Verify compressed file can be opened
                with gzip.open(backup_path, 'rb') as f:
                    # Try to read first few bytes
                    f.read(1024)
            
            # Try to open as SQLite database
            if is_compressed:
                # Extract to temporary file for verification
                temp_path = backup_path + '.temp'
                with gzip.open(backup_path, 'rb') as f_in:
                    with open(temp_path, 'wb') as f_out:
                        shutil.copyfileobj(f_in, f_out)
                verify_path = temp_path
            else:
                verify_path = backup_path
            
            try:
                # Test database integrity
                conn = sqlite3.connect(verify_path)
                cursor = conn.cursor()
                
                # Run integrity check
                cursor.execute("PRAGMA integrity_check")
                result = cursor.fetchone()
                
                conn.close()
                
                # Clean up temporary file
                if is_compressed and os.path.exists(temp_path):
                    os.remove(temp_path)
                
                return result and result[0] == 'ok'
                
            except sqlite3.Error:
                if is_compressed and os.path.exists(temp_path):
                    os.remove(temp_path)
                return False
                
        except Exception as e:
            print(f'[ERROR] Backup verification failed: {e}')
            return False
    
    def _calculate_file_checksum(self, file_path: str) -> str:
        """Calculate SHA-256 checksum of a file."""
        print(f'[DEBUG] BackupManager._calculate_file_checksum called')
        
        hash_sha256 = hashlib.sha256()
        
        try:
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_sha256.update(chunk)
            return hash_sha256.hexdigest()
            
        except Exception as e:
            print(f'[ERROR] Checksum calculation failed: {e}')
            return ""
    
    def _record_backup_history(self, backup_path: str, backup_info: Dict[str, Any], 
                              config: Dict[str, Any]):
        """Record backup in the backup history table."""
        print('[DEBUG] BackupManager._record_backup_history called')
        
        try:
            with self.connection_pool.get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    INSERT INTO backup_history (
                        backup_type, backup_path, file_size, compression_ratio,
                        verification_status, backup_config, checksum
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    backup_info.get('backup_type', 'full'),
                    backup_path,
                    backup_info.get('file_size', 0),
                    backup_info.get('compression_ratio', 1.0),
                    backup_info.get('verification_status', 'pending'),
                    json.dumps(config),
                    backup_info.get('checksum', '')
                ))
                
                conn.commit()
                
        except sqlite3.Error as e:
            print(f'[ERROR] Failed to record backup history: {e}')
    
    def get_backup_history(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get backup history records."""
        print('[DEBUG] BackupManager.get_backup_history called')
        
        try:
            with self.connection_pool.get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT * FROM backup_history
                    ORDER BY created_date DESC
                    LIMIT ?
                """, (limit,))
                
                columns = [desc[0] for desc in cursor.description]
                rows = cursor.fetchall()
                
                return [dict(zip(columns, row)) for row in rows]
                
        except sqlite3.Error as e:
            print(f'[ERROR] Failed to get backup history: {e}')
            return []

    def verify_backup(self, backup_path: str) -> Dict[str, Any]:
        """Verify backup integrity and validity."""
        print(f'[DEBUG] BackupManager.verify_backup called for {backup_path}')

        try:
            if not os.path.exists(backup_path):
                return {'is_valid': False, 'error': 'Backup file does not exist'}

            # Check if it's a valid ZIP file
            if not zipfile.is_zipfile(backup_path):
                return {'is_valid': False, 'error': 'File is not a valid ZIP archive'}

            with zipfile.ZipFile(backup_path, 'r') as zip_file:
                # Check for required files
                required_files = ['database_export.json', 'backup_metadata.json']
                missing_files = []

                for required_file in required_files:
                    if required_file not in zip_file.namelist():
                        missing_files.append(required_file)

                if missing_files:
                    return {
                        'is_valid': False,
                        'error': f'Missing required files: {", ".join(missing_files)}'
                    }

                # Verify metadata
                try:
                    metadata_content = zip_file.read('backup_metadata.json')
                    metadata = json.loads(metadata_content)

                    if not metadata.get('backup_date') or not metadata.get('database_file'):
                        return {'is_valid': False, 'error': 'Invalid metadata structure'}

                except (json.JSONDecodeError, KeyError) as e:
                    return {'is_valid': False, 'error': f'Metadata validation failed: {e}'}

                # Verify database export
                try:
                    db_content = zip_file.read('database_export.json')
                    db_data = json.loads(db_content)

                    if not isinstance(db_data, dict) or 'data' not in db_data:
                        return {'is_valid': False, 'error': 'Invalid database export structure'}

                except (json.JSONDecodeError, KeyError) as e:
                    return {'is_valid': False, 'error': f'Database export validation failed: {e}'}

            return {'is_valid': True, 'message': 'Backup verification successful'}

        except Exception as e:
            return {'is_valid': False, 'error': f'Verification error: {e}'}

    def extract_backup_data(self, backup_path: str) -> List[Dict[str, Any]]:
        """Extract data from backup file."""
        print(f'[DEBUG] BackupManager.extract_backup_data called for {backup_path}')

        try:
            # First verify the backup
            verification = self.verify_backup(backup_path)
            if not verification['is_valid']:
                raise BackupError(f"Backup verification failed: {verification['error']}")

            with zipfile.ZipFile(backup_path, 'r') as zip_file:
                # Extract database export
                db_content = zip_file.read('database_export.json')
                db_data = json.loads(db_content)

                return db_data.get('data', [])

        except Exception as e:
            raise BackupError(f"Error extracting backup data: {e}")
