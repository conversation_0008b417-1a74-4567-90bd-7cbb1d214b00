"""
ScrollHandler - Global mouse wheel scrolling management for ClipsMore application.

This module provides a centralized scroll handling system that enables global mouse wheel
functionality across the entire application window, automatically routing scroll events
to the appropriate tab content based on the currently active tab.

Features:
- Global mouse wheel binding to root window
- Cross-platform scroll event handling (Windows, macOS, Linux)
- Tab-aware scroll routing
- Intelligent content detection for complex UI layouts
- Error handling and debug logging

Author: ClipsMore Development Team
"""

import tkinter as tk
from typing import Optional, Callable, Any


class ScrollHandler:
    """
    Handles global mouse wheel scrolling for the ClipsMore application.
    
    This class provides a centralized way to manage mouse wheel events across
    the entire application window, automatically routing scroll actions to the
    appropriate tab content based on the currently active tab.
    """
    
    def __init__(self, root: tk.Tk, tab_control: tk.Widget):
        """
        Initialize the ScrollHandler.
        
        Args:
            root: The main Tkinter root window
            tab_control: The notebook widget that manages tabs
        """
        print('[DEBUG] ScrollHandler.__init__ called')
        self.root = root
        self.tab_control = tab_control
        self.ui_manager = None  # Will be set by the UI manager
        
        # Setup global mouse wheel bindings
        self._setup_global_mousewheel()
    
    def set_ui_manager(self, ui_manager: Any) -> None:
        """
        Set the UI manager reference for accessing UI components.
        
        Args:
            ui_manager: The UIManager instance that contains the UI components
        """
        print('[DEBUG] ScrollHandler.set_ui_manager called')
        self.ui_manager = ui_manager
    
    def _setup_global_mousewheel(self) -> None:
        """Setup global mouse wheel support for the entire application window."""
        print('[DEBUG] ScrollHandler._setup_global_mousewheel called')
        
        # Bind mouse wheel events to the root window for global scrolling
        # Windows and MacOS
        self.root.bind("<MouseWheel>", self._on_global_mousewheel)
        # Linux
        self.root.bind("<Button-4>", self._on_global_mousewheel)
        self.root.bind("<Button-5>", self._on_global_mousewheel)
    
    def _on_global_mousewheel(self, event: tk.Event) -> None:
        """
        Handle global mouse wheel scrolling based on active tab.
        
        Args:
            event: The mouse wheel event containing scroll information
        """
        print('[DEBUG] ScrollHandler._on_global_mousewheel called')
        
        if not self.ui_manager:
            print('[WARNING] ScrollHandler: UI manager not set, cannot handle scroll')
            return
        
        # Get the currently active tab
        try:
            current_tab_index = self.tab_control.index(self.tab_control.select())
        except Exception as e:
            print(f'[ERROR] ScrollHandler: Failed to get active tab: {e}')
            return
        
        # Handle different platforms for scroll direction
        delta = self._calculate_scroll_delta(event)
        
        # Route scroll to appropriate tab
        if current_tab_index == 0:  # Clips tab
            self._scroll_clips_tab(delta)
        elif current_tab_index == 1:  # More tab
            self._scroll_more_tab(delta)
        elif current_tab_index == 2:  # About tab
            self._scroll_about_tab(delta)
    
    def _calculate_scroll_delta(self, event: tk.Event) -> int:
        """
        Calculate scroll delta from event for cross-platform compatibility.
        
        Args:
            event: The mouse wheel event
            
        Returns:
            Scroll delta value (-1 for up, 1 for down)
        """
        if event.num == 4 or event.delta > 0:
            return -1  # Scroll up
        elif event.num == 5 or event.delta < 0:
            return 1   # Scroll down
        else:
            return int(-1 * (event.delta / 120))  # Windows/macOS
    
    def _scroll_clips_tab(self, delta: int) -> None:
        """
        Scroll the clips tab canvas.

        Args:
            delta: Scroll direction and amount
        """
        print('[DEBUG] ScrollHandler._scroll_clips_tab called')
        try:
            # Access clips_canvas through ClipManager
            if (hasattr(self.ui_manager, 'clip_manager') and
                self.ui_manager.clip_manager and
                hasattr(self.ui_manager.clip_manager, 'clips_canvas')):
                self.ui_manager.clip_manager.clips_canvas.yview_scroll(delta, "units")
                print('[DEBUG] ScrollHandler: Clips canvas scrolled')
            else:
                print('[DEBUG] ScrollHandler: Clips canvas not available')
        except Exception as e:
            print(f'[ERROR] ScrollHandler: Failed to scroll clips tab: {e}')
    
    def _scroll_more_tab(self, delta: int) -> None:
        """
        Scroll the more tab with synchronized tree and buttons scrolling.
        Both panes are scroll-locked to move together as a unified interface.

        Args:
            delta: Scroll direction and amount
        """
        print('[DEBUG] ScrollHandler._scroll_more_tab called')
        try:
            # Scroll both tree and buttons canvas together (scroll-locked)
            tree_scrolled = False
            buttons_scrolled = False

            # Access tree through TreeManager
            if (hasattr(self.ui_manager, 'tree_manager') and
                self.ui_manager.tree_manager and
                hasattr(self.ui_manager.tree_manager, 'tree')):
                try:
                    tree = self.ui_manager.tree_manager.tree
                    # Get current tree scroll position before scrolling
                    tree_top, tree_bottom = tree.yview()

                    # Only scroll if there's content and we're not at the limits
                    if self._can_scroll_tree(delta, tree_top, tree_bottom):
                        tree.yview_scroll(delta, "units")
                        tree_scrolled = True
                        print('[DEBUG] ScrollHandler: Tree scrolled')
                except Exception as e:
                    print(f'[DEBUG] ScrollHandler: Tree scroll failed: {e}')

            # Access buttons canvas through TreeManager
            if (hasattr(self.ui_manager, 'tree_manager') and
                self.ui_manager.tree_manager and
                hasattr(self.ui_manager.tree_manager, 'buttons_canvas')):
                try:
                    buttons_canvas = self.ui_manager.tree_manager.buttons_canvas
                    # Get current buttons canvas scroll position
                    buttons_top, buttons_bottom = buttons_canvas.yview()

                    # Only scroll if there's content and we're not at the limits
                    if self._can_scroll_buttons(delta, buttons_top, buttons_bottom):
                        buttons_canvas.yview_scroll(delta, "units")
                        buttons_scrolled = True
                        print('[DEBUG] ScrollHandler: Buttons canvas scrolled')
                except Exception as e:
                    print(f'[DEBUG] ScrollHandler: Buttons canvas scroll failed: {e}')

            # Log synchronization status
            if tree_scrolled and buttons_scrolled:
                print('[DEBUG] ScrollHandler: Both panes scrolled in sync')
            elif tree_scrolled or buttons_scrolled:
                print('[DEBUG] ScrollHandler: Partial scroll - one pane reached limit')
            else:
                print('[DEBUG] ScrollHandler: No scrolling - both panes at limits')

        except Exception as e:
            print(f'[ERROR] ScrollHandler: Failed to scroll more tab: {e}')

    def _can_scroll_tree(self, delta: int, top: float, bottom: float) -> bool:
        """
        Check if the tree can be scrolled in the given direction.

        Args:
            delta: Scroll direction (-1 for up, 1 for down)
            top: Current top position (0.0 to 1.0)
            bottom: Current bottom position (0.0 to 1.0)

        Returns:
            True if scrolling is possible, False if at limit
        """
        try:
            # Check if tree has children (content to scroll) through TreeManager
            if (not hasattr(self.ui_manager, 'tree_manager') or
                not self.ui_manager.tree_manager or
                not hasattr(self.ui_manager.tree_manager, 'tree') or
                not self.ui_manager.tree_manager.tree.get_children()):
                return False

            # Check scroll limits
            if delta < 0:  # Scrolling up
                return top > 0.0  # Can scroll up if not at top
            else:  # Scrolling down
                return bottom < 1.0  # Can scroll down if not at bottom

        except Exception as e:
            print(f'[DEBUG] ScrollHandler: Tree scroll check failed: {e}')
            return False

    def _can_scroll_buttons(self, delta: int, top: float, bottom: float) -> bool:
        """
        Check if the buttons canvas can be scrolled in the given direction.

        Args:
            delta: Scroll direction (-1 for up, 1 for down)
            top: Current top position (0.0 to 1.0)
            bottom: Current bottom position (0.0 to 1.0)

        Returns:
            True if scrolling is possible, False if at limit
        """
        try:
            # Check if buttons canvas has content to scroll through TreeManager
            if (not hasattr(self.ui_manager, 'tree_manager') or
                not self.ui_manager.tree_manager or
                not hasattr(self.ui_manager.tree_manager, 'buttons_canvas')):
                return False

            # Get scroll region to check if there's content
            buttons_canvas = self.ui_manager.tree_manager.buttons_canvas
            scroll_region = buttons_canvas.cget('scrollregion')
            if not scroll_region or scroll_region == '0 0 0 0':
                return False  # No content to scroll

            # Check scroll limits
            if delta < 0:  # Scrolling up
                return top > 0.0  # Can scroll up if not at top
            else:  # Scrolling down
                return bottom < 1.0  # Can scroll down if not at bottom

        except Exception as e:
            print(f'[DEBUG] ScrollHandler: Buttons scroll check failed: {e}')
            return False
    
    def _scroll_about_tab(self, delta: int) -> None:
        """
        Scroll the currently active document in the about tab.
        
        Args:
            delta: Scroll direction and amount
        """
        print('[DEBUG] ScrollHandler._scroll_about_tab called')
        try:
            if hasattr(self.ui_manager, 'docs_notebook'):
                # Get the currently selected document tab
                current_doc_tab = self.ui_manager.docs_notebook.select()
                if current_doc_tab:
                    # Find the text widget in the current document tab
                    doc_frame = self.ui_manager.docs_notebook.nametowidget(current_doc_tab)
                    text_widget = self._find_text_widget(doc_frame)
                    if text_widget:
                        text_widget.yview_scroll(delta, "units")
        except Exception as e:
            print(f'[ERROR] ScrollHandler: Failed to scroll about tab: {e}')
    
    def _find_text_widget(self, parent: tk.Widget) -> Optional[tk.Text]:
        """
        Recursively find the first Text widget in a frame.
        
        Args:
            parent: The parent widget to search within
            
        Returns:
            The first Text widget found, or None if none found
        """
        for child in parent.winfo_children():
            if isinstance(child, tk.Text):
                return child
            elif hasattr(child, 'winfo_children'):
                result = self._find_text_widget(child)
                if result:
                    return result
        return None
    
    def cleanup(self) -> None:
        """Clean up scroll handler bindings."""
        print('[DEBUG] ScrollHandler.cleanup called')
        try:
            self.root.unbind("<MouseWheel>")
            self.root.unbind("<Button-4>")
            self.root.unbind("<Button-5>")
        except Exception as e:
            print(f'[ERROR] ScrollHandler: Failed to cleanup bindings: {e}')
