#!/usr/bin/env python3
"""
Test script for the smart file browser enhancement.
Tests that the browse button opens to the correct target location.
"""

import os
import sys
import tempfile
import tkinter as tk
from unittest.mock import patch, MagicMock
from datetime import datetime

# Add parent directories to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backup.backup_dialog import BackupDialog
from utils.directory_manager import <PERSON><PERSON><PERSON><PERSON>


def test_smart_file_browser_logic():
    """Test the smart file browser directory detection logic."""
    print('[TEST] Starting smart file browser logic test')
    
    try:
        # Create root window and dialog
        root = tk.Tk()
        root.withdraw()
        
        dialog = BackupDialog(root)
        
        # Test 1: No existing path - should use Documents
        print('\n[TEST 1] Testing with no existing backup path')
        dialog.backup_path.set("")
        
        # Mock the file dialog to capture the parameters
        with patch('tkinter.filedialog.asksaveasfilename') as mock_dialog:
            mock_dialog.return_value = ""  # User cancels
            
            # Call the browse method
            dialog._browse_backup_path()
            
            # Check that the dialog was called with correct parameters
            call_args = mock_dialog.call_args
            if call_args:
                kwargs = call_args[1]
                initial_dir = kwargs.get('initialdir')
                initial_file = kwargs.get('initialfile')
                
                print(f'[TEST 1] Initial directory: {initial_dir}')
                print(f'[TEST 1] Initial filename: {initial_file}')
                
                # Should have an initial directory
                if not initial_dir:
                    print('[ERROR] No initial directory set')
                    return False
                
                # Should have a default filename with timestamp
                if not initial_file or 'clipsmore_backup_' not in initial_file:
                    print('[ERROR] Invalid default filename')
                    return False
                
                print('[TEST 1] ✅ Default directory and filename logic works')
            else:
                print('[ERROR] File dialog not called')
                return False
        
        # Test 2: Existing path - should use existing directory
        print('\n[TEST 2] Testing with existing backup path')
        test_path = os.path.join(tempfile.gettempdir(), 'existing_backup.db')
        dialog.backup_path.set(test_path)
        
        with patch('tkinter.filedialog.asksaveasfilename') as mock_dialog:
            mock_dialog.return_value = ""  # User cancels
            
            dialog._browse_backup_path()
            
            call_args = mock_dialog.call_args
            if call_args:
                kwargs = call_args[1]
                initial_dir = kwargs.get('initialdir')
                initial_file = kwargs.get('initialfile')
                
                print(f'[TEST 2] Initial directory: {initial_dir}')
                print(f'[TEST 2] Initial filename: {initial_file}')
                
                # Should use the directory from existing path
                expected_dir = os.path.dirname(test_path)
                if initial_dir != expected_dir:
                    print(f'[ERROR] Expected {expected_dir}, got {initial_dir}')
                    return False
                
                # Should use the filename from existing path
                expected_file = os.path.basename(test_path)
                if initial_file != expected_file:
                    print(f'[ERROR] Expected {expected_file}, got {initial_file}')
                    return False
                
                print('[TEST 2] ✅ Existing path directory and filename logic works')
            else:
                print('[ERROR] File dialog not called')
                return False
        
        # Test 3: User selects a file - path should be updated
        print('\n[TEST 3] Testing file selection')
        new_backup_path = os.path.join(tempfile.gettempdir(), 'new_backup.db')
        
        with patch('tkinter.filedialog.asksaveasfilename') as mock_dialog:
            mock_dialog.return_value = new_backup_path
            
            dialog._browse_backup_path()
            
            # Check that the backup path was updated
            if dialog.backup_path.get() != new_backup_path:
                print(f'[ERROR] Path not updated. Expected {new_backup_path}, got {dialog.backup_path.get()}')
                return False
            
            print(f'[TEST 3] ✅ Backup path updated to: {dialog.backup_path.get()}')
        
        # Clean up
        dialog.destroy()
        root.destroy()
        
        print('\n[TEST] ✅ ALL SMART FILE BROWSER TESTS PASSED!')
        return True
        
    except Exception as e:
        print(f'[ERROR] Test failed with exception: {e}')
        import traceback
        traceback.print_exc()
        return False


def test_directory_manager_integration():
    """Test integration with DirectoryManager for path detection."""
    print('\n[INTEGRATION TEST] Testing DirectoryManager integration')
    
    try:
        directory_manager = DirectoryManager()
        
        # Test Documents path detection
        documents_path = directory_manager.get_documents_path()
        print(f'[INTEGRATION] Documents path: {documents_path}')
        
        if not documents_path:
            print('[ERROR] Documents path not detected')
            return False
        
        # Test Desktop path detection
        desktop_path = directory_manager.get_desktop_path()
        print(f'[INTEGRATION] Desktop path: {desktop_path}')
        
        if not desktop_path:
            print('[ERROR] Desktop path not detected')
            return False
        
        # Test directory validation
        documents_valid = directory_manager.validate_directory(str(documents_path))
        desktop_valid = directory_manager.validate_directory(str(desktop_path))
        
        print(f'[INTEGRATION] Documents valid: {documents_valid}')
        print(f'[INTEGRATION] Desktop valid: {desktop_valid}')
        
        if not (documents_valid or desktop_valid):
            print('[ERROR] Neither Documents nor Desktop directories are valid')
            return False
        
        print('[INTEGRATION] ✅ DirectoryManager integration working')
        return True
        
    except Exception as e:
        print(f'[ERROR] Integration test failed: {e}')
        return False


def test_filename_generation():
    """Test default filename generation logic."""
    print('\n[FILENAME TEST] Testing default filename generation')
    
    try:
        # Test timestamp format
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        expected_pattern = f"clipsmore_backup_{timestamp}.db"
        
        print(f'[FILENAME] Generated pattern: {expected_pattern}')
        
        # Verify format
        if not expected_pattern.startswith('clipsmore_backup_'):
            print('[ERROR] Filename doesn\'t start with correct prefix')
            return False
        
        if not expected_pattern.endswith('.db'):
            print('[ERROR] Filename doesn\'t end with .db extension')
            return False
        
        # Check timestamp format (YYYYMMDD_HHMMSS)
        timestamp_part = expected_pattern.replace('clipsmore_backup_', '').replace('.db', '')
        if len(timestamp_part) != 15 or timestamp_part[8] != '_':
            print(f'[ERROR] Invalid timestamp format: {timestamp_part}')
            return False
        
        print('[FILENAME] ✅ Filename generation logic correct')
        return True
        
    except Exception as e:
        print(f'[ERROR] Filename test failed: {e}')
        return False


if __name__ == '__main__':
    print('=' * 70)
    print('SMART FILE BROWSER ENHANCEMENT TEST')
    print('Testing intelligent directory detection for backup browse')
    print('=' * 70)
    
    # Run tests
    test1_passed = test_smart_file_browser_logic()
    test2_passed = test_directory_manager_integration()
    test3_passed = test_filename_generation()
    
    print('=' * 70)
    print('TEST RESULTS:')
    print(f'  Smart File Browser Logic: {"PASSED" if test1_passed else "FAILED"}')
    print(f'  DirectoryManager Integration: {"PASSED" if test2_passed else "FAILED"}')
    print(f'  Filename Generation: {"PASSED" if test3_passed else "FAILED"}')
    
    if test1_passed and test2_passed and test3_passed:
        print('  OVERALL: ALL TESTS PASSED ✅')
        print('\n🎯 FEATURE SUMMARY:')
        print('  • Browse button opens to intelligent target location')
        print('  • Uses existing backup path directory when available')
        print('  • Falls back to Documents → Desktop → Current directory')
        print('  • Generates smart default filename with timestamp')
        print('  • Integrates with enhanced DirectoryManager')
        print('\n✨ READY FOR PRODUCTION USE ✨')
        sys.exit(0)
    else:
        print('  OVERALL: SOME TESTS FAILED ❌')
        sys.exit(1)
