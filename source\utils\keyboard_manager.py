"""
KeyboardManager - Handles keyboard bindings and shortcuts

This manager centralizes all keyboard binding logic, including
shortcuts, hotkeys, and keyboard event handling.
"""

import tkinter as tk
from typing import Dict, Callable, Optional, Any


class KeyboardManager:
    """
    Manages keyboard bindings and shortcuts for the application.
    
    This class handles all keyboard-related functionality including
    global shortcuts, context-specific bindings, and keyboard events.
    """
    
    def __init__(self, root: tk.Tk):
        """
        Initialize KeyboardManager.
        
        Args:
            root: Main tkinter root window
        """
        print('[DEBUG] KeyboardManager.__init__ called')
        self.root = root
        self.bindings = {}
        self.callbacks = {}
        
    def setup_global_bindings(self, undo_callback: Callable, tab_manager=None, theme_manager=None, ui_manager=None):
        """
        Setup global keyboard bindings for the application.

        Args:
            undo_callback: Callback function for undo operations
            tab_manager: TabManager instance for tab navigation shortcuts
            theme_manager: ThemeManager instance for theme toggle shortcut
            ui_manager: UIManager instance for search and other functionality
        """
        print('[DEBUG] KeyboardManager.setup_global_bindings called')

        try:
            # Bind Ctrl+Z for undo (both lowercase and uppercase for Shift+Ctrl+Z)
            self.bind_key('<Control-z>', undo_callback, 'undo_lowercase')
            self.bind_key('<Control-Z>', undo_callback, 'undo_uppercase')

            # Tab navigation shortcuts
            if tab_manager:
                self.bind_key('<Control-Key-1>', lambda: tab_manager.switch_to_tab(0), 'tab_clips')
                self.bind_key('<Control-Key-2>', lambda: tab_manager.switch_to_tab(1), 'tab_more')
                self.bind_key('<Control-Key-3>', lambda: tab_manager.switch_to_tab(2), 'tab_about')
                print('[DEBUG] Tab navigation shortcuts bound')

            # Theme toggle shortcut
            if theme_manager and ui_manager:
                self.bind_key('<Control-t>', lambda: ui_manager.toggle_theme(), 'theme_toggle')
                self.bind_key('<Control-T>', lambda: ui_manager.toggle_theme(), 'theme_toggle_upper')
                print('[DEBUG] Theme toggle shortcuts bound')

            # Search focus shortcut
            if ui_manager:
                self.bind_key('<Control-f>', lambda: self._focus_search_field(ui_manager), 'search_focus')
                self.bind_key('<Control-F>', lambda: self._focus_search_field(ui_manager), 'search_focus_upper')
                print('[DEBUG] Search focus shortcuts bound')

            print('[DEBUG] Global keyboard bindings setup successfully')

        except Exception as e:
            print(f'[ERROR] Failed to setup global keyboard bindings: {e}')
            raise
            
    def bind_key(self, key_sequence: str, callback: Callable, binding_name: str = None):
        """
        Bind a key sequence to a callback function.
        
        Args:
            key_sequence: Tkinter key sequence (e.g., '<Control-z>')
            callback: Callback function to execute
            binding_name: Optional name for the binding (for tracking)
        """
        print(f'[DEBUG] KeyboardManager.bind_key called: {key_sequence}')
        
        try:
            # Create wrapper to handle event parameter
            def key_handler(event):
                try:
                    callback()
                    return 'break'  # Prevent further event propagation
                except Exception as e:
                    print(f'[ERROR] Error in key handler for {key_sequence}: {e}')
                    
            # Bind to root window
            self.root.bind(key_sequence, key_handler)
            
            # Store binding information
            if binding_name:
                self.bindings[binding_name] = {
                    'key_sequence': key_sequence,
                    'callback': callback,
                    'handler': key_handler
                }
                
            print(f'[DEBUG] Key binding created: {key_sequence}')
            
        except Exception as e:
            print(f'[ERROR] Failed to bind key {key_sequence}: {e}')
            raise
            
    def unbind_key(self, key_sequence: str, binding_name: str = None):
        """
        Unbind a key sequence.
        
        Args:
            key_sequence: Tkinter key sequence to unbind
            binding_name: Optional binding name to remove from tracking
        """
        print(f'[DEBUG] KeyboardManager.unbind_key called: {key_sequence}')
        
        try:
            self.root.unbind(key_sequence)
            
            # Remove from tracking if binding name provided
            if binding_name and binding_name in self.bindings:
                del self.bindings[binding_name]
                
            print(f'[DEBUG] Key binding removed: {key_sequence}')
            
        except Exception as e:
            print(f'[ERROR] Failed to unbind key {key_sequence}: {e}')
            
    def setup_entry_bindings(self, entry_widget: tk.Entry, callbacks: Dict[str, Callable]):
        """
        Setup keyboard bindings for entry widgets.
        
        Args:
            entry_widget: Entry widget to bind keys to
            callbacks: Dictionary of key sequences to callbacks
        """
        print('[DEBUG] KeyboardManager.setup_entry_bindings called')
        
        try:
            for key_sequence, callback in callbacks.items():
                def entry_handler(event, cb=callback):
                    try:
                        cb(event)
                    except Exception as e:
                        print(f'[ERROR] Error in entry handler for {key_sequence}: {e}')
                        
                entry_widget.bind(key_sequence, entry_handler)
                
            print(f'[DEBUG] Entry bindings setup for {len(callbacks)} keys')
            
        except Exception as e:
            print(f'[ERROR] Failed to setup entry bindings: {e}')
            
    def setup_tree_bindings(self, tree_widget: tk.Widget, callbacks: Dict[str, Callable]):
        """
        Setup keyboard bindings for tree widgets.
        
        Args:
            tree_widget: Tree widget to bind keys to
            callbacks: Dictionary of key sequences to callbacks
        """
        print('[DEBUG] KeyboardManager.setup_tree_bindings called')
        
        try:
            for key_sequence, callback in callbacks.items():
                def tree_handler(event, cb=callback):
                    try:
                        cb(event)
                    except Exception as e:
                        print(f'[ERROR] Error in tree handler for {key_sequence}: {e}')
                        
                tree_widget.bind(key_sequence, tree_handler)
                
            print(f'[DEBUG] Tree bindings setup for {len(callbacks)} keys')
            
        except Exception as e:
            print(f'[ERROR] Failed to setup tree bindings: {e}')
            
    def setup_text_widget_bindings(self, text_widget: tk.Text, callbacks: Dict[str, Callable]):
        """
        Setup keyboard bindings for text widgets.
        
        Args:
            text_widget: Text widget to bind keys to
            callbacks: Dictionary of key sequences to callbacks
        """
        print('[DEBUG] KeyboardManager.setup_text_widget_bindings called')
        
        try:
            for key_sequence, callback in callbacks.items():
                def text_handler(event, cb=callback):
                    try:
                        cb(event)
                    except Exception as e:
                        print(f'[ERROR] Error in text handler for {key_sequence}: {e}')
                        
                text_widget.bind(key_sequence, text_handler)
                
            print(f'[DEBUG] Text widget bindings setup for {len(callbacks)} keys')
            
        except Exception as e:
            print(f'[ERROR] Failed to setup text widget bindings: {e}')
            
    def add_accelerator_keys(self, menu_item, key_sequence: str):
        """
        Add accelerator key display to menu items.
        
        Args:
            menu_item: Menu item to add accelerator to
            key_sequence: Key sequence string for display
        """
        print(f'[DEBUG] KeyboardManager.add_accelerator_keys called: {key_sequence}')
        
        try:
            # Convert tkinter key sequence to display format
            display_key = self._format_key_for_display(key_sequence)
            menu_item.config(accelerator=display_key)
            
            print(f'[DEBUG] Accelerator added: {display_key}')
            
        except Exception as e:
            print(f'[ERROR] Failed to add accelerator: {e}')
            
    def _format_key_for_display(self, key_sequence: str) -> str:
        """
        Format key sequence for display in menus.
        
        Args:
            key_sequence: Tkinter key sequence
            
        Returns:
            Formatted key sequence for display
        """
        # Convert common key sequences to display format
        replacements = {
            '<Control-': 'Ctrl+',
            '<Alt-': 'Alt+',
            '<Shift-': 'Shift+',
            '>': '',
            '-z': 'Z',
            '-c': 'C',
            '-v': 'V',
            '-x': 'X',
            '-a': 'A',
            '-s': 'S',
            '-o': 'O',
            '-n': 'N'
        }
        
        display_key = key_sequence
        for old, new in replacements.items():
            display_key = display_key.replace(old, new)
            
        return display_key
        
    def get_binding_info(self, binding_name: str) -> Optional[Dict[str, Any]]:
        """
        Get information about a specific binding.
        
        Args:
            binding_name: Name of the binding
            
        Returns:
            Dictionary with binding information or None
        """
        return self.bindings.get(binding_name)
        
    def get_all_bindings(self) -> Dict[str, Dict[str, Any]]:
        """
        Get information about all bindings.
        
        Returns:
            Dictionary of all binding information
        """
        return self.bindings.copy()
        
    def clear_all_bindings(self):
        """
        Clear all tracked bindings.
        """
        print('[DEBUG] KeyboardManager.clear_all_bindings called')
        
        try:
            for binding_name, binding_info in self.bindings.items():
                key_sequence = binding_info['key_sequence']
                self.root.unbind(key_sequence)
                
            self.bindings.clear()
            print('[DEBUG] All bindings cleared')
            
        except Exception as e:
            print(f'[ERROR] Failed to clear all bindings: {e}')
            
    def is_key_bound(self, key_sequence: str) -> bool:
        """
        Check if a key sequence is currently bound.
        
        Args:
            key_sequence: Key sequence to check
            
        Returns:
            True if key is bound, False otherwise
        """
        for binding_info in self.bindings.values():
            if binding_info['key_sequence'] == key_sequence:
                return True
        return False

    def _focus_search_field(self, ui_manager):
        """
        Focus the search field in the current tab.

        Args:
            ui_manager: UIManager instance to access current tab and search fields
        """
        print('[DEBUG] KeyboardManager._focus_search_field called')

        try:
            # Get current tab from ui_manager
            current_tab_name = ui_manager.get_current_tab_name()
            print(f'[DEBUG] Current tab: {current_tab_name}')

            # Focus search field based on current tab
            if current_tab_name == 'Clips':
                # Focus search in Clips tab if available
                if hasattr(ui_manager, 'clip_manager') and ui_manager.clip_manager:
                    # Check if clip manager has a search field
                    if hasattr(ui_manager.clip_manager, 'search_entry'):
                        ui_manager.clip_manager.search_entry.focus_set()
                        print('[DEBUG] Focused Clips tab search field')
                    else:
                        print('[DEBUG] No search field found in Clips tab')

            elif current_tab_name == 'More':
                # Focus search in More tab if available
                if hasattr(ui_manager, 'more_tab_manager') and ui_manager.more_tab_manager:
                    # Check if more tab manager has search elements
                    if hasattr(ui_manager.more_tab_manager, 'ui_elements'):
                        search_entry = ui_manager.more_tab_manager.ui_elements.get('search_entry')
                        if search_entry:
                            search_entry.focus_set()
                            print('[DEBUG] Focused More tab search field')
                        else:
                            print('[DEBUG] No search field found in More tab')

            elif current_tab_name == 'About':
                # About tab doesn't typically have search
                print('[DEBUG] About tab does not have search functionality')

        except Exception as e:
            print(f'[ERROR] Failed to focus search field: {e}')
