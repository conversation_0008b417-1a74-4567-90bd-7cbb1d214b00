#!/usr/bin/env python3
"""
Advanced Alias Generation Utility for ClipsMore
Provides intelligent alias generation from clip content with uniqueness validation.
"""

import re
import string
from typing import List, Set, Optional, Dict, Any
from collections import Counter

# NOTE: All new code should include debug print statements at the start of every function/method.

class AliasGenerator:
    """Advanced alias generation with intelligent text analysis."""
    
    # Common words to filter out
    STOP_WORDS = {
        'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with',
        'by', 'from', 'up', 'about', 'into', 'through', 'during', 'before', 'after',
        'above', 'below', 'between', 'among', 'this', 'that', 'these', 'those', 'i',
        'me', 'my', 'myself', 'we', 'our', 'ours', 'ourselves', 'you', 'your', 'yours',
        'yourself', 'yourselves', 'he', 'him', 'his', 'himself', 'she', 'her', 'hers',
        'herself', 'it', 'its', 'itself', 'they', 'them', 'their', 'theirs', 'themselves',
        'what', 'which', 'who', 'whom', 'whose', 'where', 'when', 'why', 'how', 'all',
        'any', 'both', 'each', 'few', 'more', 'most', 'other', 'some', 'such', 'no',
        'nor', 'not', 'only', 'own', 'same', 'so', 'than', 'too', 'very', 'can', 'will',
        'just', 'should', 'now', 'get', 'got', 'has', 'have', 'had', 'is', 'are', 'was',
        'were', 'be', 'been', 'being', 'do', 'does', 'did', 'done', 'doing', 'would',
        'could', 'should', 'may', 'might', 'must', 'shall', 'will', 'can'
    }
    
    # Technical terms that are often meaningful
    TECHNICAL_TERMS = {
        'api', 'url', 'http', 'https', 'sql', 'json', 'xml', 'csv', 'pdf', 'doc', 'docx',
        'xls', 'xlsx', 'ppt', 'pptx', 'zip', 'tar', 'gz', 'exe', 'dll', 'jar', 'war',
        'class', 'function', 'method', 'variable', 'constant', 'array', 'list', 'dict',
        'object', 'string', 'integer', 'float', 'boolean', 'null', 'undefined', 'true',
        'false', 'import', 'export', 'module', 'package', 'library', 'framework',
        'database', 'table', 'column', 'row', 'index', 'key', 'value', 'query', 'select',
        'insert', 'update', 'delete', 'create', 'drop', 'alter', 'join', 'where', 'order',
        'group', 'having', 'limit', 'offset', 'count', 'sum', 'avg', 'min', 'max'
    }
    
    def __init__(self):
        """Initialize the alias generator."""
        print('[DEBUG] AliasGenerator.__init__ called')
        self.existing_aliases: Set[str] = set()
    
    def set_existing_aliases(self, aliases: List[str]) -> None:
        """Set the list of existing aliases for uniqueness checking.
        
        Args:
            aliases: List of existing aliases to avoid conflicts
        """
        print(f'[DEBUG] AliasGenerator.set_existing_aliases called with {len(aliases)} aliases')
        self.existing_aliases = set(alias.lower() for alias in aliases if alias)
    
    def generate_from_content(self, content: str, max_length: int = 20) -> str:
        """Generate alias from clip content using intelligent text analysis.
        
        Args:
            content: The clip content to analyze
            max_length: Maximum length of generated alias
            
        Returns:
            Generated unique alias
        """
        print(f'[DEBUG] AliasGenerator.generate_from_content called with content length: {len(content)}')
        
        if not content or not content.strip():
            return self._generate_fallback_alias()
        
        # Try different strategies in order of preference
        strategies = [
            self._extract_from_url,
            self._extract_from_filename,
            self._extract_from_code,
            self._extract_meaningful_words,
            self._extract_first_words,
            self._extract_from_numbers_and_dates
        ]
        
        for strategy in strategies:
            alias = strategy(content, max_length)
            if alias and len(alias) >= 3:  # Minimum meaningful length
                unique_alias = self._ensure_uniqueness(alias)
                if unique_alias:
                    print(f'[DEBUG] Generated alias using {strategy.__name__}: {unique_alias}')
                    return unique_alias
        
        # Final fallback
        fallback = self._generate_fallback_alias()
        print(f'[DEBUG] Using fallback alias: {fallback}')
        return fallback
    
    def _extract_from_url(self, content: str, max_length: int) -> Optional[str]:
        """Extract alias from URL patterns."""
        print('[DEBUG] AliasGenerator._extract_from_url called')
        # Look for URLs
        url_pattern = r'https?://(?:www\.)?([a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*)'
        urls = re.findall(url_pattern, content.lower())
        
        if urls:
            domain = urls[0]
            # Extract main domain name
            domain_parts = domain.split('.')
            if len(domain_parts) >= 2:
                main_domain = domain_parts[-2]  # Get the main part before TLD
                if len(main_domain) >= 3 and main_domain not in self.STOP_WORDS:
                    return main_domain[:max_length]
        
        # Look for file paths or API endpoints
        path_pattern = r'/([a-zA-Z0-9_-]+)(?:\.[a-zA-Z0-9]+)?'
        paths = re.findall(path_pattern, content)
        
        if paths:
            # Get the most meaningful path component
            meaningful_paths = [p for p in paths if len(p) >= 3 and p.lower() not in self.STOP_WORDS]
            if meaningful_paths:
                return meaningful_paths[0][:max_length]
        
        return None
    
    def _extract_from_filename(self, content: str, max_length: int) -> Optional[str]:
        """Extract alias from filename patterns."""
        print('[DEBUG] AliasGenerator._extract_from_filename called')
        # Look for file extensions and names
        filename_pattern = r'([a-zA-Z0-9_-]+)\.[a-zA-Z0-9]{2,4}'
        filenames = re.findall(filename_pattern, content)
        
        if filenames:
            # Prefer longer, more descriptive filenames
            meaningful_files = [f for f in filenames if len(f) >= 3 and f.lower() not in self.STOP_WORDS]
            if meaningful_files:
                # Sort by length and take the longest meaningful one
                best_filename = max(meaningful_files, key=len)
                return best_filename[:max_length]
        
        return None
    
    def _extract_from_code(self, content: str, max_length: int) -> Optional[str]:
        """Extract alias from code patterns."""
        print('[DEBUG] AliasGenerator._extract_from_code called')
        # Look for function/method names
        function_pattern = r'(?:def|function|class|interface)\s+([a-zA-Z_][a-zA-Z0-9_]*)'
        functions = re.findall(function_pattern, content, re.IGNORECASE)
        
        if functions:
            # Prefer camelCase or snake_case identifiers
            for func in functions:
                if len(func) >= 3 and func.lower() not in self.STOP_WORDS:
                    return func[:max_length]
        
        # Look for variable assignments
        var_pattern = r'([a-zA-Z_][a-zA-Z0-9_]*)\s*[=:]'
        variables = re.findall(var_pattern, content)
        
        if variables:
            meaningful_vars = [v for v in variables if len(v) >= 3 and v.lower() not in self.STOP_WORDS]
            if meaningful_vars:
                return meaningful_vars[0][:max_length]
        
        # Look for technical terms
        words = re.findall(r'\b[a-zA-Z]+\b', content.lower())
        tech_words = [w for w in words if w in self.TECHNICAL_TERMS and len(w) >= 3]
        if tech_words:
            return tech_words[0][:max_length]
        
        return None
    
    def _extract_meaningful_words(self, content: str, max_length: int) -> Optional[str]:
        """Extract meaningful words using frequency and length analysis."""
        print('[DEBUG] AliasGenerator._extract_meaningful_words called')
        # Clean and tokenize
        words = re.findall(r'\b[a-zA-Z]{3,}\b', content.lower())
        
        # Filter out stop words and very common words
        meaningful_words = [
            word for word in words 
            if word not in self.STOP_WORDS and len(word) >= 3
        ]
        
        if not meaningful_words:
            return None
        
        # Count word frequency
        word_counts = Counter(meaningful_words)
        
        # Prefer words that appear multiple times (likely important)
        # but also consider unique longer words
        scored_words = []
        for word, count in word_counts.items():
            # Score based on length and frequency
            score = len(word) * 0.5 + count * 2
            # Bonus for technical terms
            if word in self.TECHNICAL_TERMS:
                score += 5
            scored_words.append((word, score))
        
        # Sort by score and take the best
        scored_words.sort(key=lambda x: x[1], reverse=True)
        
        if scored_words:
            best_word = scored_words[0][0]
            
            # Try to combine with second word if space allows
            if len(best_word) < max_length - 3 and len(scored_words) > 1:
                second_word = scored_words[1][0]
                combined = f"{best_word}_{second_word}"
                if len(combined) <= max_length:
                    return combined
            
            return best_word[:max_length]
        
        return None
    
    def _extract_first_words(self, content: str, max_length: int) -> Optional[str]:
        """Extract first few meaningful words as fallback."""
        print('[DEBUG] AliasGenerator._extract_first_words called')
        # Get first few words, clean them
        words = re.findall(r'\b[a-zA-Z]{2,}\b', content)[:5]
        
        meaningful_words = [
            word.lower() for word in words 
            if word.lower() not in self.STOP_WORDS and len(word) >= 2
        ]
        
        if meaningful_words:
            if len(meaningful_words) == 1:
                return meaningful_words[0][:max_length]
            else:
                # Combine first two words
                combined = "_".join(meaningful_words[:2])
                return combined[:max_length]
        
        return None
    
    def _extract_from_numbers_and_dates(self, content: str, max_length: int) -> Optional[str]:
        """Extract alias from numbers, dates, or identifiers."""
        print('[DEBUG] AliasGenerator._extract_from_numbers_and_dates called')
        # Look for dates
        date_pattern = r'\b(\d{4}[-/]\d{1,2}[-/]\d{1,2}|\d{1,2}[-/]\d{1,2}[-/]\d{4})\b'
        dates = re.findall(date_pattern, content)
        
        if dates:
            # Convert to simple format
            date_str = dates[0].replace('/', '_').replace('-', '_')
            return f"date_{date_str}"[:max_length]
        
        # Look for version numbers
        version_pattern = r'\bv?(\d+\.\d+(?:\.\d+)?)\b'
        versions = re.findall(version_pattern, content, re.IGNORECASE)
        
        if versions:
            version_str = versions[0].replace('.', '_')
            return f"v_{version_str}"[:max_length]
        
        # Look for IDs or numbers
        id_pattern = r'\b(?:id|ref|num|no)[\s:]*(\d+)\b'
        ids = re.findall(id_pattern, content, re.IGNORECASE)
        
        if ids:
            return f"id_{ids[0]}"[:max_length]
        
        return None
    
    def _ensure_uniqueness(self, base_alias: str) -> str:
        """Ensure alias is unique by adding suffix if needed.
        
        Args:
            base_alias: Base alias to make unique
            
        Returns:
            Unique alias
        """
        if not base_alias:
            return self._generate_fallback_alias()
        
        # Clean the alias
        clean_alias = self._clean_alias(base_alias)
        
        if clean_alias.lower() not in self.existing_aliases:
            self.existing_aliases.add(clean_alias.lower())
            return clean_alias
        
        # Try variations
        counter = 1
        while counter <= 999:  # Reasonable limit
            variant = f"{clean_alias}_{counter}"
            if variant.lower() not in self.existing_aliases:
                self.existing_aliases.add(variant.lower())
                return variant
            counter += 1
        
        # Ultimate fallback
        return self._generate_fallback_alias()
    
    def _clean_alias(self, alias: str) -> str:
        """Clean alias to ensure it's valid.

        Args:
            alias: Raw alias to clean

        Returns:
            Cleaned alias
        """
        print('[DEBUG] AliasGenerator._clean_alias called')
        # Remove invalid characters
        cleaned = re.sub(r'[^\w\-_]', '_', alias)
        
        # Remove multiple underscores
        cleaned = re.sub(r'_+', '_', cleaned)
        
        # Remove leading/trailing underscores
        cleaned = cleaned.strip('_')
        
        # Ensure it starts with a letter
        if cleaned and not cleaned[0].isalpha():
            cleaned = f"clip_{cleaned}"
        
        # Ensure minimum length
        if len(cleaned) < 3:
            cleaned = f"clip_{cleaned}"
        
        return cleaned[:20]  # Limit length
    
    def _generate_fallback_alias(self) -> str:
        """Generate fallback alias when content analysis fails.
        
        Returns:
            Fallback alias
        """
        counter = 1
        while counter <= 9999:  # Very high limit for fallback
            alias = f"clip_{counter}"
            if alias.lower() not in self.existing_aliases:
                self.existing_aliases.add(alias.lower())
                return alias
            counter += 1
        
        # Ultimate fallback with timestamp
        import time
        timestamp = str(int(time.time()))[-6:]  # Last 6 digits
        alias = f"clip_{timestamp}"
        self.existing_aliases.add(alias.lower())
        return alias
    
    def validate_alias(self, alias: str) -> bool:
        """Validate if an alias is acceptable.
        
        Args:
            alias: Alias to validate
            
        Returns:
            True if alias is valid
        """
        if not alias or len(alias) < 1:
            return False
        
        # Check for valid characters
        if not re.match(r'^[a-zA-Z][a-zA-Z0-9_-]*$', alias):
            return False
        
        # Check length
        if len(alias) > 50:
            return False
        
        return True
    
    def suggest_alternatives(self, base_alias: str, count: int = 5) -> List[str]:
        """Suggest alternative aliases based on a base alias.
        
        Args:
            base_alias: Base alias to generate alternatives for
            count: Number of alternatives to generate
            
        Returns:
            List of alternative aliases
        """
        alternatives = []
        clean_base = self._clean_alias(base_alias)
        
        # Add numbered variations
        for i in range(1, count + 1):
            alt = f"{clean_base}_{i}"
            if alt.lower() not in self.existing_aliases:
                alternatives.append(alt)
        
        # Add descriptive suffixes
        suffixes = ['new', 'copy', 'alt', 'mod', 'v2']
        for suffix in suffixes:
            if len(alternatives) >= count:
                break
            alt = f"{clean_base}_{suffix}"
            if alt.lower() not in self.existing_aliases:
                alternatives.append(alt)
        
        return alternatives[:count]
