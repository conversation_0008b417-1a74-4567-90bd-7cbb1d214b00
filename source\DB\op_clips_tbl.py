import os, sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


"""Clip table operations module

This module handles all database operations for the clips_tbl including:
- Creating, reading, updating and deleting clips
- Validation and error handling
- Duplicate detection
"""
import sqlite3
from typing import Optional, Dict, Any, List, Tuple
from .db_connection import ConnectionPoolManager
from pathlib import Path

class ClipError(Exception):
    """Base exception for clip operations"""
    pass

class ClipNotFoundError(ClipError):
    """Raised when a clip is not found"""
    pass

class ClipValidationError(ClipError):
    """Raised when clip data is invalid"""
    pass

from datetime import datetime

class ClipsTableOperations:
    """Operations for managing clips in the clips_tbl.

    Attributes:
        pool (ConnectionPoolManager): Database connection pool instance
    """
    
    def __init__(self, db_path: Optional[str] = None) -> None:
        """Initialize with connection pool.

        Args:
            db_path: Optional database path override. Defaults to None.
        """
        print('[DEBUG] ClipsTableOperations.__init__ called')
        if db_path is None:
            db_path = str(Path(__file__).parent / "clipsmore_db.db")
        print(f'[DEBUG] [ClipsTableOperations.__init__] Using database file path: {db_path}')
        self.pool = ConnectionPoolManager(db_path)

    def _validate_clip_text(self, clip_text: str) -> None:
        """Validate clip text content.

        Args:
            clip_text: Content to validate

        Raises:
            ClipValidationError: For invalid content
        """
        print(f'[DEBUG] Validating clip text: {clip_text[:50]}...')  # Show first 50 chars
        if not isinstance(clip_text, str):
            raise ClipValidationError(f"Clip text must be string, got {type(clip_text)}")
        if not clip_text.strip():
            raise ClipValidationError("Clip text cannot be empty")
        if (len(clip_text.encode('utf-8')) > 500 * 1024):
            raise ClipValidationError("Clip exceeds 500KB size limit")
        print('[DEBUG] Clip text validation passed')

    def _validate_clip_alias(self, alias: Optional[str]) -> None:
        """Validate clip alias.

        Args:
            alias: Alias to validate

        Raises:
            ClipValidationError: For invalid alias format
        """
        print(f'[DEBUG] Validating clip alias: {alias}')
        if alias is None:
            return
        if not isinstance(alias, str):
            raise ClipValidationError(f"Alias must be string, got {type(alias)}")
        if not alias.strip():
            raise ClipValidationError("Alias cannot be empty when provided")
        print('[DEBUG] Clip alias validation passed')

    def create_clip(self, data: Dict[str, Any]) -> int:
        """Create a new clip entry with content verification.

        Args:
            data: Dictionary containing:
                - clip (str): Required clip content
                - alias (str): Optional alias

        Returns:
            int: ID of the created clip

        Raises:
            ClipValidationError: Invalid input data
            ClipError: Database operation or verification failed

        Example:
            >>> ops = ClipsTableOperations()
            >>> ops.create_clip({'clip': 'Important note', 'alias': 'reminder'})
            42
        """
        print(f'[DEBUG] Creating clip with data: {data}')
        print(f'[DEBUG] [create_clip] Using database file path: {self.pool.db_path}')
        clip_text = data.get('clip')
        alias = data.get('alias')

        # Convert bytes to string if needed
        if isinstance(clip_text, bytes):
            clip_text = clip_text.decode('utf-8')

        self._validate_clip_text(clip_text)
        self._validate_clip_alias(alias)

        try:
            with self.pool.get_connection() as conn:
                cursor = conn.cursor()
                query = "INSERT INTO clips_tbl (clip, alias) VALUES (?, ?)"
                cursor.execute(query, (clip_text, alias))
                clip_id = cursor.lastrowid
                print(f'[DEBUG] Clip inserted with ID: {clip_id}')

                # Verify the clip was created
                cursor.execute("SELECT clip_id, clip, alias FROM clips_tbl WHERE clip_id = ?", (clip_id,))
                result = cursor.fetchone()

                if not result:
                    raise ClipError("Failed to create clip - verification failed")

                # Double check the content matches
                stored_clip = result[1]
                if stored_clip != clip_text:
                    raise ClipError("Content verification failed")

                conn.commit()
                print('[DEBUG] Clip creation committed')
                return clip_id
                
        except sqlite3.Error as e:
            raise ClipError(f"Database error: {str(e)}") from e

    def read_clip(self, clip_id: int) -> Dict[str, Any]:
        """Retrieve full clip data by ID with validation.

        Args:
            clip_id: Positive integer > 0 identifying the clip

        Returns:
            dict: Contains:
                - clip_id (int)
                - clip (str)
                - alias (str | None)
                - timestamp (str) ISO 8601 format

        Raises:
            ClipValidationError: Invalid ID format
            ClipNotFoundError: No matching clip found
            ClipError: Database operation failure

        Example:
            >>> ops.read_clip(123)
            {'clip_id': 123, 'clip': 'Sample text', 'alias': 'demo', 'timestamp': '2025-05-26T00:42:00Z'}
        """
        print(f'[DEBUG] Reading clip with ID: {clip_id}')
        print(f'[DEBUG] [read_clip] Using database file path: {self.pool.db_path}')
        if not isinstance(clip_id, int) or clip_id <= 0:
            raise ClipValidationError("clip_id must be a positive integer")
    
        try:
            with self.pool.get_connection() as conn:
                cursor = conn.cursor()
                query = "SELECT * FROM clips_tbl WHERE clip_id = ?"
                cursor.execute(query, (clip_id,))
                result = cursor.fetchone()
                
                if not result:
                    raise ClipNotFoundError(f"Clip with ID {clip_id} not found")
                    
                columns = [description[0] for description in cursor.description]
                clip_data = dict(zip(columns, result))
                print(f'[DEBUG] Clip data retrieved: {clip_data}')
                return clip_data
                
        except sqlite3.Error as e:
            raise ClipError(f"Database error: {str(e)}") from e

    def find_duplicate_clip(self, clip_text: str) -> Optional[int]:
        """Check for existing clips with identical content.

        Args:
            clip_text: Text content to search for

        Returns:
            int | None: Existing clip ID if duplicate found

        Raises:
            ClipValidationError: Invalid clip_text format

        Example:
            >>> ops.find_duplicate_clip("Meeting notes")
            42
            >>> ops.find_duplicate_clip("New idea")
            None
        """
        print(f'[DEBUG] Finding duplicate clip for text: {clip_text[:50]}...')  # Show first 50 chars
        print(f'[DEBUG] [find_duplicate_clip] Using database file path: {self.pool.db_path}')
        # Convert bytes to string if needed
        if isinstance(clip_text, bytes):
            clip_text = clip_text.decode('utf-8')
        self._validate_clip_text(clip_text)

        try:
            with self.pool.get_connection() as conn:
                cursor = conn.cursor()
                query = "SELECT clip_id FROM clips_tbl WHERE clip = ? COLLATE NOCASE"
                cursor.execute(query, (clip_text,))
                result = cursor.fetchone()

                duplicate_id = result[0] if result else None
                print(f'[DEBUG] Duplicate clip ID: {duplicate_id}')
                return duplicate_id

        except sqlite3.Error as e:
            raise ClipError(f"Database error: {str(e)}")

    def read_all_clips(self) -> List[Dict[str, Any]]:
        """Retrieves all clips in reverse chronological order.

        Returns:
            list[dict]: All clips with complete metadata, sorted by:
                - timestamp (descending)
                - clip_id (descending)

        Raises:
            ClipError: Database query failure

        Example:
            [{'clip_id': 123, 'clip': '...', 'alias': '...', ...}, ...]
        """
        print('[DEBUG] Reading all clips')
        print(f'[DEBUG] [read_all_clips] Using database file path: {self.pool.db_path}')
        try:
            with self.pool.get_connection() as conn:
                cursor = conn.cursor()
                query = "SELECT * FROM clips_tbl ORDER BY timestamp DESC"
                cursor.execute(query)
                columns = [description[0] for description in cursor.description]
                results = cursor.fetchall()
                
                clips = [dict(zip(columns, row)) for row in results]
                print(f'[DEBUG] Total clips retrieved: {len(clips)}')
                return clips
                
        except sqlite3.Error as e:
            raise ClipError(f"Database error: {str(e)}")

    def update_clip(self, clip_id: int, clip: Optional[str] = None, alias: Optional[str] = None) -> int:
        """Update clip content and/or alias with validation.

        Args:
            clip_id: Positive integer > 0 of clip to update
            clip: New text content (optional)
            alias: New alias (optional)

        Returns:
            int: Number of affected rows (should be 1)

        Raises:
            ClipValidationError: Invalid input or no updates provided
            ClipNotFoundError: Specified clip_id doesn't exist

        Example:
            >>> ops.update_clip(123, clip="Updated content")
            1
            >>> ops.update_clip(123, alias="new-alias")
            1
        """
        print(f'[DEBUG] Updating clip ID {clip_id} - Clip: {clip}, Alias: {alias}')
        print(f'[DEBUG] [update_clip] Using database file path: {self.pool.db_path}')
        if not isinstance(clip_id, int) or clip_id <= 0:
            raise ClipValidationError("clip_id must be a positive integer")
            
        if clip is None and alias is None:
            raise ClipValidationError("Must provide at least one update parameter")
            
        if clip is not None:
            # Convert bytes to string if needed
            if isinstance(clip, bytes):
                clip = clip.decode('utf-8')
            self._validate_clip_text(clip)
        if alias is not None:
            self._validate_clip_alias(alias)
            
        try:
            updates = []
            params = {}

            with self.pool.get_connection() as conn:
                cursor = conn.cursor()
                if clip is not None:
                    # Check if new content differs from existing
                    cursor.execute("SELECT clip FROM clips_tbl WHERE clip_id = :clip_id", {'clip_id': clip_id})
                    current_clip = cursor.fetchone()
                    if current_clip and current_clip[0] == clip:
                        raise ClipValidationError("New content matches existing clip")
                    updates.append("clip = :clip")
                    params['clip'] = clip

                if alias is not None:
                    updates.append("alias = :alias")
                    params['alias'] = alias

                if not updates:
                    raise ClipValidationError("No valid updates provided")

                params['clip_id'] = clip_id
                query = f"UPDATE clips_tbl SET {', '.join(updates)} WHERE clip_id = :clip_id"

                cursor.execute(query, params)
                
                if cursor.rowcount == 0:
                    raise ClipNotFoundError(f"Clip with ID {clip_id} not found")
                    
                print(f'[DEBUG] Clip updated, affected rows: {cursor.rowcount}')
                return cursor.rowcount
                
        except sqlite3.Error as e:
            raise ClipError(f"Database error: {str(e)}")

    def delete_clip(self, clip_id: int) -> int:
        """Permanently delete a clip by ID.

        Args:
            clip_id: Positive integer > 0 of clip to delete

        Returns:
            int: Number of rows deleted (should be 1)

        Raises:
            ClipValidationError: Invalid clip_id format
            ClipNotFoundError: No clip with specified ID

        Example:
            >>> ops.delete_clip(123)
            1
        """
        print(f'[DEBUG] Deleting clip with ID: {clip_id}')
        print(f'[DEBUG] [delete_clip] Using database file path: {self.pool.db_path}')
        if not isinstance(clip_id, int) or clip_id <= 0:
            raise ClipValidationError("clip_id must be a positive integer")

        try:
            with self.pool.get_connection() as conn:
                cursor = conn.cursor()
                query = "DELETE FROM clips_tbl WHERE clip_id = ?"
                cursor.execute(query, (clip_id,))
                
                if cursor.rowcount == 0:
                    raise ClipNotFoundError(f"Clip with ID {clip_id} not found")
                
                conn.commit()
                print(f'[DEBUG] Clip deleted, rows affected: {cursor.rowcount}')
                return cursor.rowcount

        except sqlite3.Error as e:
            raise ClipError(f"Database error: {str(e)}") from e
    def delete_all_clips(self, confirmation: str = "NO") -> int:
        """Permanently remove all clips from database.

        Args:
            confirmation: Must be "CONFIRM" to proceed

        Returns:
            int: Number of clips deleted

        Raises:
            ClipError: Database operation failed or missing confirmation

        Example:
            >>> ops.delete_all_clips("CONFIRM")
            42
        """
        print(f'[DEBUG] Deleting all clips with confirmation: {confirmation}')
        print(f'[DEBUG] [delete_all_clips] Using database file path: {self.pool.db_path}')
        if confirmation != "CONFIRM":
            raise ClipError("Mass deletion requires confirmation string 'CONFIRM'")
        try:
            with self.pool.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM clips_tbl")
                conn.commit()
                deleted_count = cursor.rowcount
                print(f'[DEBUG] Total clips deleted: {deleted_count}')
                return deleted_count
        except sqlite3.Error as e:
            raise ClipError(f"Database error: {str(e)}") from e

    def truncate_clips_table(self) -> bool:
        """Remove all clips from the database (alias for delete_all_clips).

        Returns:
            bool: True if successful, False otherwise
        """
        print('[DEBUG] ClipsTableOperations.truncate_clips_table called')
        try:
            deleted_count = self.delete_all_clips("CONFIRM")
            print(f'[DEBUG] Truncated clips table, removed {deleted_count} clips')
            return True
        except Exception as e:
            print(f'[ERROR] Failed to truncate clips table: {e}')
            return False

# NOTE: All new code should include debug print statements at the start of every function/method.