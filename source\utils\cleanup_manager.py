"""
CleanupManager - Handles application cleanup and resource management

This manager centralizes cleanup logic for when the application is closing,
ensuring proper resource disposal and cleanup of managers.
"""

import tkinter as tk
from typing import Dict, Any, Optional, List


class CleanupManager:
    """
    Manages application cleanup and resource disposal.
    
    This class handles cleanup operations when the application is closing,
    ensuring all resources are properly disposed of and managers are cleaned up.
    """
    
    def __init__(self):
        """
        Initialize CleanupManager.
        """
        print('[DEBUG] CleanupManager.__init__ called')
        self.cleanup_handlers = []
        self.managers_to_cleanup = {}
        self.resources_to_cleanup = {}
        
    def register_cleanup_handler(self, handler_func, handler_name: str = None):
        """
        Register a cleanup handler function.
        
        Args:
            handler_func: Function to call during cleanup
            handler_name: Optional name for the handler
        """
        print(f'[DEBUG] CleanupManager.register_cleanup_handler called: {handler_name}')
        
        try:
            handler_info = {
                'function': handler_func,
                'name': handler_name or f'handler_{len(self.cleanup_handlers)}',
                'executed': False
            }
            
            self.cleanup_handlers.append(handler_info)
            print(f'[DEBUG] Cleanup handler registered: {handler_info["name"]}')
            
        except Exception as e:
            print(f'[ERROR] Failed to register cleanup handler: {e}')
            
    def register_manager_for_cleanup(self, manager, manager_name: str, cleanup_method: str = 'cleanup'):
        """
        Register a manager for cleanup.
        
        Args:
            manager: Manager instance to cleanup
            manager_name: Name of the manager
            cleanup_method: Name of the cleanup method to call (default: 'cleanup')
        """
        print(f'[DEBUG] CleanupManager.register_manager_for_cleanup called: {manager_name}')
        
        try:
            self.managers_to_cleanup[manager_name] = {
                'manager': manager,
                'cleanup_method': cleanup_method,
                'cleaned_up': False
            }
            
            print(f'[DEBUG] Manager registered for cleanup: {manager_name}')
            
        except Exception as e:
            print(f'[ERROR] Failed to register manager for cleanup: {e}')
            
    def register_resource_for_cleanup(self, resource, resource_name: str, cleanup_func):
        """
        Register a resource for cleanup.
        
        Args:
            resource: Resource to cleanup
            resource_name: Name of the resource
            cleanup_func: Function to call to cleanup the resource
        """
        print(f'[DEBUG] CleanupManager.register_resource_for_cleanup called: {resource_name}')
        
        try:
            self.resources_to_cleanup[resource_name] = {
                'resource': resource,
                'cleanup_function': cleanup_func,
                'cleaned_up': False
            }
            
            print(f'[DEBUG] Resource registered for cleanup: {resource_name}')
            
        except Exception as e:
            print(f'[ERROR] Failed to register resource for cleanup: {e}')
            
    def cleanup_clipboard_monitor(self, clipboard_monitor):
        """
        Cleanup clipboard monitor specifically.
        
        Args:
            clipboard_monitor: ClipboardMonitor instance
        """
        print('[DEBUG] CleanupManager.cleanup_clipboard_monitor called')
        
        try:
            if clipboard_monitor and hasattr(clipboard_monitor, 'stop_monitoring'):
                clipboard_monitor.stop_monitoring()
                print('[DEBUG] Clipboard monitoring stopped')
            else:
                print('[WARNING] Clipboard monitor not available or missing stop_monitoring method')
                
        except Exception as e:
            print(f'[ERROR] Error stopping clipboard monitoring: {e}')
            
    def cleanup_event_handlers(self, root: tk.Tk):
        """
        Cleanup event handlers and bindings.
        
        Args:
            root: Root tkinter window
        """
        print('[DEBUG] CleanupManager.cleanup_event_handlers called')
        
        try:
            # Unbind common event handlers
            common_bindings = [
                '<Control-z>',
                '<Control-Z>',
                '<Button-1>',
                '<Double-Button-1>',
                '<B1-Motion>',
                '<ButtonRelease-1>'
            ]
            
            for binding in common_bindings:
                try:
                    root.unbind(binding)
                except tk.TclError:
                    # Binding might not exist, ignore
                    pass
                    
            print('[DEBUG] Event handlers cleaned up')
            
        except Exception as e:
            print(f'[ERROR] Error cleaning up event handlers: {e}')
            
    def cleanup_timers_and_after_calls(self, root: tk.Tk):
        """
        Cleanup any pending timer calls and after() calls.
        
        Args:
            root: Root tkinter window
        """
        print('[DEBUG] CleanupManager.cleanup_timers_and_after_calls called')
        
        try:
            # Cancel any pending after() calls
            # Note: This is a basic implementation - more sophisticated tracking
            # would be needed for complete cleanup
            
            print('[DEBUG] Timers and after calls cleaned up')
            
        except Exception as e:
            print(f'[ERROR] Error cleaning up timers: {e}')
            
    def execute_cleanup(self, root: tk.Tk = None):
        """
        Execute all registered cleanup operations.
        
        Args:
            root: Optional root tkinter window for additional cleanup
        """
        print('[DEBUG] CleanupManager.execute_cleanup called')
        
        try:
            # Execute registered cleanup handlers
            self._execute_cleanup_handlers()
            
            # Cleanup registered managers
            self._cleanup_registered_managers()
            
            # Cleanup registered resources
            self._cleanup_registered_resources()
            
            # Additional cleanup if root provided
            if root:
                self.cleanup_event_handlers(root)
                self.cleanup_timers_and_after_calls(root)
                
            print('[DEBUG] All cleanup operations completed')
            
        except Exception as e:
            print(f'[ERROR] Error during cleanup execution: {e}')
            
    def _execute_cleanup_handlers(self):
        """Execute all registered cleanup handlers."""
        print('[DEBUG] CleanupManager._execute_cleanup_handlers called')
        
        for handler_info in self.cleanup_handlers:
            if not handler_info['executed']:
                try:
                    handler_info['function']()
                    handler_info['executed'] = True
                    print(f'[DEBUG] Cleanup handler executed: {handler_info["name"]}')
                except Exception as e:
                    print(f'[ERROR] Error executing cleanup handler {handler_info["name"]}: {e}')
                    
    def _cleanup_registered_managers(self):
        """Cleanup all registered managers."""
        print('[DEBUG] CleanupManager._cleanup_registered_managers called')
        
        for manager_name, manager_info in self.managers_to_cleanup.items():
            if not manager_info['cleaned_up']:
                try:
                    manager = manager_info['manager']
                    cleanup_method = manager_info['cleanup_method']
                    
                    if hasattr(manager, cleanup_method):
                        getattr(manager, cleanup_method)()
                        manager_info['cleaned_up'] = True
                        print(f'[DEBUG] Manager cleaned up: {manager_name}')
                    else:
                        print(f'[WARNING] Manager {manager_name} missing cleanup method: {cleanup_method}')
                        
                except Exception as e:
                    print(f'[ERROR] Error cleaning up manager {manager_name}: {e}')
                    
    def _cleanup_registered_resources(self):
        """Cleanup all registered resources."""
        print('[DEBUG] CleanupManager._cleanup_registered_resources called')
        
        for resource_name, resource_info in self.resources_to_cleanup.items():
            if not resource_info['cleaned_up']:
                try:
                    cleanup_func = resource_info['cleanup_function']
                    resource = resource_info['resource']
                    
                    cleanup_func(resource)
                    resource_info['cleaned_up'] = True
                    print(f'[DEBUG] Resource cleaned up: {resource_name}')
                    
                except Exception as e:
                    print(f'[ERROR] Error cleaning up resource {resource_name}: {e}')
                    
    def get_cleanup_status(self) -> Dict[str, Any]:
        """
        Get status of cleanup operations.
        
        Returns:
            Dictionary with cleanup status information
        """
        status = {
            'handlers': {
                'total': len(self.cleanup_handlers),
                'executed': sum(1 for h in self.cleanup_handlers if h['executed'])
            },
            'managers': {
                'total': len(self.managers_to_cleanup),
                'cleaned_up': sum(1 for m in self.managers_to_cleanup.values() if m['cleaned_up'])
            },
            'resources': {
                'total': len(self.resources_to_cleanup),
                'cleaned_up': sum(1 for r in self.resources_to_cleanup.values() if r['cleaned_up'])
            }
        }
        
        return status
        
    def reset_cleanup_status(self):
        """
        Reset cleanup status for all registered items.
        """
        print('[DEBUG] CleanupManager.reset_cleanup_status called')
        
        # Reset handler execution status
        for handler_info in self.cleanup_handlers:
            handler_info['executed'] = False
            
        # Reset manager cleanup status
        for manager_info in self.managers_to_cleanup.values():
            manager_info['cleaned_up'] = False
            
        # Reset resource cleanup status
        for resource_info in self.resources_to_cleanup.values():
            resource_info['cleaned_up'] = False
            
        print('[DEBUG] Cleanup status reset')
        
    def clear_all_registrations(self):
        """
        Clear all registered cleanup items.
        """
        print('[DEBUG] CleanupManager.clear_all_registrations called')
        
        self.cleanup_handlers.clear()
        self.managers_to_cleanup.clear()
        self.resources_to_cleanup.clear()
        
        print('[DEBUG] All cleanup registrations cleared')
