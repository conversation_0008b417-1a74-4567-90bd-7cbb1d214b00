import unittest
import sqlite3
from source.DB.db_connection import ConnectionPoolManager, get_connection

# NOTE: All new code should include debug print statements at the start of every function/method.

class TestConnectionPoolManager(unittest.TestCase):
    def setUp(self):
        self.pool = ConnectionPoolManager()

    def test_get_connection_returns_sqlite_connection(self):
        print('[DEBUG] test_get_connection_returns_sqlite_connection called')
        with self.pool.get_connection() as conn:
            self.assertIsInstance(conn, sqlite3.Connection)

    def test_get_connection_context_manager(self):
        print('[DEBUG] test_get_connection_context_manager called')
        # Should be able to use get_connection as a context manager
        try:
            with self.pool.get_connection() as conn:
                cur = conn.cursor()
                cur.execute("SELECT 1")
                result = cur.fetchone()
                self.assertEqual(result[0], 1)
        except Exception as e:
            self.fail(f"get_connection context manager failed: {e}")

    def test_multiple_connections(self):
        print('[DEBUG] test_multiple_connections called')
        # Should be able to get multiple connections without error
        conns = []
        try:
            for _ in range(self.pool.MIN_POOL_SIZE):
                conns.append(self.pool._pool.get())
            self.assertEqual(len(conns), self.pool.MIN_POOL_SIZE)
        finally:
            for conn in conns:
                self.pool._pool.put(conn)

    def test_get_connection_function(self):
        print('[DEBUG] test_get_connection_function called')
        # Test the get_connection context manager function
        with get_connection() as conn:
            self.assertIsInstance(conn, sqlite3.Connection)
            cur = conn.cursor()
            cur.execute("SELECT 1")
            self.assertEqual(cur.fetchone()[0], 1)

if __name__ == "__main__":
    unittest.main()
