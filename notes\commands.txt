#Create Schema export in sqllite3
sqlite3 clipsmore_db.db '.schema' > clipsmore_db.sql

#Promps

Rule: this SQL file has all of the DDL for the Clipsmore_db.db.  
This is the table that is the nucleus of the clipsmore applicaiton.  
In the same folder as the Clipsmore_db.db file is the API files used to inerface with the database.  
These files are in the syntax of op_*.PY.  
all interaction from the UI has to use these op_*.Py files.