import os, sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

"""View operations for the clipsmore_vw database view"""
import sqlite3
from typing import Optional, List, Dict, Any
from .db_connection import ConnectionPoolManager
from pathlib import Path

class ClipViewError(Exception):
    """Base exception for view operations."""

class ClipViewValidationError(ClipViewError):
    """Raised when invalid parameters are provided."""

class ClipsMoreViewOperations:
    """Operations for the joined clipsmore_vw view.
    Provides filtered and sorted access to the combined view of clips with their
    associated business cases and components.

    Attributes:
        db_path: Path to SQLite database file
    """

    def __init__(self, db_path: Optional[str] = None) -> None:
        """Initialize view operations with optional database path.
        Args:
            db_path: Alternate path to SQLite database file. Uses default if None.
        """
        print('[DEBUG] ClipsMoreViewOperations.__init__ called')
        if db_path is None:
            db_path = str(Path(__file__).parent / "clipsmore_db.db")
        self.pool = ConnectionPoolManager(db_path)

    def read_clipsmore_view(
        self,
        clip_id: Optional[int] = None,
        bus_case: Optional[str] = None,
        component: Optional[str] = None,
        order_by: str = "clip_timestamp",
        order_dir: str = "DESC",
        limit: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """Query the clipsmore view with filtering and sorting.

        Args:
            clip_id: Filter by specific clip ID (>0 if provided)
            bus_case: Case-insensitive partial match for business case
            component: Case-insensitive partial match for component
            order_by: Column to sort results by
            order_dir: Sort direction - ASC or DESC
            limit: Maximum records to return (>0 if provided)
        Returns:
            List[dict]: View records with columns:
                - transaction_id: Unique assignment identifier
                - clip_id: Unique clip identifier
                - clip_content: Clip content text
                - alias: Clip display name
                - clip_timestamp: Clip creation time
                - created_date: Assignment creation time
                - modified_date: Assignment modification time
                - tree_position: Position in tree
                - business_case_name: Business case name
                - component_name: Component name
                - more_bus_id: Business case ID
                - more_comp_id: Component ID
        Raises:
            ClipViewValidationError: Invalid parameter values
            ClipViewError: Database operation failed
        Example:
            >>> view_ops = ClipsMoreViewOperations()
            >>> results = view_ops.read_clipsmore_view(
                bus_case="analytics",
                limit=10
            )
        """
        print('[DEBUG] ClipsMoreViewOperations.read_clipsmore_view called')
        with self.pool.get_connection() as conn:
            conditions = []
            params = []

            if clip_id is not None:
                conditions.append("clip_id = ?")
                params.append(clip_id)

            if bus_case:
                conditions.append("bus_case LIKE ?")
                params.append(f"%{bus_case}%")

            if component:
                conditions.append("bus_component LIKE ?")
                params.append(f"%{component}%")

            query = "SELECT * FROM clipsmore_vw"

            if conditions:
                query += " WHERE " + " AND ".join(conditions)

            # Validate order_by column exists in view
            valid_columns = ["clip_id", "clip_content", "alias", "clip_timestamp", "business_case_name", "component_name", "created_date", "modified_date", "tree_position", "transaction_id"]
            if order_by not in valid_columns:
                raise ClipViewValidationError(
                    f"Invalid sort column '{order_by}'. Valid options: {', '.join(valid_columns)}"
                )

            if order_dir.upper() not in ["ASC", "DESC"]:
                raise ClipViewValidationError("Sort direction must be 'ASC' or 'DESC'")

            query += f" ORDER BY {order_by} {order_dir}"

            if limit is not None:
                if not isinstance(limit, int) or limit <= 0:
                    raise ClipViewValidationError("Limit must be positive integer >0")
                query += " LIMIT ?"
                params.append(limit)

            try:
                cursor = conn.cursor()
                cursor.execute(query, tuple(params))
                columns = [description[0] for description in cursor.description]
                results = cursor.fetchall()

                return [dict(zip(columns, row)) for row in results]

            except sqlite3.Error as e:
                raise ClipViewError(f"View query failed: {str(e)}") from e

# NOTE: All new code should include debug print statements at the start of every function/method.