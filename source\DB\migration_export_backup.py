#!/usr/bin/env python3
"""
Database Migration for Export & Backup System
Adds tables for export templates, backup history, and import history.
"""

import os
import sys
import sqlite3
from datetime import datetime

# Add parent directories to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from db_connection import ConnectionPoolManager


class ExportBackupMigration:
    """Migration class for export and backup system tables."""
    
    def __init__(self):
        """Initialize the migration."""
        print('[DEBUG] ExportBackupMigration.__init__ called')
        self.connection_pool = ConnectionPoolManager()
        self.migration_version = "export_backup_v1"
    
    def run_migration(self) -> bool:
        """
        Run the migration to add export/backup tables.
        
        Returns:
            True if migration successful, False otherwise
        """
        print('[DEBUG] ExportBackupMigration.run_migration called')
        
        try:
            with self.connection_pool.get_connection() as conn:
                cursor = conn.cursor()
                
                # Check if migration already applied
                if self._is_migration_applied(cursor):
                    print('[INFO] Export/backup migration already applied')
                    return True
                
                # Create export templates table
                self._create_export_templates_table(cursor)
                
                # Create backup history table
                self._create_backup_history_table(cursor)
                
                # Create import history table
                self._create_import_history_table(cursor)
                
                # Create migration tracking table if it doesn't exist
                self._create_migration_tracking_table(cursor)
                
                # Record migration
                self._record_migration(cursor)
                
                conn.commit()
                print('[INFO] Export/backup migration completed successfully')
                return True
                
        except sqlite3.Error as e:
            print(f'[ERROR] Migration failed: {e}')
            return False
        except Exception as e:
            print(f'[ERROR] Unexpected error during migration: {e}')
            return False
    
    def _is_migration_applied(self, cursor: sqlite3.Cursor) -> bool:
        """Check if this migration has already been applied."""
        try:
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name='migration_history'
            """)
            
            if not cursor.fetchone():
                return False
            
            cursor.execute("""
                SELECT 1 FROM migration_history 
                WHERE migration_name = ?
            """, (self.migration_version,))
            
            return cursor.fetchone() is not None
            
        except sqlite3.Error:
            return False
    
    def _create_export_templates_table(self, cursor: sqlite3.Cursor):
        """Create the export_templates table."""
        print('[DEBUG] Creating export_templates table')
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS export_templates (
                template_id INTEGER PRIMARY KEY AUTOINCREMENT,
                template_name TEXT UNIQUE NOT NULL,
                format_type TEXT NOT NULL,
                selection_criteria TEXT,
                field_configuration TEXT,
                export_config TEXT,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_used TIMESTAMP,
                use_count INTEGER DEFAULT 0,
                description TEXT
            )
        """)
        
        # Create indexes for export templates
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_export_templates_name 
            ON export_templates(template_name)
        """)
        
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_export_templates_format 
            ON export_templates(format_type)
        """)
        
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_export_templates_last_used 
            ON export_templates(last_used DESC)
        """)
    
    def _create_backup_history_table(self, cursor: sqlite3.Cursor):
        """Create the backup_history table."""
        print('[DEBUG] Creating backup_history table')
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS backup_history (
                backup_id INTEGER PRIMARY KEY AUTOINCREMENT,
                backup_type TEXT NOT NULL,
                backup_path TEXT NOT NULL,
                file_size INTEGER,
                compression_ratio REAL,
                verification_status TEXT DEFAULT 'pending',
                backup_config TEXT,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                verified_date TIMESTAMP,
                restore_count INTEGER DEFAULT 0,
                notes TEXT,
                checksum TEXT
            )
        """)
        
        # Create indexes for backup history
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_backup_history_type 
            ON backup_history(backup_type)
        """)
        
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_backup_history_created 
            ON backup_history(created_date DESC)
        """)
        
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_backup_history_status 
            ON backup_history(verification_status)
        """)
    
    def _create_import_history_table(self, cursor: sqlite3.Cursor):
        """Create the import_history table."""
        print('[DEBUG] Creating import_history table')
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS import_history (
                import_id INTEGER PRIMARY KEY AUTOINCREMENT,
                source_file TEXT NOT NULL,
                source_format TEXT,
                records_imported INTEGER DEFAULT 0,
                records_skipped INTEGER DEFAULT 0,
                duplicates_found INTEGER DEFAULT 0,
                import_status TEXT DEFAULT 'pending',
                import_config TEXT,
                import_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                completed_date TIMESTAMP,
                rollback_data TEXT,
                error_log TEXT,
                import_summary TEXT
            )
        """)
        
        # Create indexes for import history
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_import_history_format 
            ON import_history(source_format)
        """)
        
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_import_history_date 
            ON import_history(import_date DESC)
        """)
        
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_import_history_status 
            ON import_history(import_status)
        """)
    
    def _create_migration_tracking_table(self, cursor: sqlite3.Cursor):
        """Create migration tracking table if it doesn't exist."""
        print('[DEBUG] Creating migration_history table')
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS migration_history (
                migration_id INTEGER PRIMARY KEY AUTOINCREMENT,
                migration_name TEXT UNIQUE NOT NULL,
                applied_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                description TEXT
            )
        """)
    
    def _record_migration(self, cursor: sqlite3.Cursor):
        """Record this migration in the tracking table."""
        print('[DEBUG] Recording migration in history')
        
        cursor.execute("""
            INSERT INTO migration_history (migration_name, description)
            VALUES (?, ?)
        """, (
            self.migration_version,
            "Added export templates, backup history, and import history tables"
        ))
    
    def rollback_migration(self) -> bool:
        """
        Rollback the migration (drop the created tables).
        
        Returns:
            True if rollback successful, False otherwise
        """
        print('[DEBUG] ExportBackupMigration.rollback_migration called')
        
        try:
            with self.connection_pool.get_connection() as conn:
                cursor = conn.cursor()
                
                # Drop tables in reverse order
                tables_to_drop = [
                    'import_history',
                    'backup_history',
                    'export_templates'
                ]
                
                for table in tables_to_drop:
                    cursor.execute(f"DROP TABLE IF EXISTS {table}")
                    print(f'[DEBUG] Dropped table: {table}')
                
                # Remove migration record
                cursor.execute("""
                    DELETE FROM migration_history 
                    WHERE migration_name = ?
                """, (self.migration_version,))
                
                conn.commit()
                print('[INFO] Export/backup migration rollback completed')
                return True
                
        except sqlite3.Error as e:
            print(f'[ERROR] Migration rollback failed: {e}')
            return False
        except Exception as e:
            print(f'[ERROR] Unexpected error during rollback: {e}')
            return False
    
    def get_migration_status(self) -> dict:
        """Get the status of this migration."""
        print('[DEBUG] ExportBackupMigration.get_migration_status called')
        
        try:
            with self.connection_pool.get_connection() as conn:
                cursor = conn.cursor()
                
                # Check if tables exist
                tables = ['export_templates', 'backup_history', 'import_history']
                table_status = {}
                
                for table in tables:
                    cursor.execute("""
                        SELECT name FROM sqlite_master 
                        WHERE type='table' AND name=?
                    """, (table,))
                    table_status[table] = cursor.fetchone() is not None
                
                # Check migration record
                migration_applied = self._is_migration_applied(cursor)
                
                return {
                    'migration_name': self.migration_version,
                    'applied': migration_applied,
                    'tables': table_status,
                    'all_tables_exist': all(table_status.values())
                }
                
        except Exception as e:
            print(f'[ERROR] Error checking migration status: {e}')
            return {
                'migration_name': self.migration_version,
                'applied': False,
                'error': str(e)
            }


def main():
    """Run the migration."""
    print("Running Export & Backup System Migration")
    print("=" * 40)
    
    migration = ExportBackupMigration()
    
    # Check current status
    status = migration.get_migration_status()
    print(f"Migration Status: {status}")
    
    if not status.get('applied', False):
        print("\nApplying migration...")
        success = migration.run_migration()
        
        if success:
            print("✅ Migration completed successfully!")
        else:
            print("❌ Migration failed!")
            return False
    else:
        print("✅ Migration already applied!")
    
    return True


if __name__ == "__main__":
    main()
