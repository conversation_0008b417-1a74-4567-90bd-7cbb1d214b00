#!/usr/bin/env python3
"""
Test suite for enhanced ClipsMore operations with v2.0 schema.
"""

import unittest
import sqlite3
import os
import sys
import tempfile

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from op_clipsmore_enhanced import ClipsMoreEnhancedOperations, ClipsMoreError
from db_connection import ConnectionPoolManager

# NOTE: All new code should include debug print statements at the start of every function/method.

class TestClipsMoreEnhancedOperations(unittest.TestCase):
    """Test cases for enhanced ClipsMore operations."""
    
    def setUp(self):
        """Set up test database."""
        print('[DEBUG] TestClipsMoreEnhancedOperations.setUp called')
        
        # Create temporary database
        self.test_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.test_db.close()
        self.db_path = self.test_db.name
        
        # Initialize test database with schema
        self._create_test_schema()
        self._insert_test_data()
        
        # Initialize operations class
        self.ops = ClipsMoreEnhancedOperations(self.db_path)
    
    def tearDown(self):
        """Clean up test database."""
        print('[DEBUG] TestClipsMoreEnhancedOperations.tearDown called')
        if os.path.exists(self.db_path):
            os.unlink(self.db_path)
    
    def _create_test_schema(self):
        """Create test database schema."""
        print('[DEBUG] Creating test schema')
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Enable foreign keys
            cursor.execute("PRAGMA foreign_keys = ON")
            
            # Create clips_tbl
            cursor.execute("""
                CREATE TABLE clips_tbl (
                    clip_id INTEGER PRIMARY KEY,
                    clip BLOB NOT NULL,
                    alias TEXT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Create more_bus_tbl
            cursor.execute("""
                CREATE TABLE more_bus_tbl (
                    more_bus_id INTEGER PRIMARY KEY,
                    bus_case TEXT NOT NULL
                )
            """)
            
            # Create more_comp_tbl
            cursor.execute("""
                CREATE TABLE more_comp_tbl (
                    more_comp_id INTEGER PRIMARY KEY,
                    more_bus_id INTEGER NOT NULL,
                    bus_component TEXT,
                    FOREIGN KEY (more_bus_id) REFERENCES more_bus_tbl (more_bus_id) ON DELETE CASCADE
                )
            """)
            
            # Create enhanced clipsmore_tbl
            cursor.execute("""
                CREATE TABLE clipsmore_tbl (
                    transaction_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    clip_id INTEGER NOT NULL,
                    alias TEXT UNIQUE,
                    more_bus_id INTEGER NOT NULL,
                    more_comp_id INTEGER,
                    tree_position INTEGER DEFAULT 0,
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (clip_id) REFERENCES clips_tbl (clip_id) ON DELETE CASCADE,
                    FOREIGN KEY (more_bus_id) REFERENCES more_bus_tbl (more_bus_id) ON DELETE CASCADE,
                    FOREIGN KEY (more_comp_id) REFERENCES more_comp_tbl (more_comp_id) ON DELETE SET NULL,
                    CONSTRAINT uq_clip_more UNIQUE (clip_id, more_bus_id, more_comp_id)
                )
            """)
            
            # Create clipsmore_vw
            cursor.execute("""
                CREATE VIEW clipsmore_vw AS
                SELECT 
                    cm.transaction_id,
                    cm.clip_id,
                    cm.alias,
                    cm.tree_position,
                    cm.created_date,
                    cm.modified_date,
                    c.clip as clip_content,
                    c.timestamp as clip_timestamp,
                    mb.bus_case as business_case_name,
                    mb.more_bus_id,
                    mc.bus_component as component_name,
                    mc.more_comp_id
                FROM clipsmore_tbl cm
                LEFT JOIN clips_tbl c ON cm.clip_id = c.clip_id
                LEFT JOIN more_bus_tbl mb ON cm.more_bus_id = mb.more_bus_id
                LEFT JOIN more_comp_tbl mc ON cm.more_comp_id = mc.more_comp_id
                ORDER BY cm.tree_position, cm.created_date
            """)
            
            conn.commit()
    
    def _insert_test_data(self):
        """Insert test data."""
        print('[DEBUG] Inserting test data')
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # Insert comprehensive cyber security clips
            cursor.execute("INSERT INTO clips_tbl (clip_id, clip) VALUES (1, ?)",
                         ("Cybercriminals are getting smarter. Are you?\nA 76-year-old retired lawyer lost his entire life savings to a sophisticated phishing attack.",))
            cursor.execute("INSERT INTO clips_tbl (clip_id, clip) VALUES (2, ?)",
                         ("Imposter scams: They can pose as government agents by phone or email, claiming your accounts are compromised and demanding immediate payment.",))
            cursor.execute("INSERT INTO clips_tbl (clip_id, clip) VALUES (3, ?)",
                         ("Romance scams: These cybercriminals play the long game— building trust over weeks by phone, text, or social media before asking for money.",))
            cursor.execute("INSERT INTO clips_tbl (clip_id, clip) VALUES (4, ?)",
                         ("Call-center scams: If you get a call or receive a computer pop up claiming your computer is experiencing problems, hang up immediately.",))
            cursor.execute("INSERT INTO clips_tbl (clip_id, clip) VALUES (5, ?)",
                         ("Endpoint security solutions protect individual devices like computers and mobile phones from malware, unauthorized access, and data breaches.",))
            cursor.execute("INSERT INTO clips_tbl (clip_id, clip) VALUES (6, ?)",
                         ("Security Information and Event Management (SIEM) systems collect and analyze security data from across your network to detect threats.",))
            cursor.execute("INSERT INTO clips_tbl (clip_id, clip) VALUES (7, ?)",
                         ("Penetration testing involves authorized simulated attacks on your systems to identify vulnerabilities before real attackers do.",))

            # Insert comprehensive cyber security business cases
            cursor.execute("INSERT INTO more_bus_tbl (more_bus_id, bus_case) VALUES (1, ?)", ("Cyber Security",))
            cursor.execute("INSERT INTO more_bus_tbl (more_bus_id, bus_case) VALUES (2, ?)", ("Network Security",))
            
            # Insert comprehensive cyber security components
            cursor.execute("INSERT INTO more_comp_tbl (more_comp_id, more_bus_id, bus_component) VALUES (1, 1, ?)", ("Imposter scams",))
            cursor.execute("INSERT INTO more_comp_tbl (more_comp_id, more_bus_id, bus_component) VALUES (2, 1, ?)", ("Romance scams",))
            cursor.execute("INSERT INTO more_comp_tbl (more_comp_id, more_bus_id, bus_component) VALUES (3, 1, ?)", ("Call-center scams",))
            cursor.execute("INSERT INTO more_comp_tbl (more_comp_id, more_bus_id, bus_component) VALUES (4, 2, ?)", ("Endpoint security",))
            cursor.execute("INSERT INTO more_comp_tbl (more_comp_id, more_bus_id, bus_component) VALUES (5, 2, ?)", ("SIEM systems",))
            cursor.execute("INSERT INTO more_comp_tbl (more_comp_id, more_bus_id, bus_component) VALUES (6, 2, ?)", ("Penetration testing",))
            
            conn.commit()
    
    def test_create_assignment_with_auto_alias(self):
        """Test creating assignment with auto-generated alias."""
        print('[DEBUG] test_create_assignment_with_auto_alias')
        
        transaction_id = self.ops.create_assignment(
            clip_id=1,
            more_bus_id=1,
            more_comp_id=1
        )
        
        self.assertIsInstance(transaction_id, int)
        self.assertGreater(transaction_id, 0)
        
        # Verify assignment was created
        assignments = self.ops.get_assignments_by_component(1)
        self.assertEqual(len(assignments), 1)
        self.assertEqual(assignments[0]['clip_id'], 1)
        self.assertIsNotNone(assignments[0]['alias'])
    
    def test_create_assignment_with_custom_alias(self):
        """Test creating assignment with custom alias."""
        print('[DEBUG] test_create_assignment_with_custom_alias')
        
        custom_alias = "my_custom_alias"
        transaction_id = self.ops.create_assignment(
            clip_id=2,
            more_bus_id=1,
            alias=custom_alias
        )
        
        self.assertIsInstance(transaction_id, int)
        
        # Verify custom alias was used
        assignments = self.ops.get_assignments_by_business_case(1)
        found_assignment = next((a for a in assignments if a['transaction_id'] == transaction_id), None)
        self.assertIsNotNone(found_assignment)
        self.assertEqual(found_assignment['alias'], custom_alias)
    
    def test_duplicate_alias_prevention(self):
        """Test that duplicate aliases are prevented."""
        print('[DEBUG] test_duplicate_alias_prevention')
        
        alias = "duplicate_test"
        
        # Create first assignment
        self.ops.create_assignment(
            clip_id=1,
            more_bus_id=1,
            alias=alias
        )
        
        # Try to create second assignment with same alias
        with self.assertRaises(ClipsMoreError):
            self.ops.create_assignment(
                clip_id=2,
                more_bus_id=1,
                alias=alias
            )
    
    def test_move_assignment(self):
        """Test moving assignment to different business case/component."""
        print('[DEBUG] test_move_assignment')
        
        # Create assignment
        transaction_id = self.ops.create_assignment(
            clip_id=1,
            more_bus_id=1,
            more_comp_id=1
        )
        
        # Move to different component
        success = self.ops.move_assignment(
            transaction_id=transaction_id,
            target_bus_id=1,
            target_comp_id=2
        )
        
        self.assertTrue(success)
        
        # Verify move
        assignments = self.ops.get_assignments_by_component(2)
        self.assertEqual(len(assignments), 1)
        self.assertEqual(assignments[0]['transaction_id'], transaction_id)
        
        # Verify old location is empty
        assignments = self.ops.get_assignments_by_component(1)
        self.assertEqual(len(assignments), 0)
    
    def test_copy_assignment(self):
        """Test copying assignment with new alias."""
        print('[DEBUG] test_copy_assignment')
        
        # Create original assignment
        original_transaction_id = self.ops.create_assignment(
            clip_id=1,
            more_bus_id=1,
            more_comp_id=1,
            alias="original_alias"
        )
        
        # Copy to different component
        new_transaction_id = self.ops.copy_assignment(
            transaction_id=original_transaction_id,
            target_bus_id=1,
            target_comp_id=2
        )
        
        self.assertNotEqual(original_transaction_id, new_transaction_id)
        
        # Verify both assignments exist
        assignments_comp1 = self.ops.get_assignments_by_component(1)
        assignments_comp2 = self.ops.get_assignments_by_component(2)
        
        self.assertEqual(len(assignments_comp1), 1)
        self.assertEqual(len(assignments_comp2), 1)
        
        # Verify different aliases
        self.assertNotEqual(assignments_comp1[0]['alias'], assignments_comp2[0]['alias'])
        self.assertTrue(assignments_comp2[0]['alias'].startswith('original_alias_copy'))
    
    def test_update_assignment(self):
        """Test updating assignment properties."""
        print('[DEBUG] test_update_assignment')
        
        # Create assignment
        transaction_id = self.ops.create_assignment(
            clip_id=1,
            more_bus_id=1,
            alias="original_alias"
        )
        
        # Update alias
        new_alias = "updated_alias"
        success = self.ops.update_assignment(
            transaction_id=transaction_id,
            alias=new_alias
        )
        
        self.assertTrue(success)
        
        # Verify update
        assignments = self.ops.get_assignments_by_business_case(1)
        found_assignment = next((a for a in assignments if a['transaction_id'] == transaction_id), None)
        self.assertIsNotNone(found_assignment)
        self.assertEqual(found_assignment['alias'], new_alias)
    
    def test_delete_assignment(self):
        """Test deleting assignment."""
        print('[DEBUG] test_delete_assignment')
        
        # Create assignment
        transaction_id = self.ops.create_assignment(
            clip_id=1,
            more_bus_id=1
        )
        
        # Verify it exists
        assignments = self.ops.get_assignments_by_business_case(1)
        self.assertEqual(len(assignments), 1)
        
        # Delete assignment
        success = self.ops.delete_assignment(transaction_id)
        self.assertTrue(success)
        
        # Verify deletion
        assignments = self.ops.get_assignments_by_business_case(1)
        self.assertEqual(len(assignments), 0)
    
    def test_tree_positioning(self):
        """Test tree position management."""
        print('[DEBUG] test_tree_positioning')
        
        # Create multiple assignments
        transaction_id1 = self.ops.create_assignment(clip_id=1, more_bus_id=1)
        transaction_id2 = self.ops.create_assignment(clip_id=2, more_bus_id=1)
        transaction_id3 = self.ops.create_assignment(clip_id=3, more_bus_id=1)
        
        # Get assignments and verify positions
        assignments = self.ops.get_assignments_by_business_case(1)
        self.assertEqual(len(assignments), 3)
        
        # Verify positions are sequential
        positions = [a['tree_position'] for a in assignments]
        self.assertEqual(sorted(positions), [0, 1, 2])

if __name__ == '__main__':
    print('[DEBUG] Running enhanced operations tests')
    unittest.main(verbosity=2)
