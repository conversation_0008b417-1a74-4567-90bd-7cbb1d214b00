#!/usr/bin/env python3
"""
Database Migration Script for ClipsMore v2.0
Migrates existing clipsmore_tbl to new enhanced schema with alias and tree_position support.
"""

import sqlite3
import os
import shutil
from datetime import datetime
from typing import Optional
from .db_connection import ConnectionPoolManager

# NOTE: All new code should include debug print statements at the start of every function/method.

class DatabaseMigrationV2:
    """Handles migration from v1 to v2 database schema."""
    
    def __init__(self, db_path: Optional[str] = None):
        """Initialize migration with database path.
        
        Args:
            db_path: Path to database file. Uses default if None.
        """
        print('[DEBUG] DatabaseMigrationV2.__init__ called')
        self.db_path = db_path or ConnectionPoolManager.DEFAULT_DB_PATH
        self.backup_path = f"{self.db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
    def backup_database(self) -> bool:
        """Create backup of current database.
        
        Returns:
            True if backup successful, False otherwise.
        """
        print('[DEBUG] DatabaseMigrationV2.backup_database called')
        try:
            if os.path.exists(self.db_path):
                shutil.copy2(self.db_path, self.backup_path)
                print(f'[DEBUG] Database backed up to: {self.backup_path}')
                return True
            else:
                print('[DEBUG] No existing database found, creating new one')
                return True
        except Exception as e:
            print(f'[ERROR] Failed to backup database: {e}')
            return False
    
    def check_current_schema(self) -> dict:
        """Check current database schema and return table info.
        
        Returns:
            Dictionary with table information.
        """
        print('[DEBUG] DatabaseMigrationV2.check_current_schema called')
        schema_info = {
            'clipsmore_tbl_exists': False,
            'clipsmore_tbl_columns': [],
            'needs_migration': False
        }
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Check if clipsmore_tbl exists
                cursor.execute("""
                    SELECT name FROM sqlite_master 
                    WHERE type='table' AND name='clipsmore_tbl'
                """)
                if cursor.fetchone():
                    schema_info['clipsmore_tbl_exists'] = True
                    
                    # Get current column info
                    cursor.execute("PRAGMA table_info(clipsmore_tbl)")
                    columns = cursor.fetchall()
                    schema_info['clipsmore_tbl_columns'] = [col[1] for col in columns]
                    
                    # Check if migration is needed
                    required_columns = ['transaction_id', 'alias', 'tree_position']
                    missing_columns = [col for col in required_columns 
                                     if col not in schema_info['clipsmore_tbl_columns']]
                    schema_info['needs_migration'] = len(missing_columns) > 0
                    schema_info['missing_columns'] = missing_columns
                    
                print(f'[DEBUG] Schema info: {schema_info}')
                return schema_info
                
        except sqlite3.Error as e:
            print(f'[ERROR] Failed to check schema: {e}')
            return schema_info
    
    def migrate_clipsmore_table(self) -> bool:
        """Migrate clipsmore_tbl to new schema.
        
        Returns:
            True if migration successful, False otherwise.
        """
        print('[DEBUG] DatabaseMigrationV2.migrate_clipsmore_table called')
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Enable foreign key constraints
                cursor.execute("PRAGMA foreign_keys = ON")
                
                # Check if old table exists
                cursor.execute("""
                    SELECT name FROM sqlite_master 
                    WHERE type='table' AND name='clipsmore_tbl'
                """)
                
                if cursor.fetchone():
                    print('[DEBUG] Migrating existing clipsmore_tbl')
                    
                    # Get existing data
                    cursor.execute("SELECT * FROM clipsmore_tbl")
                    existing_data = cursor.fetchall()
                    print(f'[DEBUG] Found {len(existing_data)} existing records')
                    
                    # Rename old table
                    cursor.execute("ALTER TABLE clipsmore_tbl RENAME TO clipsmore_tbl_old")
                    
                    # Create new table with enhanced schema
                    cursor.execute("""
                        CREATE TABLE clipsmore_tbl (
                            transaction_id INTEGER PRIMARY KEY AUTOINCREMENT,
                            clip_id INTEGER NOT NULL,
                            alias TEXT UNIQUE,
                            more_bus_id INTEGER NOT NULL,
                            more_comp_id INTEGER,
                            tree_position INTEGER DEFAULT 0,
                            created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            FOREIGN KEY (clip_id) REFERENCES clips_tbl (clip_id) ON DELETE CASCADE,
                            FOREIGN KEY (more_bus_id) REFERENCES more_bus_tbl (more_bus_id) ON DELETE CASCADE,
                            FOREIGN KEY (more_comp_id) REFERENCES more_comp_tbl (more_comp_id) ON DELETE SET NULL,
                            CONSTRAINT uq_clip_more UNIQUE (clip_id, more_bus_id, more_comp_id)
                        )
                    """)
                    
                    # Migrate existing data with auto-generated aliases
                    for i, row in enumerate(existing_data):
                        clip_id, more_bus_id, more_comp_id = row
                        
                        # Generate unique alias
                        alias = self._generate_alias_for_clip(cursor, clip_id, i + 1)
                        
                        cursor.execute("""
                            INSERT INTO clipsmore_tbl 
                            (clip_id, alias, more_bus_id, more_comp_id, tree_position)
                            VALUES (?, ?, ?, ?, ?)
                        """, (clip_id, alias, more_bus_id, more_comp_id, i))
                    
                    # Drop old table
                    cursor.execute("DROP TABLE clipsmore_tbl_old")
                    print('[DEBUG] Migration completed successfully')
                    
                else:
                    print('[DEBUG] Creating new clipsmore_tbl')
                    # Create new table from scratch
                    cursor.execute("""
                        CREATE TABLE clipsmore_tbl (
                            transaction_id INTEGER PRIMARY KEY AUTOINCREMENT,
                            clip_id INTEGER NOT NULL,
                            alias TEXT UNIQUE,
                            more_bus_id INTEGER NOT NULL,
                            more_comp_id INTEGER,
                            tree_position INTEGER DEFAULT 0,
                            created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            FOREIGN KEY (clip_id) REFERENCES clips_tbl (clip_id) ON DELETE CASCADE,
                            FOREIGN KEY (more_bus_id) REFERENCES more_bus_tbl (more_bus_id) ON DELETE CASCADE,
                            FOREIGN KEY (more_comp_id) REFERENCES more_comp_tbl (more_comp_id) ON DELETE SET NULL,
                            CONSTRAINT uq_clip_more UNIQUE (clip_id, more_bus_id, more_comp_id)
                        )
                    """)
                
                # Create indexes for performance
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_clipsmore_alias ON clipsmore_tbl(alias)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_clipsmore_tree_pos ON clipsmore_tbl(tree_position)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_clipsmore_bus_id ON clipsmore_tbl(more_bus_id)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_clipsmore_comp_id ON clipsmore_tbl(more_comp_id)")
                
                conn.commit()
                return True
                
        except sqlite3.Error as e:
            print(f'[ERROR] Migration failed: {e}')
            return False
    
    def _generate_alias_for_clip(self, cursor, clip_id: int, fallback_num: int) -> str:
        """Generate unique alias for a clip during migration.
        
        Args:
            cursor: Database cursor
            clip_id: ID of the clip
            fallback_num: Fallback number for generic aliases
            
        Returns:
            Unique alias string
        """
        print(f'[DEBUG] _generate_alias_for_clip called for clip_id={clip_id}')
        
        try:
            # Get clip content
            cursor.execute("SELECT clip FROM clips_tbl WHERE clip_id = ?", (clip_id,))
            result = cursor.fetchone()
            
            if result and result[0]:
                content = str(result[0])
                # Extract meaningful words (basic implementation)
                words = content.split()[:3]  # Take first 3 words
                if words:
                    alias = "_".join(word.strip('.,!?;:"()[]{}') for word in words)
                    alias = alias[:20]  # Limit length
                    
                    # Check if alias is unique
                    cursor.execute("SELECT alias FROM clipsmore_tbl WHERE alias = ?", (alias,))
                    if not cursor.fetchone():
                        return alias
            
            # Fallback to generic name
            alias = f"clip_{fallback_num}"
            counter = 1
            original_alias = alias
            
            # Ensure uniqueness
            while True:
                cursor.execute("SELECT alias FROM clipsmore_tbl WHERE alias = ?", (alias,))
                if not cursor.fetchone():
                    break
                counter += 1
                alias = f"{original_alias}_{counter}"
            
            return alias
            
        except Exception as e:
            print(f'[ERROR] Failed to generate alias: {e}')
            return f"clip_{fallback_num}"
    
    def update_clipsmore_view(self) -> bool:
        """Update clipsmore_vw to include new fields.
        
        Returns:
            True if successful, False otherwise.
        """
        print('[DEBUG] DatabaseMigrationV2.update_clipsmore_view called')
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Drop existing view
                cursor.execute("DROP VIEW IF EXISTS clipsmore_vw")
                
                # Create updated view
                cursor.execute("""
                    CREATE VIEW clipsmore_vw AS
                    SELECT 
                        cm.transaction_id,
                        cm.clip_id,
                        cm.alias,
                        cm.tree_position,
                        cm.created_date,
                        cm.modified_date,
                        c.clip as clip_content,
                        c.timestamp as clip_timestamp,
                        mb.bus_case as business_case_name,
                        mb.more_bus_id,
                        mc.bus_component as component_name,
                        mc.more_comp_id
                    FROM clipsmore_tbl cm
                    LEFT JOIN clips_tbl c ON cm.clip_id = c.clip_id
                    LEFT JOIN more_bus_tbl mb ON cm.more_bus_id = mb.more_bus_id
                    LEFT JOIN more_comp_tbl mc ON cm.more_comp_id = mc.more_comp_id
                    ORDER BY cm.tree_position, cm.created_date
                """)
                
                conn.commit()
                print('[DEBUG] clipsmore_vw updated successfully')
                return True
                
        except sqlite3.Error as e:
            print(f'[ERROR] Failed to update view: {e}')
            return False
    
    def run_migration(self) -> bool:
        """Run complete migration process.
        
        Returns:
            True if migration successful, False otherwise.
        """
        print('[DEBUG] DatabaseMigrationV2.run_migration called')
        
        # Step 1: Backup database
        if not self.backup_database():
            print('[ERROR] Failed to backup database')
            return False
        
        # Step 2: Check current schema
        schema_info = self.check_current_schema()
        
        if not schema_info['needs_migration'] and schema_info['clipsmore_tbl_exists']:
            print('[DEBUG] Database already up to date')
            return True
        
        # Step 3: Migrate table
        if not self.migrate_clipsmore_table():
            print('[ERROR] Failed to migrate clipsmore_tbl')
            return False
        
        # Step 4: Update view
        if not self.update_clipsmore_view():
            print('[ERROR] Failed to update clipsmore_vw')
            return False
        
        print('[DEBUG] Migration completed successfully')
        return True

def main():
    """Run migration if called directly."""
    print('[DEBUG] Running database migration v2')
    migration = DatabaseMigrationV2()
    success = migration.run_migration()
    
    if success:
        print('[SUCCESS] Database migration completed successfully')
    else:
        print('[ERROR] Database migration failed')
        
    return success

if __name__ == "__main__":
    main()
