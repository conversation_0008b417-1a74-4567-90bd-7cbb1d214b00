#!/usr/bin/env python3
"""
Test script for the Clear All Data button changes.
Verifies that:
1. More tab button is renamed to "Clear Bus Case/Comp"
2. New "Clear All Data" button exists at top level
3. Functionality works correctly
"""

import os
import sys
import tkinter as tk
from unittest.mock import patch, MagicMock

# Add parent directories to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ui_manager import UIManager
from utils.more_tab_manager import MoreTabManager


def test_more_tab_button_renamed():
    """Test that the More tab button is renamed to 'Clear Bus Case/Comp'."""
    print('[TEST] Starting More tab button rename test')
    
    try:
        # Create root window
        root = tk.Tk()
        root.withdraw()
        
        # Create UI manager
        ui_manager = UIManager(root)
        
        # Get the More tab frame
        more_tab = ui_manager.more_tab
        if not more_tab:
            print('[ERROR] More tab not found')
            return False
        
        print('[TEST] More tab found, checking for renamed button...')
        
        # Search through all widgets in the More tab for the renamed button
        def find_button_with_text(widget, target_text):
            """Recursively search for button with specific text."""
            found_buttons = []
            try:
                # Check if this widget is a button with the target text
                if isinstance(widget, tk.Button):
                    text = widget.cget('text')
                    if target_text in text:
                        found_buttons.append(text)
                        print(f'[TEST] Found button: {text}')
                
                # Recursively check children
                for child in widget.winfo_children():
                    found_buttons.extend(find_button_with_text(child, target_text))
                    
            except Exception as e:
                # Some widgets might not support certain operations
                pass
            
            return found_buttons
        
        # Look for the renamed button
        renamed_buttons = find_button_with_text(more_tab, "Clear Bus Case/Comp")
        old_buttons = find_button_with_text(more_tab, "Clear All Data")
        
        if renamed_buttons:
            print(f'[TEST] ✅ Found renamed button: {renamed_buttons[0]}')
        else:
            print('[ERROR] Renamed button "Clear Bus Case/Comp" not found')
            return False
        
        if old_buttons:
            print(f'[ERROR] Old button text "Clear All Data" still found in More tab: {old_buttons}')
            return False
        
        # Clean up
        root.destroy()
        
        print('[TEST] ✅ More tab button rename test passed!')
        return True
        
    except Exception as e:
        print(f'[ERROR] Test failed with exception: {e}')
        import traceback
        traceback.print_exc()
        return False


def test_top_level_clear_all_button():
    """Test that the new 'Clear All Data' button exists at top level."""
    print('\n[TEST] Starting top-level Clear All Data button test')
    
    try:
        # Create root window
        root = tk.Tk()
        root.withdraw()
        
        # Create UI manager
        ui_manager = UIManager(root)
        
        # Check that clear all button was created
        if not hasattr(ui_manager, 'clear_all_button') or not ui_manager.clear_all_button:
            print('[ERROR] Clear All Data button not found in UI manager')
            return False
        
        print('[TEST] ✅ Clear All Data button found in UI manager')
        
        # Check button properties
        button_text = ui_manager.clear_all_button.cget('text')
        button_bg = ui_manager.clear_all_button.cget('bg')
        
        if '🗑️' not in button_text or 'Clear All Data' not in button_text:
            print(f'[ERROR] Clear All Data button has incorrect text: {button_text}')
            return False
        
        if button_bg != '#d32f2f':
            print(f'[ERROR] Clear All Data button has incorrect background color: {button_bg}')
            return False
        
        print(f'[TEST] ✅ Button text correct: "{button_text}"')
        print(f'[TEST] ✅ Button color correct: {button_bg}')
        
        # Check that the button is in the top frame
        top_frame_children = ui_manager.top_frame.winfo_children()
        button_found_in_top = False
        
        for child in top_frame_children:
            # Check if this frame contains our button
            frame_children = child.winfo_children()
            for frame_child in frame_children:
                if frame_child == ui_manager.clear_all_button:
                    button_found_in_top = True
                    break
            if button_found_in_top:
                break
        
        if not button_found_in_top:
            print('[ERROR] Clear All Data button not found in top frame')
            return False
        
        print('[TEST] ✅ Clear All Data button correctly positioned in top frame')
        
        # Clean up
        root.destroy()
        
        print('[TEST] ✅ Top-level Clear All Data button test passed!')
        return True
        
    except Exception as e:
        print(f'[ERROR] Test failed with exception: {e}')
        import traceback
        traceback.print_exc()
        return False


def test_clear_all_data_functionality():
    """Test that the Clear All Data functionality works correctly."""
    print('\n[TEST] Starting Clear All Data functionality test')
    
    try:
        # Create root window
        root = tk.Tk()
        root.withdraw()
        
        # Create UI manager
        ui_manager = UIManager(root)
        
        # Mock the database manager's truncate_all_tables method
        with patch.object(ui_manager.database_manager, 'truncate_all_tables') as mock_truncate:
            mock_truncate.return_value = True
            
            # Mock all the confirmation dialogs to simulate user confirming
            with patch('tkinter.messagebox.askyesno') as mock_askyesno:
                with patch('tkinter.simpledialog.askstring') as mock_askstring:
                    with patch('tkinter.messagebox.showinfo') as mock_showinfo:
                        
                        # Set up mocks to simulate user confirming all dialogs
                        mock_askyesno.return_value = True  # User confirms both warnings
                        mock_askstring.return_value = "Y"  # User types correct confirmation
                        
                        # Call the clear all data method
                        ui_manager.clear_all_data()
                        
                        # Verify that the database truncate method was called with correct confirmation
                        mock_truncate.assert_called_once_with("CONFIRM_TRUNCATE_ALL")
                        
                        # Verify that success message was shown
                        mock_showinfo.assert_called()
                        
                        print('[TEST] ✅ Clear All Data functionality works correctly')
        
        # Test cancellation scenarios
        with patch.object(ui_manager.database_manager, 'truncate_all_tables') as mock_truncate:
            with patch('tkinter.messagebox.askyesno') as mock_askyesno:
                
                # Test user cancelling at first dialog
                mock_askyesno.return_value = False
                ui_manager.clear_all_data()
                
                # Verify truncate was not called
                mock_truncate.assert_not_called()
                
                print('[TEST] ✅ Cancellation handling works correctly')
        
        # Clean up
        root.destroy()
        
        print('[TEST] ✅ Clear All Data functionality test passed!')
        return True
        
    except Exception as e:
        print(f'[ERROR] Test failed with exception: {e}')
        import traceback
        traceback.print_exc()
        return False


def test_database_truncate_function():
    """Test the database truncate_all_tables function."""
    print('\n[TEST] Starting database truncate function test')
    
    try:
        from utils.database_manager import DatabaseManager
        
        # Create database manager
        db_manager = DatabaseManager()
        
        # Test with invalid confirmation
        result = db_manager.truncate_all_tables("INVALID")
        if result:
            print('[ERROR] Truncate should fail with invalid confirmation')
            return False
        
        print('[TEST] ✅ Invalid confirmation properly rejected')
        
        # Test with valid confirmation (but don't actually truncate)
        # We'll mock the database operations to avoid actually clearing data
        with patch('sqlite3.connect') as mock_connect:
            mock_conn = MagicMock()
            mock_cursor = MagicMock()
            mock_connect.return_value.__enter__.return_value = mock_conn
            mock_conn.cursor.return_value = mock_cursor
            
            # Mock table existence checks
            mock_cursor.fetchone.return_value = ('table_exists',)
            mock_cursor.rowcount = 5  # Simulate 5 rows deleted
            
            result = db_manager.truncate_all_tables("CONFIRM_TRUNCATE_ALL")
            
            if not result:
                print('[ERROR] Truncate should succeed with valid confirmation')
                return False
            
            print('[TEST] ✅ Valid confirmation accepted and truncate executed')
        
        print('[TEST] ✅ Database truncate function test passed!')
        return True
        
    except Exception as e:
        print(f'[ERROR] Test failed with exception: {e}')
        import traceback
        traceback.print_exc()
        return False


if __name__ == '__main__':
    print('=' * 70)
    print('CLEAR ALL DATA BUTTON CHANGES TEST')
    print('Testing button rename and new functionality')
    print('=' * 70)
    
    # Run tests
    test1_passed = test_more_tab_button_renamed()
    test2_passed = test_top_level_clear_all_button()
    test3_passed = test_clear_all_data_functionality()
    test4_passed = test_database_truncate_function()
    
    print('=' * 70)
    print('TEST RESULTS:')
    print(f'  More Tab Button Renamed: {"PASSED" if test1_passed else "FAILED"}')
    print(f'  Top-Level Clear All Button: {"PASSED" if test2_passed else "FAILED"}')
    print(f'  Clear All Data Functionality: {"PASSED" if test3_passed else "FAILED"}')
    print(f'  Database Truncate Function: {"PASSED" if test4_passed else "FAILED"}')
    
    if test1_passed and test2_passed and test3_passed and test4_passed:
        print('  OVERALL: ALL TESTS PASSED ✅')
        print('\n🎯 CHANGES SUMMARY:')
        print('  • More tab button renamed to "Clear Bus Case/Comp"')
        print('  • New "Clear All Data" button added to top toolbar')
        print('  • Top-level button truncates ALL database tables')
        print('  • Multiple confirmation dialogs prevent accidental deletion')
        print('  • Simple "Y" confirmation for final step')
        print('  • Comprehensive error handling and user feedback')
        print('\n✨ READY FOR PRODUCTION USE ✨')
        sys.exit(0)
    else:
        print('  OVERALL: SOME TESTS FAILED ❌')
        sys.exit(1)
