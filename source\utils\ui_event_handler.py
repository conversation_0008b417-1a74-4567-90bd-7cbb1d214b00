import tkinter as tk

class UIEventHandler:
    """Handler class for UI events extracted from UIManager"""
    
    def __init__(self, ui_manager):
        print('[DEBUG] UIEventHandler.__init__ called')
        self.ui_manager = ui_manager
    
    def handle_tab_switching(self, tab_index: int) -> bool:
        """
        Switch to a specific tab by index.
        
        Args:
            tab_index: Tab index (0=Clips, 1=More, 2=About)
            
        Returns:
            True if switch was successful, False otherwise
        """
        print(f'[DEBUG] UIEventHandler.handle_tab_switching called with index={tab_index}')
        
        try:
            if hasattr(self.ui_manager, 'tab_control') and self.ui_manager.tab_control:
                # Check if tab index is valid
                if 0 <= tab_index < self.ui_manager.tab_control.index('end'):
                    self.ui_manager.tab_control.select(tab_index)
                    print(f'[DEBUG] Switched to tab index {tab_index}')
                    return True
                else:
                    print(f'[WARNING] Invalid tab index: {tab_index}')
                    return False
            else:
                print('[WARNING] Tab control not available')
                return False
                
        except Exception as e:
            print(f'[ERROR] Failed to switch to tab {tab_index}: {e}')
            return False
    
    def get_current_tab_name(self) -> str:
        """
        Get the name of the currently active tab.
        
        Returns:
            Name of current tab ('Clips', 'More', or 'About')
        """
        try:
            if hasattr(self.ui_manager, 'tab_control') and self.ui_manager.tab_control:
                current_index = self.ui_manager.tab_control.index(self.ui_manager.tab_control.select())
                tab_names = ['Clips', 'More', 'About']
                if 0 <= current_index < len(tab_names):
                    return tab_names[current_index]
            return 'Unknown'
        except Exception as e:
            print(f'[ERROR] Failed to get current tab name: {e}')
            return 'Unknown'
    
    def handle_tree_double_click(self, event):
        """Handle double-click on tree items, especially clip buttons"""
        print('[DEBUG] UIEventHandler.handle_tree_double_click called')
        
        tree = self.ui_manager.tree
        item = tree.identify_row(event.y)
        if not item:
            return
            
        item_values = tree.item(item, 'values')
        item_text = tree.item(item, 'text')
        
        # Check if it's a clip button
        if item_values and item_values[0] == 'Clip Button':
            # Extract alias from the text (remove the 📎 emoji)
            alias = item_text.replace('📎 ', '').strip()
            self._copy_clip_by_alias(alias)
    
    def handle_tree_item_press(self, event):
        """Handle mouse press on tree item for drag initiation using DragDropManager"""
        print('[DEBUG] UIEventHandler.handle_tree_item_press called')
        
        if self.ui_manager.drag_drop_manager:
            self.ui_manager.drag_drop_manager.handle_drag_start(event)
    
    def handle_tree_item_drag(self, event):
        """Handle drag motion using DragDropManager"""
        if self.ui_manager.drag_drop_manager:
            self.ui_manager.drag_drop_manager.handle_drag_motion(event)
    
    def handle_tree_select(self, event):
        """Handle tree selection event"""
        print('[DEBUG] UIEventHandler.handle_tree_select called')
        
        if self.ui_manager.business_logic_manager:
            self.ui_manager.business_logic_manager.handle_tree_item_selection(event)
        else:
            print('[WARNING] BusinessLogicManager not initialized')
    
    def handle_bus_entry_change(self, event=None):
        """Handle business case entry change using BusinessLogicManager"""
        print('[DEBUG] UIEventHandler.handle_bus_entry_change called')
        
        if self.ui_manager.business_logic_manager:
            self.ui_manager.business_logic_manager.handle_business_case_entry_change(event)
        else:
            print('[WARNING] BusinessLogicManager not initialized')
    
    def handle_comp_entry_change(self, event=None):
        """Handle component entry change using BusinessLogicManager"""
        print('[DEBUG] UIEventHandler.handle_comp_entry_change called')
        
        if self.ui_manager.business_logic_manager:
            self.ui_manager.business_logic_manager.handle_component_entry_change(event)
        else:
            print('[WARNING] BusinessLogicManager not initialized')
    
    def handle_tree_selection_changed(self, item, item_type, name):
        """Handle tree selection changes and update entry fields"""
        print(f'[DEBUG] UIEventHandler.handle_tree_selection_changed called: item={item}, type={item_type}, name={name}')
        
        # Only populate business case field when business case is selected
        if item_type == 'Business Case':
            self.ui_manager.bus_entry_var.set(name)
            self.ui_manager.comp_entry_var.set('')  # Clear component field
        elif item_type == 'Component':
            # Set parent business case name
            parent_item = self.ui_manager.tree.parent(item)
            if parent_item:
                parent_name = self.ui_manager.tree.item(parent_item, 'text')
                self.ui_manager.bus_entry_var.set(parent_name)
            # Set component field to selected component's name
            self.ui_manager.comp_entry_var.set(name)
    
    def handle_treeview_select(self, event):
        """Handle treeview selection event"""
        selected = self.ui_manager.tree.selection()
        if selected:
            item = selected[0]
            values = self.ui_manager.tree.item(item, 'values')
            item_type = values[0] if values and len(values) > 0 else None
            name = self.ui_manager.tree.item(item, 'text')
            # Call the handler to update entry fields
            self.handle_tree_selection_changed(item, item_type, name)
        else:
            # Clear fields if nothing is selected
            self.ui_manager.bus_entry_var.set('')
            self.ui_manager.comp_entry_var.set('')
    
    def _copy_clip_by_alias(self, alias):
        """Copy clip content to clipboard by alias - delegates to clipboard utility"""
        print(f'[DEBUG] UIEventHandler._copy_clip_by_alias called with alias={alias}')
        
        # Use the clipboard utility
        if hasattr(self.ui_manager, 'ui_clipboard_utility'):
            self.ui_manager.ui_clipboard_utility.copy_clip_by_alias(alias)
        else:
            # Fallback to original implementation for now
            self.ui_manager._copy_clip_by_alias(alias)

    def handle_tree_selection_changed(self, event):
        """Handle tree selection change event"""
        print('[DEBUG] UIEventHandler.handle_tree_selection_changed called')

        try:
            selected = self.ui_manager.tree.selection()
            if selected:
                item = selected[0]
                values = self.ui_manager.tree.item(item, 'values')
                item_type = values[0] if values and len(values) > 0 else None
                name = self.ui_manager.tree.item(item, 'text')
                # Call the handler to update entry fields
                self.ui_manager.on_tree_selection_changed(item, item_type, name)
            else:
                # Clear fields if nothing is selected
                self.ui_manager.bus_entry_var.set('')
                self.ui_manager.comp_entry_var.set('')

        except Exception as e:
            print(f'[ERROR] Failed to handle tree selection change: {e}')
