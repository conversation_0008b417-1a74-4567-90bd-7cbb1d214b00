#!/usr/bin/env python3
"""
Integration test suite for ClipsMore application
Tests end-to-end workflows and component integration.
"""

import unittest
import sqlite3
import tempfile
import os
import sys
import tkinter as tk
from unittest.mock import Mock, patch

# Add source directory to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from source.ui_manager import UIManager
from source.DB.op_clips_tbl import ClipsTableOperations
from source.DB.op_more_tbl import MoreTableOperations
from source.DB.op_clipsmore_enhanced import ClipsMoreEnhancedOperations
from source.utils.alias_generator import AliasGenerator

# NOTE: All new code should include debug print statements at the start of every function/method.

class TestIntegration(unittest.TestCase):
    """Integration test cases for ClipsMore application."""
    
    def setUp(self):
        """Set up test environment with temporary database."""
        # Create temporary database
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        self.db_path = self.temp_db.name
        
        # Initialize database schema
        self._create_test_schema()
        self._insert_test_data()
        
        # Create Tkinter root for UI tests
        self.root = tk.Tk()
        self.root.withdraw()  # Hide window during tests
        
        # Patch database path for all operations
        self.db_patch = patch.multiple(
            'DB.db_connection',
            DEFAULT_DB_PATH=self.db_path
        )
        self.db_patch.start()
    
    def tearDown(self):
        """Clean up test environment."""
        self.db_patch.stop()
        self.root.destroy()
        try:
            os.unlink(self.db_path)
        except OSError:
            pass
    
    def _create_test_schema(self):
        """Create complete test database schema."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Enable foreign keys
            cursor.execute("PRAGMA foreign_keys = ON")
            
            # Create all tables
            cursor.execute("""
                CREATE TABLE clips_tbl (
                    clip_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    clip BLOB NOT NULL,
                    alias TEXT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            cursor.execute("""
                CREATE TABLE more_bus_tbl (
                    more_bus_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    bus_case TEXT NOT NULL UNIQUE
                )
            """)
            
            cursor.execute("""
                CREATE TABLE more_comp_tbl (
                    more_comp_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    more_bus_id INTEGER NOT NULL,
                    bus_component TEXT NOT NULL,
                    FOREIGN KEY (more_bus_id) REFERENCES more_bus_tbl(more_bus_id) ON DELETE CASCADE
                )
            """)
            
            cursor.execute("""
                CREATE TABLE clipsmore_tbl (
                    transaction_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    clip_id INTEGER NOT NULL,
                    alias TEXT NOT NULL UNIQUE,
                    more_bus_id INTEGER NOT NULL,
                    more_comp_id INTEGER,
                    tree_position INTEGER DEFAULT 0,
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (clip_id) REFERENCES clips_tbl(clip_id) ON DELETE CASCADE,
                    FOREIGN KEY (more_bus_id) REFERENCES more_bus_tbl(more_bus_id) ON DELETE CASCADE,
                    FOREIGN KEY (more_comp_id) REFERENCES more_comp_tbl(more_comp_id) ON DELETE SET NULL
                )
            """)
            
            cursor.execute("""
                CREATE VIEW clipsmore_vw AS
                SELECT 
                    cm.transaction_id,
                    cm.clip_id,
                    cm.alias,
                    cm.tree_position,
                    cm.created_date,
                    cm.modified_date,
                    c.clip as clip_content,
                    c.timestamp as clip_timestamp,
                    mb.bus_case as business_case_name,
                    cm.more_bus_id,
                    mc.bus_component as component_name,
                    cm.more_comp_id
                FROM clipsmore_tbl cm
                JOIN clips_tbl c ON cm.clip_id = c.clip_id
                JOIN more_bus_tbl mb ON cm.more_bus_id = mb.more_bus_id
                LEFT JOIN more_comp_tbl mc ON cm.more_comp_id = mc.more_comp_id
            """)
            
            conn.commit()
    
    def _insert_test_data(self):
        """Insert test data for integration tests - using actual database contents."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # Insert comprehensive cyber security clips for integration testing
            cursor.execute("INSERT INTO clips_tbl (clip_id, clip) VALUES (1, ?)",
                         ("Cybercriminals are getting smarter. Are you?\nA 76-year-old retired lawyer lost his entire life savings to a sophisticated phishing attack.",))
            cursor.execute("INSERT INTO clips_tbl (clip_id, clip) VALUES (2, ?)",
                         ("Imposter scams: They can pose as government agents by phone or email, claiming your accounts are compromised.",))
            cursor.execute("INSERT INTO clips_tbl (clip_id, clip) VALUES (3, ?)",
                         ("Romance scams: These cybercriminals play the long game— building trust over weeks before asking for money.",))
            cursor.execute("INSERT INTO clips_tbl (clip_id, clip) VALUES (4, ?)",
                         ("Call-center scams: If you get a call claiming your computer has problems, hang up immediately. These are fake tech support scams.",))
            cursor.execute("INSERT INTO clips_tbl (clip_id, clip) VALUES (5, ?)",
                         ("Multi-factor authentication (MFA) significantly reduces the risk of unauthorized account access even if passwords are compromised.",))
            cursor.execute("INSERT INTO clips_tbl (clip_id, clip) VALUES (6, ?)",
                         ("Regular security updates and patches are critical for protecting against known vulnerabilities in software and operating systems.",))
            
            # Insert comprehensive cyber security business cases
            cursor.execute("INSERT INTO more_bus_tbl (more_bus_id, bus_case) VALUES (1, ?)", ("Cyber Security",))
            cursor.execute("INSERT INTO more_bus_tbl (more_bus_id, bus_case) VALUES (2, ?)", ("Network Security",))
            cursor.execute("INSERT INTO more_bus_tbl (more_bus_id, bus_case) VALUES (3, ?)", ("Data Protection",))

            # Insert comprehensive components
            cursor.execute("INSERT INTO more_comp_tbl (more_comp_id, more_bus_id, bus_component) VALUES (1, 1, ?)", ("Imposter scams",))
            cursor.execute("INSERT INTO more_comp_tbl (more_comp_id, more_bus_id, bus_component) VALUES (2, 1, ?)", ("Romance scams",))
            cursor.execute("INSERT INTO more_comp_tbl (more_comp_id, more_bus_id, bus_component) VALUES (3, 1, ?)", ("Call-center scams",))
            cursor.execute("INSERT INTO more_comp_tbl (more_comp_id, more_bus_id, bus_component) VALUES (4, 2, ?)", ("Firewall management",))
            cursor.execute("INSERT INTO more_comp_tbl (more_comp_id, more_bus_id, bus_component) VALUES (5, 3, ?)", ("Access controls",))
            cursor.execute("INSERT INTO more_comp_tbl (more_comp_id, more_bus_id, bus_component) VALUES (6, 3, ?)", ("Security updates",))

            # Insert comprehensive clipsmore transactions
            cursor.execute("INSERT INTO clipsmore_tbl (transaction_id, clip_id, alias, more_bus_id, more_comp_id) VALUES (26, 1, 'cybercriminals_intro', 1, NULL)", ())
            cursor.execute("INSERT INTO clipsmore_tbl (transaction_id, clip_id, alias, more_bus_id, more_comp_id) VALUES (27, 2, 'imposter_scams', 1, 1)", ())
            cursor.execute("INSERT INTO clipsmore_tbl (transaction_id, clip_id, alias, more_bus_id, more_comp_id) VALUES (28, 3, 'romance_scams', 1, 2)", ())
            cursor.execute("INSERT INTO clipsmore_tbl (transaction_id, clip_id, alias, more_bus_id, more_comp_id) VALUES (29, 4, 'tech_support_scams', 1, 3)", ())
            cursor.execute("INSERT INTO clipsmore_tbl (transaction_id, clip_id, alias, more_bus_id, more_comp_id) VALUES (30, 5, 'multi_factor_auth', 3, 5)", ())
            cursor.execute("INSERT INTO clipsmore_tbl (transaction_id, clip_id, alias, more_bus_id, more_comp_id) VALUES (31, 6, 'security_updates', 3, 6)", ())
            
            conn.commit()
    
    def test_complete_clip_assignment_workflow(self):
        """Test complete workflow from clip creation to assignment."""
        # Initialize operations
        clips_ops = ClipsTableOperations(self.db_path)
        more_ops = MoreTableOperations(self.db_path)
        enhanced_ops = ClipsMoreEnhancedOperations(self.db_path)
        
        # Create a new clip
        clip_id = clips_ops.create_clip("New test clip for assignment")
        self.assertIsNotNone(clip_id)
        
        # Get business cases
        business_cases = more_ops.read_all_business_cases()
        self.assertGreater(len(business_cases), 0)
        
        # Get components for first business case
        first_bc = business_cases[0]
        components = more_ops.read_components_for_business_case(first_bc['id'])
        
        # Create assignment to business case only
        transaction_id = enhanced_ops.create_assignment(
            clip_id=clip_id,
            more_bus_id=first_bc['id'],
            more_comp_id=None,
            alias="integration_test_alias"
        )
        
        self.assertIsNotNone(transaction_id)
        
        # Verify assignment through view
        assignments = enhanced_ops.get_assignments_by_business_case(first_bc['id'])
        self.assertGreater(len(assignments), 0)
        
        # Find our assignment
        our_assignment = next((a for a in assignments if a['alias'] == 'integration_test_alias'), None)
        self.assertIsNotNone(our_assignment)
        self.assertEqual(our_assignment['clip_id'], clip_id)
    
    def test_alias_generation_integration(self):
        """Test integration between alias generator and database operations."""
        generator = AliasGenerator()
        enhanced_ops = ClipsMoreEnhancedOperations(self.db_path)
        
        # Get existing aliases
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT alias FROM clipsmore_tbl WHERE alias IS NOT NULL")
            existing_aliases = [row[0] for row in cursor.fetchall()]
        
        generator.set_existing_aliases(existing_aliases)
        
        # Test different content types
        test_contents = [
            "https://api.github.com/repos/user/project",
            "def process_data(input_file):",
            "Meeting scheduled for 2024-02-15",
            "Database configuration settings",
            "User authentication module"
        ]
        
        generated_aliases = []
        for content in test_contents:
            alias = generator.generate_from_content(content)
            self.assertIsNotNone(alias)
            self.assertNotIn(alias, generated_aliases)  # Should be unique
            generated_aliases.append(alias)
            
            # Verify alias is valid
            self.assertTrue(generator.validate_alias(alias))
    
    def test_drag_drop_simulation(self):
        """Test drag and drop operation simulation."""
        enhanced_ops = ClipsMoreEnhancedOperations(self.db_path)
        
        # Create initial assignment
        transaction_id = enhanced_ops.create_assignment(
            clip_id=1,
            more_bus_id=1,  # Development
            more_comp_id=1,  # Frontend
            alias="drag_test_alias"
        )
        
        # Simulate drag and drop move to different business case
        success = enhanced_ops.move_assignment(
            transaction_id=transaction_id,
            target_bus_id=2,  # Documentation
            target_comp_id=None
        )
        
        self.assertTrue(success)
        
        # Verify move
        assignments = enhanced_ops.get_assignments_by_business_case(2)
        moved_assignment = next((a for a in assignments if a['alias'] == 'drag_test_alias'), None)
        self.assertIsNotNone(moved_assignment)
        self.assertEqual(moved_assignment['more_bus_id'], 2)
        self.assertIsNone(moved_assignment['more_comp_id'])
    
    def test_copy_operation_with_alias_conflict_resolution(self):
        """Test copy operation with automatic alias conflict resolution."""
        enhanced_ops = ClipsMoreEnhancedOperations(self.db_path)
        
        # Create original assignment
        original_id = enhanced_ops.create_assignment(
            clip_id=1,
            more_bus_id=1,
            alias="copy_test_original"
        )
        
        # Copy to same business case (should generate new alias)
        copy_id = enhanced_ops.copy_assignment(
            transaction_id=original_id,
            target_bus_id=1,
            target_comp_id=1
        )
        
        self.assertIsNotNone(copy_id)
        self.assertNotEqual(copy_id, original_id)
        
        # Verify both assignments exist with different aliases
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT alias FROM clipsmore_tbl 
                WHERE transaction_id IN (?, ?)
                ORDER BY transaction_id
            """, (original_id, copy_id))
            
            aliases = [row[0] for row in cursor.fetchall()]
            self.assertEqual(len(aliases), 2)
            self.assertNotEqual(aliases[0], aliases[1])
            self.assertIn("copy_test_original", aliases[0])
    
    def test_business_case_deletion_cascade(self):
        """Test that deleting business case cascades to assignments."""
        more_ops = MoreTableOperations(self.db_path)
        enhanced_ops = ClipsMoreEnhancedOperations(self.db_path)
        
        # Create new business case
        bc_id = more_ops.create_business_case("Temporary Business Case")
        
        # Create assignment to this business case
        transaction_id = enhanced_ops.create_assignment(
            clip_id=1,
            more_bus_id=bc_id,
            alias="cascade_test"
        )
        
        # Verify assignment exists
        assignments = enhanced_ops.get_assignments_by_business_case(bc_id)
        self.assertEqual(len(assignments), 1)
        
        # Delete business case
        success = more_ops.delete_business_case(bc_id)
        self.assertTrue(success)
        
        # Verify assignment was cascade deleted
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM clipsmore_tbl WHERE transaction_id = ?", (transaction_id,))
            count = cursor.fetchone()[0]
            self.assertEqual(count, 0)
    
    def test_component_deletion_set_null(self):
        """Test that deleting component sets assignments to NULL."""
        more_ops = MoreTableOperations(self.db_path)
        enhanced_ops = ClipsMoreEnhancedOperations(self.db_path)
        
        # Create assignment to component
        transaction_id = enhanced_ops.create_assignment(
            clip_id=1,
            more_bus_id=1,
            more_comp_id=1,  # Frontend component
            alias="component_null_test"
        )
        
        # Delete component
        success = more_ops.delete_component(1)
        self.assertTrue(success)
        
        # Verify assignment still exists but component is NULL
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT more_comp_id FROM clipsmore_tbl 
                WHERE transaction_id = ?
            """, (transaction_id,))
            
            result = cursor.fetchone()
            self.assertIsNotNone(result)
            self.assertIsNone(result[0])  # more_comp_id should be NULL
    
    def test_view_query_performance(self):
        """Test that view queries perform well with multiple assignments."""
        enhanced_ops = ClipsMoreEnhancedOperations(self.db_path)
        
        # Create multiple assignments
        for i in range(10):
            enhanced_ops.create_assignment(
                clip_id=(i % 4) + 1,  # Cycle through available clips
                more_bus_id=(i % 3) + 1,  # Cycle through business cases
                more_comp_id=(i % 4) + 1 if i % 2 == 0 else None,  # Some with components
                alias=f"perf_test_{i}"
            )
        
        # Query view and verify results
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT COUNT(*) FROM clipsmore_vw 
                WHERE business_case_name IS NOT NULL
            """)
            
            count = cursor.fetchone()[0]
            self.assertGreaterEqual(count, 10)
    
    def test_ui_manager_initialization(self):
        """Test UI Manager initialization with test database."""
        # This test verifies UI can initialize with our test data
        with patch('DB.db_connection.DEFAULT_DB_PATH', self.db_path):
            ui_manager = UIManager(self.root)
            
            # Verify UI manager was created
            self.assertIsNotNone(ui_manager)
            self.assertEqual(ui_manager.root, self.root)
            
            # Test that tabs were initialized
            self.assertIsNotNone(ui_manager.clips_tab)
            self.assertIsNotNone(ui_manager.more_tab)
            self.assertIsNotNone(ui_manager.about_tab)
    
    def test_error_handling_integration(self):
        """Test error handling across integrated components."""
        enhanced_ops = ClipsMoreEnhancedOperations(self.db_path)
        
        # Test invalid clip ID
        with self.assertRaises(Exception):
            enhanced_ops.create_assignment(
                clip_id=999,  # Non-existent
                more_bus_id=1,
                alias="error_test"
            )
        
        # Test invalid business case ID
        with self.assertRaises(Exception):
            enhanced_ops.create_assignment(
                clip_id=1,
                more_bus_id=999,  # Non-existent
                alias="error_test2"
            )
        
        # Test duplicate alias
        enhanced_ops.create_assignment(
            clip_id=1,
            more_bus_id=1,
            alias="duplicate_test"
        )
        
        with self.assertRaises(Exception):
            enhanced_ops.create_assignment(
                clip_id=2,
                more_bus_id=1,
                alias="duplicate_test"  # Duplicate
            )

if __name__ == '__main__':
    # Run tests with minimal output for CI/CD
    unittest.main(verbosity=2)
