import unittest
import sqlite3
import tempfile
import os
import sys

# NOTE: All new code should include debug print statements at the start of every function/method.

# Add the project root to sys.path for imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))
from source.DB.op_clips_tbl import ClipsTableOperations
from source.DB.op_more_tbl import MoreTableOperations
from source.DB.op_clipsmore_tbl import ClipsMoreTableOperations, ClipsMoreValidationError, ClipsMoreNotFoundError
from source.DB.db_connection import ConnectionPoolManager

class TestClipsMoreTransactions(unittest.TestCase):
    def setUp(self):
        print('[DEBUG] setUp called')
        self.temp_db = tempfile.NamedTemporaryFile(suffix='.db', delete=False)
        self.db_path = self.temp_db.name
        self.temp_db.close()
        # Set up all tables
        conn = sqlite3.connect(self.db_path)
        cur = conn.cursor()
        cur.execute('PRAGMA foreign_keys = ON;')
        cur.execute("""
            CREATE TABLE IF NOT EXISTS clips_tbl (
                clip_id INTEGER PRIMARY KEY,
                clip TEXT NOT NULL,
                alias TEXT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        cur.execute("""
            CREATE TABLE IF NOT EXISTS more_bus_tbl (
                more_bus_id INTEGER PRIMARY KEY,
                bus_case TEXT NOT NULL
            )
        """)
        cur.execute("""
            CREATE TABLE IF NOT EXISTS more_comp_tbl (
                more_comp_id INTEGER PRIMARY KEY,
                more_bus_id INTEGER NOT NULL,
                bus_component TEXT,
                FOREIGN KEY (more_bus_id) REFERENCES more_bus_tbl (more_bus_id) ON DELETE CASCADE
            )
        """)
        cur.execute("""
            CREATE TABLE IF NOT EXISTS clipsmore_tbl (
                clip_id INTEGER NOT NULL,
                more_bus_id INTEGER NOT NULL,
                more_comp_id INTEGER,
                FOREIGN KEY (clip_id) REFERENCES clips_tbl (clip_id) ON DELETE CASCADE,
                FOREIGN KEY (more_bus_id) REFERENCES more_bus_tbl (more_bus_id) ON DELETE CASCADE,
                FOREIGN KEY (more_comp_id) REFERENCES more_comp_tbl (more_comp_id) ON DELETE SET NULL,
                CONSTRAINT uq_clip_more UNIQUE (clip_id, more_bus_id, more_comp_id)
            )
        """)
        conn.commit()
        conn.close()
        ConnectionPoolManager._instance = None
        self.pool = ConnectionPoolManager(self.db_path)
        self.clips_ops = ClipsTableOperations(self.db_path)
        self.more_ops = MoreTableOperations(self.db_path)
        self.clipsmore_ops = ClipsMoreTableOperations(self.db_path)

    def tearDown(self):
        print('[DEBUG] tearDown called')
        if hasattr(self.pool, 'closeall'):
            self.pool.closeall()
        if hasattr(ConnectionPoolManager, '_instance'):
            ConnectionPoolManager._instance = None
        del self.clips_ops
        del self.more_ops
        del self.clipsmore_ops
        del self.pool
        import gc
        gc.collect()
        os.unlink(self.db_path)

    def test_full_transaction_flow(self):
        print('[DEBUG] test_full_transaction_flow called')
        # Create clips
        clip1_id = self.clips_ops.create_clip({'clip': 'clip1', 'alias': 'a1'})
        clip2_id = self.clips_ops.create_clip({'clip': 'clip2', 'alias': 'a2'})
        # Create business cases
        bus1_id, comp1_id = self.more_ops.create_more('bus1', 'comp1')
        bus2_id, comp2_id = self.more_ops.create_more('bus2', 'comp2')
        # Add another component to bus1
        comp3_id = self.more_ops.add_component(bus1_id, 'comp3')
        # Assign clips to business cases/components
        entry1 = self.clipsmore_ops.create_entry({'clip_id': clip1_id, 'more_bus_id': bus1_id, 'more_comp_id': comp1_id})
        entry2 = self.clipsmore_ops.create_entry({'clip_id': clip2_id, 'more_bus_id': bus2_id, 'more_comp_id': comp2_id})
        entry3 = self.clipsmore_ops.create_entry({'clip_id': clip1_id, 'more_bus_id': bus1_id, 'more_comp_id': comp3_id})
        # Assign clip1 to bus2 without a component
        entry4 = self.clipsmore_ops.create_entry({'clip_id': clip1_id, 'more_bus_id': bus2_id, 'more_comp_id': None})
        # Check assignments
        e1 = self.clipsmore_ops.read_entry(entry1)
        e2 = self.clipsmore_ops.read_entry(entry2)
        e3 = self.clipsmore_ops.read_entry(entry3)
        e4 = self.clipsmore_ops.read_entry(entry4)
        self.assertEqual(e1['clip_id'], clip1_id)
        self.assertEqual(e1['more_bus_id'], bus1_id)
        self.assertEqual(e1['more_comp_id'], comp1_id)
        self.assertEqual(e4['more_bus_id'], bus2_id)
        self.assertIsNone(e4['more_comp_id'])
        # Test unique constraint
        with self.assertRaises(ClipsMoreValidationError):
            self.clipsmore_ops.create_entry({'clip_id': clip1_id, 'more_bus_id': bus2_id, 'more_comp_id': None})
        # Test deletion cascades
        self.clips_ops.delete_clip(clip1_id)
        with self.assertRaises(ClipsMoreNotFoundError):
            self.clipsmore_ops.read_entry(entry1)
        # Test deleting a business case cascades to assignments
        self.more_ops.delete_more(bus2_id)
        with self.assertRaises(ClipsMoreNotFoundError):
            self.clipsmore_ops.read_entry(entry2)

def test_transactions():
    print('[DEBUG] test_transactions called')

if __name__ == "__main__":
    unittest.main()
