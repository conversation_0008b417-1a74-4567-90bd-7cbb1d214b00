import unittest
import sqlite3
from source.DB.op_more_tbl import MoreTableOperations, MoreError, MoreValidationError, MoreNotFoundError
from source.DB.db_connection import ConnectionPoolManager

# NOTE: All new code should include debug print statements at the start of every function/method.

class TestMoreTableOperations(unittest.TestCase):
    def setUp(self):
        self.db_path = ':memory:'
        self.pool = ConnectionPoolManager()
        self.more_ops = MoreTableOperations(self.db_path)
        # Set up minimal tables for testing
        with self.pool.get_connection() as conn:
            cur = conn.cursor()
            cur.execute("PRAGMA foreign_keys = ON;")
            cur.execute("""
                CREATE TABLE IF NOT EXISTS more_bus_tbl (
                    more_bus_id INTEGER PRIMARY KEY,
                    bus_case TEXT NOT NULL
                )
            """)
            cur.execute("""
                CREATE TABLE IF NOT EXISTS more_comp_tbl (
                    more_comp_id INTEGER PRIMARY KEY,
                    more_bus_id INTEGER NOT NULL,
                    bus_component TEXT,
                    FOREIGN KEY (more_bus_id) REFERENCES more_bus_tbl (more_bus_id) ON DELETE CASCADE
                )
            """)
            conn.commit()

    def test_create_and_read_business_case(self):
        print('[DEBUG] test_create_and_read_business_case called')
        bus_id, comp_id = self.more_ops.create_more('Test Business')
        cases = self.more_ops.read_all_business_cases()
        self.assertTrue(any(c['more_bus_id'] == bus_id for c in cases))

    def test_create_business_case_with_component(self):
        print('[DEBUG] test_create_business_case_with_component called')
        bus_id, comp_id = self.more_ops.create_more('Biz', 'Comp')
        comps = self.more_ops.read_components(bus_id)
        self.assertTrue(any(c['more_comp_id'] == comp_id for c in comps))

    def test_update_business_case(self):
        print('[DEBUG] test_update_business_case called')
        bus_id, _ = self.more_ops.create_more('Old Name')
        self.more_ops.update_more(bus_id, bus_case='New Name')
        cases = self.more_ops.read_all_business_cases()
        self.assertTrue(any(c['bus_case'] == 'New Name' for c in cases))

    def test_delete_business_case(self):
        print('[DEBUG] test_delete_business_case called')
        bus_id, _ = self.more_ops.create_more('ToDelete')
        self.more_ops.delete_more(bus_id)
        cases = self.more_ops.read_all_business_cases()
        self.assertFalse(any(c['more_bus_id'] == bus_id for c in cases))

    def test_add_and_delete_component(self):
        print('[DEBUG] test_add_and_delete_component called')
        bus_id, _ = self.more_ops.create_more('Biz2')
        comp_id = self.more_ops.add_component(bus_id, 'Comp2')
        comps = self.more_ops.read_components(bus_id)
        self.assertTrue(any(c['more_comp_id'] == comp_id for c in comps))
        self.more_ops.delete_component(comp_id)
        comps = self.more_ops.read_components(bus_id)
        self.assertFalse(any(c['more_comp_id'] == comp_id for c in comps))

if __name__ == "__main__":
    unittest.main()
