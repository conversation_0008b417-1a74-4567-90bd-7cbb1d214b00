"""
MoreTabManager - Handles More tab initialization and management

This manager centralizes the complex initialization logic for the More tab,
including tree setup, drag & drop, business logic, and search functionality.
"""

import tkinter as tk
from typing import Dict, Any, Optional, Callable


class MoreTabManager:
    """
    Manages More tab initialization and functionality.
    
    This class handles the complex setup of the More tab including
    tree management, drag & drop, business logic, and search features.
    """
    
    def __init__(self, theme_manager, database_manager, validation_manager=None):
        """
        Initialize MoreTabManager.

        Args:
            theme_manager: ThemeManager instance
            database_manager: DatabaseManager instance
            validation_manager: ValidationManager instance for validation operations
        """
        print('[DEBUG] MoreTabManager.__init__ called')
        self.theme_manager = theme_manager
        self.database_manager = database_manager
        self.validation_manager = validation_manager
        self.ui_elements = {}
        self.managers = {}
        self.ui_manager_ref = None  # Will be set during initialization
        
    def initialize_more_tab(self, more_tab, tree_manager_class, drag_drop_manager_class, 
                           business_logic_manager_class, ui_manager_ref):
        """
        Initialize the complete More tab with all its components.
        
        Args:
            more_tab: More tab widget
            tree_manager_class: TreeManager class
            drag_drop_manager_class: DragDropManager class
            business_logic_manager_class: BusinessLogicManager class
            ui_manager_ref: Reference to UIManager instance
            
        Returns:
            Dictionary containing all initialized components
        """
        print('[DEBUG] MoreTabManager.initialize_more_tab called')

        # Store the UI manager reference for callback wiring
        self.ui_manager_ref = ui_manager_ref

        try:
            colors = self.theme_manager.get_theme_colors()
            
            # Create basic UI elements
            self._create_basic_ui(more_tab, colors)
            
            # Initialize TreeManager
            tree_manager = self._initialize_tree_manager(more_tab, tree_manager_class)
            
            # Initialize Drag & Drop Manager
            drag_drop_manager = self._initialize_drag_drop_manager(
                drag_drop_manager_class, more_tab, tree_manager
            )
            
            # Initialize Business Logic Manager
            business_logic_manager = self._initialize_business_logic_manager(
                business_logic_manager_class, tree_manager
            )
            
            # Setup UI component references for business logic
            self._setup_business_logic_ui_components(business_logic_manager)
            
            # Store tree manager reference for search functionality
            self.tree_manager_ref = tree_manager
            
            # Load tree data
            tree_manager.refresh_tree()
            
            # Store managers for external access
            self.managers.update({
                'tree_manager': tree_manager,
                'drag_drop_manager': drag_drop_manager,
                'business_logic_manager': business_logic_manager
            })
            
            print('[DEBUG] More tab initialized successfully')
            
            return {
                'tree_manager': tree_manager,
                'drag_drop_manager': drag_drop_manager,
                'business_logic_manager': business_logic_manager,
                'tree': self.ui_elements.get('tree'),
                'ui_elements': self.ui_elements.copy()
            }
            
        except Exception as e:
            print(f'[ERROR] Failed to initialize more tab: {e}')
            raise
            
    def _create_basic_ui(self, more_tab, colors):
        """Create basic UI elements for the More tab."""
        print('[DEBUG] MoreTabManager._create_basic_ui called')

        # Header frame with title and Clear Manager button (similar to Clips tab)
        header_frame = tk.Frame(more_tab, bg=colors['bg_color'])
        header_frame.pack(fill='x', padx=10, pady=5)

        # Main title label
        title_label = tk.Label(
            header_frame,
            text="📊 Business Case Manager",
            bg=colors['bg_color'],
            fg=colors['fg_color'],
            font=('Arial', 14, 'bold')
        )
        title_label.pack(side=tk.LEFT)

        # Clear Manager button (positioned like Clear All Clips button)
        clear_manager_btn = tk.Button(
            header_frame,
            text="Clear Manager",
            command=lambda: self._placeholder_callback("clear_all_more_data"),
            bg="#d32f2f",
            fg="white",
            activebackground="#b71c1c",
            activeforeground="white",
            font=('Arial', 9, 'bold')
        )
        clear_manager_btn.pack(side=tk.RIGHT, padx=5)

        # Search frame (moved to top)
        search_frame = tk.Frame(more_tab, bg=colors['bg_color'])
        search_frame.pack(fill='x', padx=10, pady=5)

        # Search entry
        search_var = tk.StringVar()
        search_entry = tk.Entry(
            search_frame,
            textvariable=search_var,
            width=30,
            bg=colors['entry_bg'],
            fg=colors['entry_fg'],
            insertbackground=colors['entry_fg']
        )
        search_entry.pack(side=tk.LEFT, padx=5)

        # Search button
        search_btn = tk.Button(
            search_frame,
            text="Search",
            command=lambda: self._search_callback(search_var.get()),
            bg=colors['button_bg'],
            fg=colors['button_fg'],
            activebackground=colors['tree_select'],
            activeforeground=colors['fg_color']
        )
        search_btn.pack(side=tk.LEFT, padx=5)

        # Store search elements for later use
        self.ui_elements['search_var'] = search_var
        self.ui_elements['search_entry'] = search_entry
        self.ui_elements['search_frame'] = search_frame
        self.ui_elements['header_frame'] = header_frame

        # Inline frame for business case/component UI
        inline_frame = tk.Frame(more_tab, bg=colors['bg_color'])
        inline_frame.pack(pady=5)
        self.ui_elements['inline_frame'] = inline_frame

        # Create business case entry elements
        self._create_business_case_entry(inline_frame, colors)

        # Create component entry elements
        self._create_component_entry(inline_frame, colors)

        # Create error label
        more_error_label = tk.Label(more_tab, text="", fg="red", bg=colors['bg_color'])
        more_error_label.pack()
        self.ui_elements['more_error_label'] = more_error_label
        
    def _create_business_case_entry(self, parent, colors):
        """Create business case entry elements."""
        print('[DEBUG] MoreTabManager._create_business_case_entry called')
        
        # Business case label
        tk.Label(
            parent, 
            text="Business Case:", 
            bg=colors['bg_color'], 
            fg=colors['fg_color']
        ).pack(side=tk.LEFT, padx=2)
        
        # Business case entry
        bus_entry_var = tk.StringVar()
        bus_entry = tk.Entry(
            parent, 
            textvariable=bus_entry_var, 
            width=22,
            bg=colors['entry_bg'], 
            fg=colors['entry_fg'], 
            insertbackground=colors['entry_fg']
        )
        bus_entry.pack(side=tk.LEFT, padx=2)
        
        # Add trace for entry length limit
        bus_entry_var.trace_add('write', lambda *a: self._limit_entry(bus_entry_var, 20))
        
        # Add business case button
        tk.Button(
            parent, 
            text="Add Business Case", 
            command=lambda: self._placeholder_callback("add_business_case"),
            bg=colors['button_bg'], 
            fg=colors['button_fg'], 
            activebackground=colors['tree_select'],
            activeforeground=colors['fg_color']
        ).pack(side=tk.LEFT, padx=5)
        
        self.ui_elements['bus_entry_var'] = bus_entry_var
        self.ui_elements['bus_entry'] = bus_entry
        
    def _create_component_entry(self, parent, colors):
        """Create component entry elements."""
        print('[DEBUG] MoreTabManager._create_component_entry called')
        
        # Component label
        tk.Label(
            parent, 
            text="Component:", 
            bg=colors['bg_color'], 
            fg=colors['fg_color']
        ).pack(side=tk.LEFT, padx=2)
        
        # Component entry
        comp_entry_var = tk.StringVar()
        comp_entry = tk.Entry(
            parent, 
            textvariable=comp_entry_var, 
            width=22,
            bg=colors['entry_bg'], 
            fg=colors['entry_fg'], 
            insertbackground=colors['entry_fg']
        )
        comp_entry.pack(side=tk.LEFT, padx=2)
        
        # Add trace for entry length limit
        comp_entry_var.trace_add('write', lambda *a: self._limit_entry(comp_entry_var, 20))
        
        # CUD Component button
        add_component_btn = tk.Button(
            parent, 
            text="CUD Component", 
            command=lambda: self._placeholder_callback("cud_component"),
            bg=colors['button_bg'], 
            fg=colors['button_fg'], 
            activebackground=colors['tree_select'],
            activeforeground=colors['fg_color']
        )
        add_component_btn.pack(side=tk.LEFT, padx=5)
        
        self.ui_elements['comp_entry_var'] = comp_entry_var
        self.ui_elements['comp_entry'] = comp_entry
        self.ui_elements['add_component_btn'] = add_component_btn
        
    def _initialize_tree_manager(self, more_tab, tree_manager_class):
        """Initialize TreeManager."""
        print('[DEBUG] MoreTabManager._initialize_tree_manager called')
        
        tree_manager = tree_manager_class(
            parent=more_tab,
            theme_manager=self.theme_manager,
            database_manager=self.database_manager
        )
        
        # Create tree interface
        tree, tree_frame = tree_manager.create_tree_interface()
        tree_frame.pack(expand=True, fill='both', padx=5, pady=5)
        
        self.ui_elements['tree'] = tree
        self.ui_elements['tree_frame'] = tree_frame
        
        return tree_manager
        
    def _initialize_drag_drop_manager(self, drag_drop_manager_class, more_tab, tree_manager):
        """Initialize DragDropManager."""
        print('[DEBUG] MoreTabManager._initialize_drag_drop_manager called')
        
        drag_drop_manager = drag_drop_manager_class(
            tree_widget=self.ui_elements['tree'],
            more_tab=more_tab,
            theme_manager=self.theme_manager,
            database_manager=self.database_manager
        )
        
        # Set tree manager reference for refresh operations
        drag_drop_manager.set_tree_manager(tree_manager)
        
        return drag_drop_manager
        
    def _initialize_business_logic_manager(self, business_logic_manager_class, tree_manager):
        """Initialize BusinessLogicManager."""
        print('[DEBUG] MoreTabManager._initialize_business_logic_manager called')
        
        business_logic_manager = business_logic_manager_class(
            database_manager=self.database_manager,
            tree_manager=tree_manager,
            theme_manager=self.theme_manager,
            validation_manager=self.validation_manager
        )
        
        return business_logic_manager
        
    def _setup_business_logic_ui_components(self, business_logic_manager):
        """Setup UI component references for business logic operations."""
        print('[DEBUG] MoreTabManager._setup_business_logic_ui_components called')
        
        ui_components = {
            'tree': self.ui_elements['tree'],
            'bus_entry_var': self.ui_elements['bus_entry_var'],
            'comp_entry_var': self.ui_elements['comp_entry_var'],
            'more_error_label': self.ui_elements['more_error_label'],
            'add_component_btn': self.ui_elements['add_component_btn']
        }
        business_logic_manager.set_ui_components(ui_components)
        
    def _search_callback(self, search_term):
        """Handle search functionality."""
        print(f'[DEBUG] MoreTabManager._search_callback called with term: {search_term}')

        if hasattr(self, 'tree_manager_ref') and self.tree_manager_ref:
            self.tree_manager_ref.filter_tree(search_term)
        else:
            print('[WARNING] Tree manager reference not available for search')
        
    def _limit_entry(self, var, maxlen):
        """Limit entry length using UtilityManager."""
        from source.utils.utility_manager import UtilityManager
        UtilityManager.limit_entry_length(var, maxlen)
        
    def _placeholder_callback(self, action_name):
        """Callback that delegates to UIManager methods."""
        print(f'[DEBUG] MoreTabManager placeholder callback: {action_name}')

        try:
            if self.ui_manager_ref and hasattr(self.ui_manager_ref, action_name):
                method = getattr(self.ui_manager_ref, action_name)
                method()
                print(f'[DEBUG] Successfully called UIManager.{action_name}()')
            else:
                print(f'[WARNING] UIManager method {action_name} not found or UIManager not available')

        except Exception as e:
            print(f'[ERROR] Failed to call UIManager.{action_name}(): {e}')
        
    def get_manager(self, manager_name: str) -> Optional[Any]:
        """
        Get a manager by name.
        
        Args:
            manager_name: Name of the manager
            
        Returns:
            Manager instance or None if not found
        """
        return self.managers.get(manager_name)
        
    def get_ui_element(self, element_name: str) -> Optional[Any]:
        """
        Get a UI element by name.
        
        Args:
            element_name: Name of the UI element
            
        Returns:
            UI element or None if not found
        """
        return self.ui_elements.get(element_name)
