"""
Business Logic Manager for ClipsMore Application

This manager handles all business logic operations including:
- Business case creation, update, and deletion
- Component CRUD operations
- Business rule validation
- Data consistency management
- Tree item interactions
- Entry change handling

Author: ClipsMore Development Team
Version: 2.0
"""

import tkinter as tk
from typing import Dict, Any, Optional, Tuple
from tkinter import messagebox

# NOTE: All new code should include debug print statements at the start of every function/method.

class BusinessLogicManager:
    """
    Manages business logic operations for the ClipsMore application.
    
    Handles:
    - Business case operations (create, update, delete)
    - Component CRUD operations
    - Business rule validation
    - Data consistency management
    - Tree item interactions
    - Entry change handling
    """
    
    def __init__(self, database_manager, tree_manager, theme_manager, validation_manager=None):
        """
        Initialize BusinessLogicManager with required components.

        Args:
            database_manager: DatabaseManager instance for data operations
            tree_manager: TreeManager instance for tree operations
            theme_manager: ThemeManager instance for styling
            validation_manager: ValidationManager instance for validation operations
        """
        print('[DEBUG] BusinessLogicManager.__init__ called')

        self.database_manager = database_manager
        self.tree_manager = tree_manager
        self.theme_manager = theme_manager
        self.validation_manager = validation_manager
        self.notification_manager = None  # Will be set by UIManager

        # UI component references (will be set by UIManager)
        self.tree = None
        self.bus_entry_var = None
        self.comp_entry_var = None
        self.more_error_label = None
        self.add_component_btn = None
        
        print('[DEBUG] BusinessLogicManager initialized successfully')

    def set_notification_manager(self, notification_manager):
        """Set the notification manager for this business logic manager."""
        print('[DEBUG] BusinessLogicManager.set_notification_manager called')
        self.notification_manager = notification_manager

    def set_ui_components(self, ui_components: Dict[str, Any]):
        """
        Set UI component references for business logic operations.
        
        Args:
            ui_components: Dictionary containing UI component references
        """
        print('[DEBUG] BusinessLogicManager.set_ui_components called')
        
        self.tree = ui_components.get('tree')
        self.bus_entry_var = ui_components.get('bus_entry_var')
        self.comp_entry_var = ui_components.get('comp_entry_var')
        self.more_error_label = ui_components.get('more_error_label')
        self.add_component_btn = ui_components.get('add_component_btn')
        
        print('[DEBUG] UI components set successfully')
    
    def add_business_case(self) -> bool:
        """
        Add a new business case.
        
        Returns:
            bool: True if successful, False otherwise
        """
        print('[DEBUG] BusinessLogicManager.add_business_case called')
        
        if not self.bus_entry_var:
            print('[ERROR] Bus entry var not available')
            return False
        
        name = self.bus_entry_var.get().strip()
        if not name:
            self._set_error_message("Business case name required.")
            print('[ERROR] Business case name required.')
            return False
        
        try:
            more_ops = self.database_manager.get_more_operations()
            more_ops.create_more(name)
            self._refresh_tree()
            self.bus_entry_var.set("")
            self._clear_error_message()
            print(f'[DEBUG] Business case created successfully: {name}')
            return True
            
        except Exception as e:
            user_friendly_msg = self.database_manager.handle_database_error(e, "create business case", self.notification_manager)
            self._set_error_message(user_friendly_msg)
            return False
    
    def handle_component_operations(self) -> bool:
        """
        Handle component Create, Update, Delete operations.
        
        Returns:
            bool: True if successful, False otherwise
        """
        print('[DEBUG] BusinessLogicManager.handle_component_operations called')
        
        if not self.tree or not self.comp_entry_var:
            print('[ERROR] Required UI components not available')
            return False
        
        selected = self.tree.focus()
        name = self.comp_entry_var.get().strip()
        
        # Handle empty name case for deletion
        if not name and selected:
            return self._handle_component_deletion_by_empty_name(selected)
        elif not name:
            self._set_error_message("Component name required.")
            print('[ERROR] Component name required.')
            return False
        
        try:
            more_ops = self.database_manager.get_more_operations()
            
            if not selected:
                self._set_error_message("Select a business case or component.")
                print('[ERROR] No item selected.')
                return False
            
            item_type = self.tree.item(selected, 'values')[0] if self.tree.item(selected, 'values') else None
            
            if item_type == 'Business Case':
                return self._create_component(selected, name, more_ops)
            elif item_type == 'Component':
                return self._update_or_delete_component(selected, name, more_ops)
            else:
                self._set_error_message("Select a business case or component.")
                print('[ERROR] Invalid item type selected.')
                return False
                
        except Exception as e:
            self._set_error_message(str(e))
            print(f'[ERROR] Component operation failed: {e}')
            return False
    
    def _handle_component_deletion_by_empty_name(self, selected: str) -> bool:
        """Handle component deletion when name field is empty."""
        print('[DEBUG] BusinessLogicManager._handle_component_deletion_by_empty_name called')
        
        item_type = self.tree.item(selected, 'values')[0] if self.tree.item(selected, 'values') else None
        if item_type == 'Component':
            # DELETE: Empty text box means delete the component
            old_name = self.tree.item(selected, 'text')
            parent_item = self.tree.parent(selected)
            
            if parent_item:
                parent_name = self.tree.item(parent_item, 'text')
                bus_id = self.get_business_case_id_by_name(parent_name)
                comp_id = self.get_component_id_by_name_in_business_case(old_name, bus_id)
                if comp_id:
                    try:
                        more_ops = self.database_manager.get_more_operations()
                        more_ops.delete_component(comp_id)
                        print(f'[DEBUG] Deleted component: {old_name} from business case: {parent_name}')
                        self._refresh_tree()
                        self._clear_error_message()
                        return True
                    except Exception as e:
                        self._set_error_message(str(e))
                        print(f'[ERROR] Failed to delete component: {e}')
                        return False
        else:
            self._set_error_message("Component name required.")
            print('[ERROR] Component name required.')
            return False
        
        return False
    
    def _create_component(self, selected: str, name: str, more_ops) -> bool:
        """Create a new component in the selected business case."""
        print('[DEBUG] BusinessLogicManager._create_component called')
        
        # CREATE: Add new component to selected business case
        bus_name = self.tree.item(selected, 'text')
        bus_id = self.get_business_case_id_by_name(bus_name)
        more_ops.add_component(bus_id, name)
        print(f'[DEBUG] Created component: {name} in business case: {bus_name}')
        self.comp_entry_var.set("")
        self._refresh_tree()
        self._clear_error_message()
        return True
    
    def _update_or_delete_component(self, selected: str, name: str, more_ops) -> bool:
        """Update or delete an existing component."""
        print('[DEBUG] BusinessLogicManager._update_or_delete_component called')
        
        # UPDATE or DELETE: Handle existing component
        old_name = self.tree.item(selected, 'text')
        parent_item = self.tree.parent(selected)
        
        if name.lower() == 'delete' or name.lower() == 'del':
            # DELETE: Delete the component
            if parent_item:
                parent_name = self.tree.item(parent_item, 'text')
                bus_id = self.get_business_case_id_by_name(parent_name)
                comp_id = self.get_component_id_by_name_in_business_case(old_name, bus_id)
                if comp_id:
                    more_ops.delete_component(comp_id)
                    print(f'[DEBUG] Deleted component: {old_name} from business case: {parent_name}')
                    self.comp_entry_var.set("")
                else:
                    raise Exception(f"Component '{old_name}' not found")
            else:
                raise Exception("Component has no parent business case")
        
        elif name != old_name:
            # UPDATE: Update component name
            if parent_item:
                parent_name = self.tree.item(parent_item, 'text')
                bus_id = self.get_business_case_id_by_name(parent_name)
                comp_id = self.get_component_id_by_name_in_business_case(old_name, bus_id)
                if comp_id:
                    more_ops.update_more(bus_id, component_updates={comp_id: name})
                    print(f'[DEBUG] Updated component: {old_name} -> {name} in business case: {parent_name}')
                else:
                    raise Exception(f"Component '{old_name}' not found")
            else:
                raise Exception("Component has no parent business case")
        else:
            # No change needed
            print('[DEBUG] No change needed for component')
            return True
        
        self._refresh_tree()
        self._clear_error_message()
        return True
    
    def edit_selected_item(self) -> bool:
        """
        Edit the selected business case or component.
        
        Returns:
            bool: True if successful, False otherwise
        """
        print('[DEBUG] BusinessLogicManager.edit_selected_item called')
        
        if not self.tree:
            print('[ERROR] Tree not available')
            return False
        
        selected = self.tree.focus()
        if not selected:
            if self.notification_manager:
                self.notification_manager.show_info("Select a business case or component to edit.")
            else:
                messagebox.showinfo("Select Item", "Select a business case or component to edit.")
            return False
        
        item_type = self.tree.item(selected, 'values')[0]
        name = self.tree.item(selected, 'text')
        
        def save():
            print('[DEBUG] save called')
            new_name = entry.get().strip()
            if new_name:
                try:
                    more_ops = self.database_manager.get_more_operations()
                    if item_type == 'Business Case':
                        bus_id = self.get_business_case_id_by_name(name)
                        more_ops.update_more(bus_id, bus_case=new_name)
                    else:
                        comp_id = self.get_component_id_by_name(name)
                        more_ops.update_more(self.get_business_case_id_of_component(comp_id), 
                                           component_updates={comp_id: new_name})
                    self._refresh_tree()
                    dialog.destroy()
                    return True
                except Exception as e:
                    if self.notification_manager:
                        self.notification_manager.show_error(str(e))
                    else:
                        messagebox.showerror("Error", str(e))
                    print(f'[ERROR] Failed to save edit: {e}')
                    return False
        
        # Create edit dialog
        dialog = tk.Toplevel()
        dialog.title(f"Edit {item_type}")
        tk.Label(dialog, text=f"New {item_type} Name:").pack(padx=10, pady=5)
        entry = tk.Entry(dialog)
        entry.insert(0, name)
        entry.pack(padx=10, pady=5)
        tk.Button(dialog, text="Save", command=save).pack(pady=10)
        
        return True
    
    def delete_selected_item(self) -> bool:
        """
        Delete the selected business case or component.
        
        Returns:
            bool: True if successful, False otherwise
        """
        print('[DEBUG] BusinessLogicManager.delete_selected_item called')
        
        if not self.tree:
            print('[ERROR] Tree not available')
            return False
        
        selected = self.tree.focus()
        if not selected:
            if self.notification_manager:
                self.notification_manager.show_info("Select a business case or component to delete.")
            else:
                messagebox.showinfo("Select Item", "Select a business case or component to delete.")
            return False
        
        item_type = self.tree.item(selected, 'values')[0]
        name = self.tree.item(selected, 'text')
        
        # Keep messagebox for user confirmation as notifications don't support user input
        if messagebox.askyesno("Confirm Delete", f"Delete {item_type} '{name}'?"):
            try:
                more_ops = self.database_manager.get_more_operations()
                if item_type == 'Business Case':
                    bus_id = self.get_business_case_id_by_name(name)
                    more_ops.delete_more(bus_id)
                else:
                    comp_id = self.get_component_id_by_name(name)
                    more_ops.delete_component(comp_id)
                self._refresh_tree()
                return True
            except Exception as e:
                if self.notification_manager:
                    self.notification_manager.show_error(str(e))
                else:
                    messagebox.showerror("Error", str(e))
                print(f'[ERROR] Failed to delete item: {e}')
                return False
        
        return False

    def handle_business_case_entry_change(self, event=None) -> bool:
        """
        Handle business case entry field changes.

        Args:
            event: Optional event parameter

        Returns:
            bool: True if successful, False otherwise
        """
        print('[DEBUG] BusinessLogicManager.handle_business_case_entry_change called')

        if not self.tree or not self.bus_entry_var:
            print('[ERROR] Required UI components not available')
            return False

        selected = self.tree.focus()
        if not selected:
            print('[DEBUG] No tree item selected for business case update')
            return False

        item_type = self.tree.item(selected, 'values')[0] if self.tree.item(selected, 'values') else None
        if item_type != 'Business Case':
            print('[DEBUG] Selected item is not a business case')
            return False

        new_name = self.bus_entry_var.get().strip()
        old_name = self.tree.item(selected, 'text')

        if new_name and new_name != old_name:
            try:
                from source.DB.op_more_tbl import MoreTableOperations
                ops = MoreTableOperations()
                bus_id = self.get_business_case_id_by_name(old_name)
                ops.update_more(bus_id, bus_case=new_name)
                self._refresh_tree()
                print(f'[DEBUG] Business case updated: {old_name} -> {new_name}')
                self._clear_error_message()
                return True
            except Exception as e:
                self._set_error_message(str(e))
                print(f'[ERROR] Failed to update business case: {e}')
                return False

        return True

    def handle_component_entry_change(self, event=None) -> bool:
        """
        Handle component entry field changes.

        Args:
            event: Optional event parameter

        Returns:
            bool: True if successful, False otherwise
        """
        print('[DEBUG] BusinessLogicManager.handle_component_entry_change called')

        if not self.tree or not self.comp_entry_var:
            print('[ERROR] Required UI components not available')
            return False

        selected = self.tree.focus()
        if not selected:
            print('[DEBUG] No tree item selected for component update')
            return False

        item_type = self.tree.item(selected, 'values')[0] if self.tree.item(selected, 'values') else None
        if item_type != 'Component':
            print('[DEBUG] Selected item is not a component')
            return False

        new_name = self.comp_entry_var.get().strip()
        old_name = self.tree.item(selected, 'text')
        print(f'[DEBUG] Component name change detected: "{old_name}" -> "{new_name}"')

        if new_name and new_name != old_name:
            try:
                from source.DB.op_more_tbl import MoreTableOperations
                ops = MoreTableOperations()

                # Get the parent business case to make component lookup more specific
                parent_item = self.tree.parent(selected)
                if parent_item:
                    parent_name = self.tree.item(parent_item, 'text')
                    bus_id = self.get_business_case_id_by_name(parent_name)
                    if bus_id:
                        # Get component ID within the specific business case
                        comp_id = self.get_component_id_by_name_in_business_case(old_name, bus_id)
                        if comp_id:
                            ops.update_more(bus_id, component_updates={comp_id: new_name})
                            self._refresh_tree()
                            print(f'[DEBUG] Component updated: {old_name} -> {new_name} in business case: {parent_name}')
                            self._clear_error_message()
                            return True
                        else:
                            raise Exception(f"Component '{old_name}' not found in business case '{parent_name}'")
                    else:
                        raise Exception(f"Business case '{parent_name}' not found")
                else:
                    raise Exception("Component has no parent business case")
            except Exception as e:
                self._set_error_message(str(e))
                print(f'[ERROR] Failed to update component: {e}')
                return False

        return True

    def handle_tree_item_selection(self, event) -> bool:
        """
        Handle tree item selection and update UI accordingly.

        Args:
            event: Tree selection event

        Returns:
            bool: True if successful, False otherwise
        """
        print('[DEBUG] BusinessLogicManager.handle_tree_item_selection called')

        if not self.tree:
            print('[ERROR] Tree not available')
            return False

        # Get the item under the cursor
        item = self.tree.identify_row(event.y)
        if item:
            item_type = self.tree.item(item, 'values')[0] if self.tree.item(item, 'values') else None
            name = self.tree.item(item, 'text')

            if item_type == 'Business Case':
                if self.bus_entry_var:
                    self.bus_entry_var.set(name)
                if self.comp_entry_var:
                    self.comp_entry_var.set("")  # Clear component entry when business case is selected
                if self.add_component_btn:
                    self.add_component_btn.config(state=tk.NORMAL)
            elif item_type == 'Component':
                # DO NOT auto-populate component field - let user type for CUD operations
                # The component field should remain empty for user input
                if self.add_component_btn:
                    self.add_component_btn.config(state=tk.NORMAL)  # Keep button enabled for CUD operations
            else:
                if self.add_component_btn:
                    self.add_component_btn.config(state=tk.NORMAL)
        else:
            if self.add_component_btn:
                self.add_component_btn.config(state=tk.NORMAL)

        return True

    def get_business_case_id_by_name(self, name: str) -> Optional[int]:
        """Get business case ID by name - delegates to DatabaseManager"""
        print(f'[DEBUG] BusinessLogicManager.get_business_case_id_by_name called with name={name}')
        return self.database_manager.get_business_case_id_by_name(name)

    def get_component_id_by_name(self, name: str) -> Optional[int]:
        """Get component ID by name - delegates to DatabaseManager"""
        print(f'[DEBUG] BusinessLogicManager.get_component_id_by_name called with name={name}')
        return self.database_manager.get_component_id_by_name(name)

    def get_business_case_id_of_component(self, comp_id: int) -> Optional[int]:
        """Get business case ID of component - delegates to DatabaseManager"""
        print(f'[DEBUG] BusinessLogicManager.get_business_case_id_of_component called with comp_id={comp_id}')
        return self.database_manager.get_business_case_id_of_component(comp_id)

    def get_component_id_by_name_in_business_case(self, comp_name: str, bus_id: int) -> Optional[int]:
        """
        Get component ID by name within a specific business case.

        Args:
            comp_name: Component name
            bus_id: Business case ID

        Returns:
            Component ID or None if not found
        """
        print(f'[DEBUG] BusinessLogicManager.get_component_id_by_name_in_business_case called with comp_name={comp_name}, bus_id={bus_id}')

        try:
            comp_id = self.database_manager.get_component_id_by_name_in_business_case(comp_name, bus_id)
            print(f'[DEBUG] Found component id: {comp_id} for name: {comp_name} in business case: {bus_id}')
            return comp_id
        except Exception as e:
            print(f'[ERROR] get_component_id_by_name_in_business_case: {e}')
            self._set_error_message(f"Error finding component id: {e}")
            return None

    def validate_business_rules(self, operation: str, data: Dict[str, Any]) -> Tuple[bool, str]:
        """
        Validate business rules for operations.

        Args:
            operation: Type of operation (create, update, delete)
            data: Operation data

        Returns:
            Tuple of (is_valid, error_message)
        """
        print(f'[DEBUG] BusinessLogicManager.validate_business_rules called for operation: {operation}')

        try:
            # Use centralized validation through ValidationManager
            if self.validation_manager:
                return self.validation_manager.validate_operation_data(operation, data)
            else:
                # Fallback to basic validation if ValidationManager not available
                print('[WARNING] ValidationManager not available, using fallback validation')

                if operation in ['create_business_case', 'update_business_case']:
                    name = data.get('name', '').strip()
                    if not name:
                        return False, "Business case name is required"
                    if len(name) > 100:
                        return False, "Business case name too long (max 100 characters)"

                elif operation in ['create_component', 'update_component']:
                    name = data.get('name', '').strip()
                    if not name:
                        return False, "Component name is required"
                    if len(name) > 100:
                        return False, "Component name too long (max 100 characters)"
                    if operation == 'create_component' and not data.get('bus_id'):
                        return False, "Business case is required for component"

                return True, ""

        except Exception as e:
            print(f'[ERROR] Business rule validation failed: {e}')
            return False, f"Validation error: {e}"

    def _refresh_tree(self):
        """Refresh the tree display."""
        print('[DEBUG] BusinessLogicManager._refresh_tree called')

        if self.tree_manager:
            self.tree_manager.refresh_tree()
        else:
            print('[WARNING] TreeManager not available')

    def _set_error_message(self, message: str):
        """Set error message in the UI."""
        print(f'[DEBUG] BusinessLogicManager._set_error_message called: {message}')

        if self.more_error_label:
            self.more_error_label.config(text=message)

    def _clear_error_message(self):
        """Clear error message in the UI."""
        print('[DEBUG] BusinessLogicManager._clear_error_message called')

        if self.more_error_label:
            self.more_error_label.config(text="")

# NOTE: All new code should include debug print statements at the start of every function/method.


