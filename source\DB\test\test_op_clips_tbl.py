import unittest
import sqlite3
from source.DB.op_clips_tbl import ClipsTableOperations, ClipError, ClipNotFoundError
from source.DB.db_connection import ConnectionPoolManager

# NOTE: All new code should include debug print statements at the start of every function/method.

class TestClipsTableOperations(unittest.TestCase):
    def setUp(self):
        self.db_path = ':memory:'
        self.pool = ConnectionPoolManager()
        self.clips_ops = ClipsTableOperations(self.db_path)
        # Set up a minimal clips_tbl for testing
        with self.pool.get_connection() as conn:
            cur = conn.cursor()
            cur.execute("""
                CREATE TABLE IF NOT EXISTS clips_tbl (
                    clip_id INTEGER PRIMARY KEY,
                    clip TEXT NOT NULL,
                    alias TEXT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            conn.commit()

    def test_create_and_read_clip(self):
        print('[DEBUG] test_create_and_read_clip called')
        data = {'clip': 'test content', 'alias': 'test'}
        clip_id = self.clips_ops.create_clip(data)
        clip = self.clips_ops.read_clip(clip_id)
        self.assertEqual(clip['clip'], 'test content')
        self.assertEqual(clip['alias'], 'test')

    def test_read_nonexistent_clip_raises(self):
        print('[DEBUG] test_read_nonexistent_clip_raises called')
        with self.assertRaises(ClipNotFoundError):
            self.clips_ops.read_clip(9999)

    def test_update_clip(self):
        print('[DEBUG] test_update_clip called')
        data = {'clip': 'original', 'alias': 'orig'}
        clip_id = self.clips_ops.create_clip(data)
        self.clips_ops.update_clip(clip_id, clip='updated', alias='new')
        updated = self.clips_ops.read_clip(clip_id)
        self.assertEqual(updated['clip'], 'updated')
        self.assertEqual(updated['alias'], 'new')

    def test_delete_clip(self):
        print('[DEBUG] test_delete_clip called')
        data = {'clip': 'to delete', 'alias': 'del'}
        clip_id = self.clips_ops.create_clip(data)
        self.clips_ops.delete_clip(clip_id)
        with self.assertRaises(ClipNotFoundError):
            self.clips_ops.read_clip(clip_id)

def test_op_clips_tbl():
    print('[DEBUG] test_op_clips_tbl called')
    # NOTE: All new code should include debug print statements at the start of every function/method.

if __name__ == "__main__":
    unittest.main()
