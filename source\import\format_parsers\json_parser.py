#!/usr/bin/env python3
"""
JSON Parser for ClipsMore Import System
Handles parsing JSON files for import operations.
"""

import json
import os
from typing import Dict, List, Any
from pathlib import Path


class JSONParseError(Exception):
    """Custom exception for JSON parsing errors."""
    pass


class JSONParser:
    """
    JSON format parser for import operations.
    Handles ClipsMore JSON export format and generic JSON files.
    """
    
    def __init__(self):
        """Initialize the JSON parser."""
        print('[DEBUG] JSONParser.__init__ called')
        self.supported_schemas = ['clipsmore_v1', 'generic']
    
    def detect_schema(self, data: Dict[str, Any]) -> str:
        """
        Detect the schema type of the JSON data.
        
        Args:
            data: Parsed JSON data
            
        Returns:
            Schema type identifier
        """
        print('[DEBUG] JSONParser.detect_schema called')
        
        # Check for ClipsMore export format
        if isinstance(data, dict) and 'metadata' in data and 'data' in data:
            metadata = data.get('metadata', {})
            if metadata.get('source') == 'ClipsMore' or metadata.get('application') == 'ClipsMore':
                print('[DEBUG] Detected ClipsMore export format')
                return 'clipsmore_v1'
        
        # Check for array of objects (generic format)
        if isinstance(data, list) and len(data) > 0:
            if isinstance(data[0], dict):
                print('[DEBUG] Detected generic JSON array format')
                return 'generic'
        
        # Default to generic
        print('[DEBUG] Using generic schema as fallback')
        return 'generic'
    
    def preview(self, file_path: str, config) -> Dict[str, Any]:
        """
        Preview JSON file contents without full parsing.
        
        Args:
            file_path: Path to the JSON file
            config: Import configuration
            
        Returns:
            Dictionary containing preview information
        """
        print(f'[DEBUG] JSONParser.preview called for {file_path}')
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            schema = self.detect_schema(data)
            
            if schema == 'clipsmore_v1':
                return self._preview_clipsmore_format(data)
            else:
                return self._preview_generic_format(data)
                
        except json.JSONDecodeError as e:
            raise JSONParseError(f"Invalid JSON format: {e}")
        except Exception as e:
            raise JSONParseError(f"Error previewing JSON file: {e}")
    
    def _preview_clipsmore_format(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Preview ClipsMore export format."""
        print('[DEBUG] JSONParser._preview_clipsmore_format called')
        
        records = data.get('data', [])
        metadata = data.get('metadata', {})
        
        # Get sample records (first 5)
        sample_records = records[:5] if len(records) > 5 else records
        
        # Detect fields from first record
        detected_fields = {}
        if records:
            first_record = records[0]
            for field, value in first_record.items():
                detected_fields[field] = {
                    'type': type(value).__name__,
                    'sample_value': str(value)[:50] if value else ''
                }
        
        return {
            'record_count': len(records),
            'sample_records': sample_records,
            'detected_fields': detected_fields,
            'duplicate_count': 0,  # Would need duplicate detection logic
            'warnings': [],
            'metadata': metadata
        }
    
    def _preview_generic_format(self, data: Any) -> Dict[str, Any]:
        """Preview generic JSON format."""
        print('[DEBUG] JSONParser._preview_generic_format called')
        
        warnings = []
        
        # Handle different data structures
        if isinstance(data, list):
            records = data
        elif isinstance(data, dict):
            # Try to find array of records in the dict
            possible_arrays = [v for v in data.values() if isinstance(v, list)]
            if possible_arrays:
                records = max(possible_arrays, key=len)  # Use largest array
                warnings.append("Multiple arrays found, using largest one")
            else:
                records = [data]  # Treat single dict as one record
                warnings.append("Single object treated as one record")
        else:
            raise JSONParseError("Unsupported JSON structure")
        
        # Get sample records
        sample_records = records[:5] if len(records) > 5 else records
        
        # Detect fields
        detected_fields = {}
        if records and isinstance(records[0], dict):
            first_record = records[0]
            for field, value in first_record.items():
                detected_fields[field] = {
                    'type': type(value).__name__,
                    'sample_value': str(value)[:50] if value else ''
                }
        
        return {
            'record_count': len(records),
            'sample_records': sample_records,
            'detected_fields': detected_fields,
            'duplicate_count': 0,
            'warnings': warnings
        }
    
    def parse(self, file_path: str, config) -> List[Dict[str, Any]]:
        """
        Parse JSON file and return structured data.
        
        Args:
            file_path: Path to the JSON file
            config: Import configuration
            
        Returns:
            List of dictionaries containing parsed records
        """
        print(f'[DEBUG] JSONParser.parse called for {file_path}')
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            schema = self.detect_schema(data)
            
            if schema == 'clipsmore_v1':
                return self._parse_clipsmore_format(data, config)
            else:
                return self._parse_generic_format(data, config)
                
        except json.JSONDecodeError as e:
            raise JSONParseError(f"Invalid JSON format: {e}")
        except Exception as e:
            raise JSONParseError(f"Error parsing JSON file: {e}")
    
    def _parse_clipsmore_format(self, data: Dict[str, Any], config) -> List[Dict[str, Any]]:
        """Parse ClipsMore export format."""
        print('[DEBUG] JSONParser._parse_clipsmore_format called')
        
        records = data.get('data', [])
        parsed_records = []
        
        for record in records:
            # Apply field mapping if configured
            mapped_record = self._apply_field_mapping(record, config.field_mapping)
            
            # Validate record
            if self._validate_record(mapped_record, config.validation_rules):
                parsed_records.append(mapped_record)
        
        return parsed_records
    
    def _parse_generic_format(self, data: Any, config) -> List[Dict[str, Any]]:
        """Parse generic JSON format."""
        print('[DEBUG] JSONParser._parse_generic_format called')
        
        # Convert to list of records
        if isinstance(data, list):
            records = data
        elif isinstance(data, dict):
            # Try to find array of records
            possible_arrays = [v for v in data.values() if isinstance(v, list)]
            if possible_arrays:
                records = max(possible_arrays, key=len)
            else:
                records = [data]
        else:
            raise JSONParseError("Unsupported JSON structure")
        
        parsed_records = []
        
        for record in records:
            if isinstance(record, dict):
                # Apply field mapping
                mapped_record = self._apply_field_mapping(record, config.field_mapping)
                
                # Validate record
                if self._validate_record(mapped_record, config.validation_rules):
                    parsed_records.append(mapped_record)
        
        return parsed_records
    
    def _apply_field_mapping(self, record: Dict[str, Any], field_mapping: Dict[str, str]) -> Dict[str, Any]:
        """Apply field mapping to a record."""
        if not field_mapping:
            return record
        
        mapped_record = {}
        for source_field, target_field in field_mapping.items():
            if source_field in record:
                mapped_record[target_field] = record[source_field]
        
        # Include unmapped fields
        for field, value in record.items():
            if field not in field_mapping and field not in mapped_record:
                mapped_record[field] = value
        
        return mapped_record
    
    def _validate_record(self, record: Dict[str, Any], validation_rules: Dict[str, Any]) -> bool:
        """Validate a record against validation rules."""
        # Basic validation - can be extended
        if not validation_rules:
            return True
        
        # Check required fields
        required_fields = validation_rules.get('required_fields', [])
        for field in required_fields:
            if field not in record or not record[field]:
                return False
        
        return True
    
    def get_supported_extensions(self) -> List[str]:
        """Get list of supported file extensions."""
        return ['.json']
    
    def get_format_description(self) -> str:
        """Get human-readable format description."""
        return "JSON (JavaScript Object Notation) files"
