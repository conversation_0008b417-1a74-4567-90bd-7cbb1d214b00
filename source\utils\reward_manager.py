"""
RewardManager - Reward system for ClipsMore application

This module handles the visual reward system that provides user feedback
through animated emoji popups when actions are completed successfully.

Author: ClipsMore Development Team
Date: 2025-06-16
"""

import tkinter as tk
import random
from typing import Optional, List


class RewardManager:
    """
    Manages the reward system for the ClipsMore application.
    
    Provides visual feedback through animated emoji popups when users
    complete actions successfully (copying clips, creating assignments, etc.).
    """
    
    def __init__(self, parent: tk.Widget):
        """
        Initialize RewardManager with parent widget reference.
        
        Args:
            parent: Parent widget for reward popup positioning
        """
        print('[DEBUG] RewardManager.__init__ called')
        
        self.parent = parent
        self.reward_popup = None
        self.reward_label = None
        self.animation_after_id = None
        
        # Emoji collections for different reward types
        self.emoji_collections = {
            'success': ['🎉', '✨', '🌟', '⭐', '🎊', '🏆', '👏', '💫'],
            'copy': ['📋', '📄', '📝', '✂️', '📑', '🔗', '💾', '📌'],
            'create': ['🆕', '➕', '🔨', '🛠️', '⚡', '🎯', '🚀', '💡'],
            'update': ['🔄', '♻️', '🔧', '⚙️', '🔀', '🆙', '📈', '✅'],
            'delete': ['🗑️', '❌', '🧹', '💥', '🔥', '⚠️', '🚫', '💀'],
            'random': ['😊', '😄', '🙂', '😎', '🤗', '😍', '🥳', '😋']
        }
        
        print('[DEBUG] RewardManager initialized successfully')
    
    def show_reward(self, emoji_type: str = "random", message: str = "Great job!", duration: int = 2000):
        """
        Show a reward popup with animated emoji.
        
        Args:
            emoji_type: Type of emoji to show ('success', 'copy', 'create', etc.)
            message: Message to display with the emoji
            duration: Duration to show the popup in milliseconds
        """
        print(f'[DEBUG] RewardManager.show_reward called with type={emoji_type}, message={message}')
        
        try:
            # Hide any existing reward first
            self.hide_reward()
            
            # Select emoji
            emoji = self.cycle_emoji(emoji_type)
            
            # Create reward popup
            self.create_reward_popup(emoji, message)
            
            # Schedule automatic hiding
            if duration > 0:
                self.animation_after_id = self.parent.after(duration, self.hide_reward)
            
            print(f'[DEBUG] Reward popup shown: {emoji} {message}')
            
        except Exception as e:
            print(f'[ERROR] Failed to show reward: {e}')
    
    def hide_reward(self):
        """
        Hide the current reward popup.
        """
        print('[DEBUG] RewardManager.hide_reward called')
        
        try:
            # Cancel any pending animation
            if self.animation_after_id:
                self.parent.after_cancel(self.animation_after_id)
                self.animation_after_id = None
            
            # Destroy popup window
            if self.reward_popup:
                self.reward_popup.destroy()
                self.reward_popup = None
                self.reward_label = None
            
            print('[DEBUG] Reward popup hidden')
            
        except Exception as e:
            print(f'[ERROR] Failed to hide reward: {e}')
    
    def cycle_emoji(self, emoji_type: str = "random") -> str:
        """
        Get a random emoji from the specified collection.
        
        Args:
            emoji_type: Type of emoji collection to use
            
        Returns:
            Random emoji string from the collection
        """
        print(f'[DEBUG] RewardManager.cycle_emoji called with type={emoji_type}')
        
        try:
            # Get emoji collection
            if emoji_type in self.emoji_collections:
                emojis = self.emoji_collections[emoji_type]
            else:
                print(f'[WARNING] Unknown emoji type: {emoji_type}, using random')
                emojis = self.emoji_collections['random']
            
            # Select random emoji
            emoji = random.choice(emojis)
            print(f'[DEBUG] Selected emoji: {emoji}')
            return emoji
            
        except Exception as e:
            print(f'[ERROR] Failed to cycle emoji: {e}')
            return '😊'  # Fallback emoji
    
    def create_reward_popup(self, emoji: str, message: str):
        """
        Create the reward popup window with emoji and message.
        
        Args:
            emoji: Emoji to display
            message: Message to display
        """
        print(f'[DEBUG] RewardManager.create_reward_popup called with emoji={emoji}, message={message}')
        
        try:
            # Create popup window
            self.reward_popup = tk.Toplevel(self.parent)
            self.reward_popup.title("Reward")
            
            # Configure popup window
            self.reward_popup.overrideredirect(True)  # Remove window decorations
            self.reward_popup.attributes('-topmost', True)  # Keep on top
            self.reward_popup.configure(bg='#2E2E2E')  # Dark background
            
            # Create main frame
            main_frame = tk.Frame(self.reward_popup, bg='#2E2E2E', padx=20, pady=15)
            main_frame.pack(fill='both', expand=True)
            
            # Create emoji label
            emoji_label = tk.Label(main_frame,
                                 text=emoji,
                                 font=('Segoe UI Emoji', 32),
                                 bg='#2E2E2E',
                                 fg='white')
            emoji_label.pack(pady=(0, 5))
            
            # Create message label
            self.reward_label = tk.Label(main_frame,
                                       text=message,
                                       font=('Segoe UI', 12, 'bold'),
                                       bg='#2E2E2E',
                                       fg='white')
            self.reward_label.pack()
            
            # Position popup
            self._position_popup()
            
            # Add fade-in animation
            self._animate_popup_in()
            
            print('[DEBUG] Reward popup created successfully')
            
        except Exception as e:
            print(f'[ERROR] Failed to create reward popup: {e}')
    
    def _position_popup(self):
        """
        Position the popup window in the center-right of the parent window.
        """
        try:
            # Update popup to get actual size
            self.reward_popup.update_idletasks()
            
            # Get popup dimensions
            popup_width = self.reward_popup.winfo_reqwidth()
            popup_height = self.reward_popup.winfo_reqheight()
            
            # Get parent window position and size
            parent_x = self.parent.winfo_rootx()
            parent_y = self.parent.winfo_rooty()
            parent_width = self.parent.winfo_width()
            parent_height = self.parent.winfo_height()
            
            # Calculate position (center-right of parent)
            x = parent_x + parent_width - popup_width - 50
            y = parent_y + (parent_height - popup_height) // 2
            
            # Ensure popup stays on screen
            screen_width = self.parent.winfo_screenwidth()
            screen_height = self.parent.winfo_screenheight()
            
            if x + popup_width > screen_width:
                x = screen_width - popup_width - 10
            if y + popup_height > screen_height:
                y = screen_height - popup_height - 10
            if x < 0:
                x = 10
            if y < 0:
                y = 10
            
            # Set popup position
            self.reward_popup.geometry(f"+{x}+{y}")
            
            print(f'[DEBUG] Popup positioned at ({x}, {y})')
            
        except Exception as e:
            print(f'[ERROR] Failed to position popup: {e}')
    
    def _animate_popup_in(self):
        """
        Animate the popup appearing with a fade-in effect.
        """
        try:
            # Start with low opacity and gradually increase
            self.reward_popup.attributes('-alpha', 0.0)
            self._fade_in_step(0.0)
            
        except Exception as e:
            print(f'[ERROR] Failed to animate popup: {e}')
    
    def _fade_in_step(self, alpha: float):
        """
        Single step of fade-in animation.
        
        Args:
            alpha: Current alpha value (0.0 to 1.0)
        """
        try:
            if self.reward_popup and alpha < 1.0:
                alpha += 0.1
                self.reward_popup.attributes('-alpha', alpha)
                self.parent.after(50, lambda: self._fade_in_step(alpha))
            elif self.reward_popup:
                self.reward_popup.attributes('-alpha', 1.0)
                
        except Exception as e:
            print(f'[ERROR] Failed to fade in popup: {e}')
    
    def show_copy_reward(self, alias: str = ""):
        """
        Show a reward specifically for copying clips.
        
        Args:
            alias: Alias of the copied clip
        """
        message = f"Copied {alias}!" if alias else "Copied to clipboard!"
        self.show_reward("copy", message, 1500)
    
    def show_create_reward(self, item_type: str = "item"):
        """
        Show a reward for creating new items.
        
        Args:
            item_type: Type of item created
        """
        message = f"Created {item_type}!"
        self.show_reward("create", message, 2000)
    
    def show_update_reward(self, item_type: str = "item"):
        """
        Show a reward for updating items.
        
        Args:
            item_type: Type of item updated
        """
        message = f"Updated {item_type}!"
        self.show_reward("update", message, 1500)
    
    def show_delete_reward(self, item_type: str = "item"):
        """
        Show a reward for deleting items.
        
        Args:
            item_type: Type of item deleted
        """
        message = f"Deleted {item_type}!"
        self.show_reward("delete", message, 1500)
    
    def show_success_reward(self, message: str = "Success!"):
        """
        Show a general success reward.
        
        Args:
            message: Success message to display
        """
        self.show_reward("success", message, 2000)
    
    def is_showing(self) -> bool:
        """
        Check if a reward popup is currently being shown.
        
        Returns:
            True if popup is visible, False otherwise
        """
        return self.reward_popup is not None and self.reward_popup.winfo_exists()
