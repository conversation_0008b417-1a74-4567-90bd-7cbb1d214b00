"""
Drag & Drop Manager for ClipsMore Application

This manager handles all drag and drop operations including:
- Drag initiation and motion tracking
- Drop target validation and highlighting
- Context menu creation for drag operations
- Move and copy operations between business cases and components
- Visual feedback during drag operations

Author: ClipsMore Development Team
Version: 2.0
"""

import tkinter as tk
from typing import Dict, Any, Optional, Tuple
from tkinter import messagebox

# NOTE: All new code should include debug print statements at the start of every function/method.

class DragDropManager:
    """
    Manages drag and drop operations for the ClipsMore application.
    
    Handles:
    - Drag initiation from clip buttons
    - Drag motion tracking and visual feedback
    - Drop target validation and highlighting
    - Context menu creation for drag operations
    - Move and copy operations between assignments
    - Error handling and user feedback
    """
    
    def __init__(self, tree_widget, more_tab, theme_manager, database_manager):
        """
        Initialize DragDropManager with required components.
        
        Args:
            tree_widget: The tree widget for drag and drop operations
            more_tab: The more tab frame for context menus
            theme_manager: ThemeManager instance for styling
            database_manager: DatabaseManager instance for data operations
        """
        print('[DEBUG] DragDropManager.__init__ called')
        
        self.tree = tree_widget
        self.more_tab = more_tab
        self.theme_manager = theme_manager
        self.database_manager = database_manager
        
        # Drag state tracking
        self.drag_source = None
        self.context_menu = None
        
        # Get theme colors for styling
        self._update_theme_colors()
        
        print('[DEBUG] DragDropManager initialized successfully')
    
    def _update_theme_colors(self):
        """Update theme colors from ThemeManager."""
        print('[DEBUG] DragDropManager._update_theme_colors called')
        
        colors = self.theme_manager.get_theme_colors()
        self.button_bg = colors['button_bg']
        self.button_fg = colors['button_fg']
        self.tree_select = colors['tree_select']
        self.fg_color = colors['fg_color']
    
    def handle_drag_start(self, event) -> bool:
        """
        Handle mouse press on tree item for drag initiation.
        
        Args:
            event: Mouse press event
            
        Returns:
            bool: True if drag was initiated, False otherwise
        """
        print('[DEBUG] DragDropManager.handle_drag_start called')
        
        try:
            item = self.tree.identify_row(event.y)
            if item:
                item_values = self.tree.item(item, 'values')
                if item_values and item_values[0] == 'Clip Button':
                    # Store drag source information
                    self.drag_source = {
                        'item': item,
                        'type': 'clip_button',
                        'alias': self.tree.item(item, 'text').replace('📎 ', '').strip(),
                        'start_x': event.x,
                        'start_y': event.y
                    }
                    print(f'[DEBUG] Drag initiated for clip button: {self.drag_source["alias"]}')
                    return True
                else:
                    self.drag_source = None
                    return False
            
            return False
            
        except Exception as e:
            print(f'[ERROR] Failed to handle drag start: {e}')
            self.drag_source = None
            return False
    
    def handle_drag_motion(self, event):
        """
        Handle drag motion with visual feedback.
        
        Args:
            event: Mouse motion event
        """
        print('[DEBUG] DragDropManager.handle_drag_motion called')
        
        try:
            if hasattr(self, 'drag_source') and self.drag_source:
                # Calculate drag distance
                dx = abs(event.x - self.drag_source['start_x'])
                dy = abs(event.y - self.drag_source['start_y'])
                
                # Only start visual drag feedback if moved enough
                if dx > 5 or dy > 5:
                    # Change cursor to indicate dragging
                    self.tree.config(cursor="hand2")
                    
                    # Highlight potential drop targets
                    target_item = self.tree.identify_row(event.y)
                    if target_item and target_item != self.drag_source['item']:
                        target_values = self.tree.item(target_item, 'values')
                        if target_values and target_values[0] in ['Business Case', 'Component']:
                            # Valid drop target
                            self.tree.selection_set(target_item)
                            
        except Exception as e:
            print(f'[ERROR] Failed to handle drag motion: {e}')
    
    def handle_drop(self, event) -> bool:
        """
        Handle mouse release for drag completion.
        
        Args:
            event: Mouse release event
            
        Returns:
            bool: True if drop was handled, False otherwise
        """
        print('[DEBUG] DragDropManager.handle_drop called')
        
        try:
            # Reset cursor
            self.tree.config(cursor="")
            
            if hasattr(self, 'drag_source') and self.drag_source:
                target_item = self.tree.identify_row(event.y)
                
                if target_item and target_item != self.drag_source['item']:
                    target_values = self.tree.item(target_item, 'values')
                    target_text = self.tree.item(target_item, 'text')
                    
                    if target_values and target_values[0] in ['Business Case', 'Component']:
                        # Valid drop - show context menu
                        self.show_context_menu(event, target_item, target_values[0], target_text)
                        return True
                
                # Clear drag source
                self.drag_source = None
                return False
            
            return False
            
        except Exception as e:
            print(f'[ERROR] Failed to handle drop: {e}')
            # Clear drag source on error
            self.drag_source = None
            return False
    
    def show_context_menu(self, event, target_item: str, target_type: str, target_name: str):
        """
        Show context menu for drag and drop operations.
        
        Args:
            event: Mouse event for menu positioning
            target_item: Target tree item ID
            target_type: Type of target ('Business Case' or 'Component')
            target_name: Display name of target
        """
        print(f'[DEBUG] DragDropManager.show_context_menu called for {target_type}: {target_name}')
        
        try:
            if not hasattr(self, 'drag_source') or not self.drag_source:
                return
            
            alias = self.drag_source['alias']
            
            # Update theme colors
            self._update_theme_colors()
            
            # Create context menu
            self.context_menu = tk.Menu(self.more_tab, tearoff=0,
                                      bg=self.button_bg, fg=self.button_fg,
                                      activebackground=self.tree_select, 
                                      activeforeground=self.fg_color)
            
            self.context_menu.add_command(
                label=f"Move '{alias}' to {target_name}",
                command=lambda: self.perform_move_operation(alias, target_item, target_type, target_name)
            )
            
            self.context_menu.add_command(
                label=f"Copy '{alias}' to {target_name}",
                command=lambda: self.perform_copy_operation(alias, target_item, target_type, target_name)
            )
            
            self.context_menu.add_separator()
            
            self.context_menu.add_command(
                label="Cancel",
                command=lambda: self.context_menu.destroy()
            )
            
            # Show menu at cursor position
            self.context_menu.post(event.x_root, event.y_root)
            
        except tk.TclError:
            pass  # Menu might be destroyed already
        except Exception as e:
            print(f'[ERROR] Failed to show context menu: {e}')
    
    def perform_move_operation(self, alias: str, target_item: str, target_type: str, target_name: str):
        """
        Handle move operation from drag and drop.
        
        Args:
            alias: Alias of the clip to move
            target_item: Target tree item ID
            target_type: Type of target ('Business Case' or 'Component')
            target_name: Display name of target
        """
        print(f'[DEBUG] DragDropManager.perform_move_operation called: {alias} -> {target_name}')
        
        try:
            from source.DB.op_clipsmore_enhanced import ClipsMoreEnhancedOperations
            from source.DB.op_more_tbl import MoreTableOperations
            
            enhanced_ops = self.database_manager.get_enhanced_operations()
            more_ops = self.database_manager.get_more_operations()
            
            # Get assignment by alias
            assignment = enhanced_ops.get_assignment_by_alias(alias)
            
            if assignment:
                transaction_id = assignment['transaction_id']
                
                # Determine target IDs based on target type
                if target_type == 'Business Case':
                    target_values = self.tree.item(target_item, 'values')
                    target_bus_id = int(target_values[1]) if len(target_values) > 1 else None
                    target_comp_id = None
                elif target_type == 'Component':
                    target_values = self.tree.item(target_item, 'values')
                    target_comp_id = int(target_values[1]) if len(target_values) > 1 else None
                    # Get business case ID from parent
                    parent_item = self.tree.parent(target_item)
                    parent_values = self.tree.item(parent_item, 'values')
                    target_bus_id = int(parent_values[1]) if len(parent_values) > 1 else None
                else:
                    messagebox.showerror("Move Failed", f"Invalid target type: {target_type}")
                    return
                
                # Update assignment
                if enhanced_ops.update_assignment(transaction_id, target_bus_id, target_comp_id):
                    messagebox.showinfo("Move Successful", f"Moved '{alias}' to {target_name}")
                    # Refresh tree to show changes
                    if hasattr(self, 'tree_manager'):
                        self.tree_manager.refresh_tree()
                else:
                    messagebox.showerror("Move Failed", f"Failed to move '{alias}' to {target_name}")
            else:
                messagebox.showerror("Move Failed", f"Assignment for '{alias}' not found")
                
        except Exception as e:
            print(f'[ERROR] Drag move failed: {e}')
            messagebox.showerror("Move Failed", f"Error moving clip: {e}")
    
    def perform_copy_operation(self, alias: str, target_item: str, target_type: str, target_name: str):
        """
        Handle copy operation from drag and drop.
        
        Args:
            alias: Alias of the clip to copy
            target_item: Target tree item ID
            target_type: Type of target ('Business Case' or 'Component')
            target_name: Display name of target
        """
        print(f'[DEBUG] DragDropManager.perform_copy_operation called: {alias} -> {target_name}')
        
        try:
            from source.DB.op_clipsmore_enhanced import ClipsMoreEnhancedOperations
            from source.DB.op_more_tbl import MoreTableOperations
            
            enhanced_ops = self.database_manager.get_enhanced_operations()
            more_ops = self.database_manager.get_more_operations()
            
            # Get assignment by alias
            assignment = enhanced_ops.get_assignment_by_alias(alias)
            
            if assignment:
                clip_id = assignment['clip_id']
                
                # Determine target IDs based on target type
                if target_type == 'Business Case':
                    target_values = self.tree.item(target_item, 'values')
                    target_bus_id = int(target_values[1]) if len(target_values) > 1 else None
                    target_comp_id = None
                elif target_type == 'Component':
                    target_values = self.tree.item(target_item, 'values')
                    target_comp_id = int(target_values[1]) if len(target_values) > 1 else None
                    # Get business case ID from parent
                    parent_item = self.tree.parent(target_item)
                    parent_values = self.tree.item(parent_item, 'values')
                    target_bus_id = int(parent_values[1]) if len(parent_values) > 1 else None
                else:
                    messagebox.showerror("Copy Failed", f"Invalid target type: {target_type}")
                    return
                
                # Create new assignment (copy)
                if enhanced_ops.create_assignment(clip_id, target_bus_id, target_comp_id):
                    messagebox.showinfo("Copy Successful", f"Copied '{alias}' to {target_name}")
                    # Refresh tree to show changes
                    if hasattr(self, 'tree_manager'):
                        self.tree_manager.refresh_tree()
                else:
                    messagebox.showerror("Copy Failed", f"Failed to copy '{alias}' to {target_name}")
            else:
                messagebox.showerror("Copy Failed", f"Assignment for '{alias}' not found")
                
        except Exception as e:
            print(f'[ERROR] Drag copy failed: {e}')
            messagebox.showerror("Copy Failed", f"Error copying clip: {e}")
    
    def set_tree_manager(self, tree_manager):
        """
        Set reference to tree manager for tree refresh operations.
        
        Args:
            tree_manager: TreeManager instance
        """
        print('[DEBUG] DragDropManager.set_tree_manager called')
        self.tree_manager = tree_manager
    
    def clear_drag_state(self):
        """Clear any active drag state."""
        print('[DEBUG] DragDropManager.clear_drag_state called')
        
        self.drag_source = None
        if self.tree:
            self.tree.config(cursor="")
        if self.context_menu:
            try:
                self.context_menu.destroy()
            except:
                pass
            self.context_menu = None
    
    def update_theme(self):
        """Update theme colors when theme changes."""
        print('[DEBUG] DragDropManager.update_theme called')
        self._update_theme_colors()

# NOTE: All new code should include debug print statements at the start of every function/method.
