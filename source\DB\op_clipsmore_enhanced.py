#!/usr/bin/env python3
"""
Enhanced ClipsMore Table Operations for v2.0
Provides CRUD operations for the enhanced clipsmore_tbl with alias and tree position support.
"""

import sqlite3
import re
from typing import Optional, List, Dict, Any

class ClipsMoreError(Exception):
    """Custom exception for ClipsMore operations."""
    pass

class ClipsMoreEnhancedOperations:
    """Enhanced operations for clipsmore_tbl with v2.0 features.
    
    Provides CRUD operations for associating clips with business cases and components
    with support for aliases, tree positioning, and drag & drop operations.
    """
    
    def __init__(self, db_path: Optional[str] = None):
        """Initialize with optional custom database path.

        Args:
            db_path: Alternate path to SQLite database file. Uses default if None.
        """
        print('[DEBUG] ClipsMoreEnhancedOperations.__init__ called')

        if db_path is None:
            # Try to find database in common locations
            import os
            possible_paths = [
                "DB/clipsmore_db.db",
                "clipsmore_db.db",
                "../DB/clipsmore_db.db",
                os.path.join(os.path.dirname(__file__), "clipsmore_db.db")
            ]

            for path in possible_paths:
                if os.path.exists(path):
                    self.db_path = path
                    break
            else:
                # Default fallback
                self.db_path = "DB/clipsmore_db.db"
        else:
            self.db_path = db_path

        print(f'[DEBUG] Using database path: {self.db_path}')

        # Verify database exists
        import os
        if not os.path.exists(self.db_path):
            print(f'[WARNING] Database file not found at: {self.db_path}')
        else:
            print(f'[DEBUG] Database file confirmed at: {self.db_path}')
    
    def create_assignment(self, clip_id: int, more_bus_id: int, 
                         more_comp_id: Optional[int] = None, 
                         alias: Optional[str] = None,
                         tree_position: Optional[int] = None) -> int:
        """Create new clip assignment with auto-generated alias if needed.
        
        Args:
            clip_id: ID of the clip to assign
            more_bus_id: ID of the business case
            more_comp_id: ID of the component (optional)
            alias: Custom alias (optional, will auto-generate if None)
            tree_position: Position in tree (optional, will auto-assign if None)
            
        Returns:
            transaction_id of the created assignment
            
        Raises:
            ClipsMoreError: If assignment creation fails
        """
        print(f'[DEBUG] create_assignment called with clip_id={clip_id}, more_bus_id={more_bus_id}, more_comp_id={more_comp_id}')
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Generate alias if not provided
                if not alias:
                    alias = self._generate_unique_alias(cursor, clip_id)
                
                # Get next tree position if not provided
                if tree_position is None:
                    tree_position = self._get_next_tree_position(cursor, more_bus_id, more_comp_id)
                
                # Validate alias uniqueness
                if not self._is_alias_unique(cursor, alias):
                    raise ClipsMoreError(f"Alias '{alias}' already exists")
                
                # Create assignment
                cursor.execute("""
                    INSERT INTO clipsmore_tbl 
                    (clip_id, alias, more_bus_id, more_comp_id, tree_position)
                    VALUES (?, ?, ?, ?, ?)
                """, (clip_id, alias, more_bus_id, more_comp_id, tree_position))
                
                transaction_id = cursor.lastrowid
                conn.commit()
                
                print(f'[DEBUG] Created assignment with transaction_id={transaction_id}')
                return transaction_id
                
        except sqlite3.Error as e:
            raise ClipsMoreError(f"Failed to create assignment: {str(e)}") from e
    
    def update_assignment(self, transaction_id: int, 
                         alias: Optional[str] = None,
                         more_bus_id: Optional[int] = None,
                         more_comp_id: Optional[int] = None,
                         tree_position: Optional[int] = None) -> bool:
        """Update existing assignment.
        
        Args:
            transaction_id: ID of the assignment to update
            alias: New alias (optional)
            more_bus_id: New business case ID (optional)
            more_comp_id: New component ID (optional)
            tree_position: New tree position (optional)
            
        Returns:
            True if update successful
            
        Raises:
            ClipsMoreError: If update fails
        """
        print(f'[DEBUG] update_assignment called with transaction_id={transaction_id}')
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Build dynamic update query
                updates = []
                params = []
                
                if alias is not None:
                    if not self._is_alias_unique(cursor, alias, exclude_transaction_id=transaction_id):
                        raise ClipsMoreError(f"Alias '{alias}' already exists")
                    updates.append("alias = ?")
                    params.append(alias)
                
                if more_bus_id is not None:
                    updates.append("more_bus_id = ?")
                    params.append(more_bus_id)
                
                if more_comp_id is not None:
                    updates.append("more_comp_id = ?")
                    params.append(more_comp_id)
                
                if tree_position is not None:
                    updates.append("tree_position = ?")
                    params.append(tree_position)
                
                if updates:
                    updates.append("modified_date = CURRENT_TIMESTAMP")
                    params.append(transaction_id)
                    
                    query = f"UPDATE clipsmore_tbl SET {', '.join(updates)} WHERE transaction_id = ?"
                    cursor.execute(query, params)
                    
                    if cursor.rowcount == 0:
                        raise ClipsMoreError(f"Assignment with transaction_id {transaction_id} not found")
                    
                    conn.commit()
                    print(f'[DEBUG] Updated assignment {transaction_id}')
                    return True
                
                return True
                
        except sqlite3.Error as e:
            raise ClipsMoreError(f"Failed to update assignment: {str(e)}") from e
    
    def delete_assignment(self, transaction_id: int) -> bool:
        """Delete assignment by transaction ID.
        
        Args:
            transaction_id: ID of the assignment to delete
            
        Returns:
            True if deletion successful
            
        Raises:
            ClipsMoreError: If deletion fails
        """
        print(f'[DEBUG] delete_assignment called with transaction_id={transaction_id}')
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("DELETE FROM clipsmore_tbl WHERE transaction_id = ?", (transaction_id,))
                
                if cursor.rowcount == 0:
                    raise ClipsMoreError(f"Assignment with transaction_id {transaction_id} not found")
                
                conn.commit()
                print(f'[DEBUG] Deleted assignment {transaction_id}')
                return True
                
        except sqlite3.Error as e:
            raise ClipsMoreError(f"Failed to delete assignment: {str(e)}") from e
    
    def move_assignment(self, transaction_id: int, target_bus_id: int, 
                       target_comp_id: Optional[int] = None,
                       new_tree_position: Optional[int] = None) -> bool:
        """Move assignment to new business case/component.
        
        Args:
            transaction_id: ID of the assignment to move
            target_bus_id: Target business case ID
            target_comp_id: Target component ID (optional)
            new_tree_position: New position in target tree (optional)
            
        Returns:
            True if move successful
            
        Raises:
            ClipsMoreError: If move fails
        """
        print(f'[DEBUG] move_assignment called with transaction_id={transaction_id}')
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Get next position if not specified
                if new_tree_position is None:
                    new_tree_position = self._get_next_tree_position(cursor, target_bus_id, target_comp_id)
                
                # Update assignment
                cursor.execute("""
                    UPDATE clipsmore_tbl 
                    SET more_bus_id = ?, more_comp_id = ?, tree_position = ?, modified_date = CURRENT_TIMESTAMP
                    WHERE transaction_id = ?
                """, (target_bus_id, target_comp_id, new_tree_position, transaction_id))
                
                if cursor.rowcount == 0:
                    raise ClipsMoreError(f"Assignment with transaction_id {transaction_id} not found")
                
                conn.commit()
                print(f'[DEBUG] Moved assignment {transaction_id}')
                return True
                
        except sqlite3.Error as e:
            raise ClipsMoreError(f"Failed to move assignment: {str(e)}") from e
    
    def copy_assignment(self, transaction_id: int, target_bus_id: int,
                       target_comp_id: Optional[int] = None) -> int:
        """Copy assignment to new business case/component with new alias.
        
        Args:
            transaction_id: ID of the assignment to copy
            target_bus_id: Target business case ID
            target_comp_id: Target component ID (optional)
            
        Returns:
            transaction_id of the new copied assignment
            
        Raises:
            ClipsMoreError: If copy fails
        """
        print(f'[DEBUG] copy_assignment called with transaction_id={transaction_id}')
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Get original assignment
                cursor.execute("""
                    SELECT clip_id, alias FROM clipsmore_tbl 
                    WHERE transaction_id = ?
                """, (transaction_id,))
                
                result = cursor.fetchone()
                if not result:
                    raise ClipsMoreError(f"Assignment with transaction_id {transaction_id} not found")
                
                clip_id, original_alias = result
                
                # Generate new unique alias
                new_alias = self._generate_copy_alias(cursor, original_alias)
                
                # Create new assignment
                new_transaction_id = self.create_assignment(
                    clip_id=clip_id,
                    more_bus_id=target_bus_id,
                    more_comp_id=target_comp_id,
                    alias=new_alias
                )
                
                print(f'[DEBUG] Copied assignment to new transaction_id={new_transaction_id}')
                return new_transaction_id
                
        except sqlite3.Error as e:
            raise ClipsMoreError(f"Failed to copy assignment: {str(e)}") from e
    
    def get_assignments_by_business_case(self, more_bus_id: int) -> List[Dict[str, Any]]:
        """Get all assignments for a business case using clipsmore_vw.
        
        Args:
            more_bus_id: Business case ID
            
        Returns:
            List of assignment dictionaries
        """
        print(f'[DEBUG] get_assignments_by_business_case called with more_bus_id={more_bus_id}')
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT * FROM clipsmore_vw 
                    WHERE more_bus_id = ?
                    ORDER BY tree_position, created_date
                """, (more_bus_id,))
                
                columns = [desc[0] for desc in cursor.description]
                results = []
                
                for row in cursor.fetchall():
                    results.append(dict(zip(columns, row)))
                
                print(f'[DEBUG] Found {len(results)} assignments for business case {more_bus_id}')
                return results
                
        except sqlite3.Error as e:
            raise ClipsMoreError(f"Failed to get assignments: {str(e)}") from e
    
    def get_assignments_by_component(self, more_comp_id: int) -> List[Dict[str, Any]]:
        """Get all assignments for a component using clipsmore_vw.

        Args:
            more_comp_id: Component ID

        Returns:
            List of assignment dictionaries
        """
        print(f'[DEBUG] get_assignments_by_component called with more_comp_id={more_comp_id}')

        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute("""
                    SELECT * FROM clipsmore_vw
                    WHERE more_comp_id = ?
                    ORDER BY tree_position, created_date
                """, (more_comp_id,))

                columns = [desc[0] for desc in cursor.description]
                results = []

                for row in cursor.fetchall():
                    results.append(dict(zip(columns, row)))

                print(f'[DEBUG] Found {len(results)} assignments for component {more_comp_id}')
                return results

        except sqlite3.Error as e:
            raise ClipsMoreError(f"Failed to get assignments: {str(e)}") from e

    def get_assignments_by_clip(self, clip_id: int) -> List[Dict[str, Any]]:
        """Get all assignments for a specific clip using clipsmore_vw.

        Args:
            clip_id: Clip ID

        Returns:
            List of assignment dictionaries
        """
        print(f'[DEBUG] get_assignments_by_clip called with clip_id={clip_id}')

        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute("""
                    SELECT * FROM clipsmore_vw
                    WHERE clip_id = ?
                    ORDER BY created_date DESC
                """, (clip_id,))

                columns = [desc[0] for desc in cursor.description]
                results = []

                for row in cursor.fetchall():
                    results.append(dict(zip(columns, row)))

                print(f'[DEBUG] Found {len(results)} assignments for clip {clip_id}')
                return results

        except sqlite3.Error as e:
            raise ClipsMoreError(f"Failed to get assignments: {str(e)}") from e
    
    def _generate_unique_alias(self, cursor, clip_id: int) -> str:
        """Generate unique alias from clip content.
        
        Args:
            cursor: Database cursor
            clip_id: ID of the clip
            
        Returns:
            Unique alias string
        """
        print(f'[DEBUG] _generate_unique_alias called for clip_id={clip_id}')
        
        try:
            # Get clip content
            cursor.execute("SELECT clip FROM clips_tbl WHERE clip_id = ?", (clip_id,))
            result = cursor.fetchone()
            
            if result and result[0]:
                content = str(result[0])
                
                # Extract meaningful words
                words = re.findall(r'\b[a-zA-Z]{3,}\b', content)
                if words:
                    # Take first few meaningful words
                    alias_words = words[:2]
                    base_alias = "_".join(alias_words).lower()[:15]
                    
                    # Ensure uniqueness
                    alias = base_alias
                    counter = 1
                    while not self._is_alias_unique(cursor, alias):
                        alias = f"{base_alias}_{counter}"
                        counter += 1
                    
                    return alias
            
            # Fallback to generic naming
            counter = 1
            while True:
                alias = f"clip_{counter}"
                if self._is_alias_unique(cursor, alias):
                    return alias
                counter += 1
                
        except Exception as e:
            print(f'[ERROR] Failed to generate alias: {e}')
            return f"clip_{clip_id}"
    
    def _generate_copy_alias(self, cursor, original_alias: str) -> str:
        """Generate unique alias for copied assignment.
        
        Args:
            cursor: Database cursor
            original_alias: Original alias to base copy on
            
        Returns:
            Unique alias for copy
        """
        print(f'[DEBUG] _generate_copy_alias called for original_alias={original_alias}')
        
        base_alias = f"{original_alias}_copy"
        alias = base_alias
        counter = 1
        
        while not self._is_alias_unique(cursor, alias):
            alias = f"{base_alias}_{counter}"
            counter += 1
        
        return alias
    
    def _is_alias_unique(self, cursor, alias: str, exclude_transaction_id: Optional[int] = None) -> bool:
        """Check if alias is unique.

        Args:
            cursor: Database cursor
            alias: Alias to check
            exclude_transaction_id: Transaction ID to exclude from check

        Returns:
            True if alias is unique
        """
        print(f'[DEBUG] _is_alias_unique called for alias={alias}, exclude_transaction_id={exclude_transaction_id}')
        if exclude_transaction_id:
            cursor.execute("""
                SELECT transaction_id FROM clipsmore_tbl 
                WHERE alias = ? AND transaction_id != ?
            """, (alias, exclude_transaction_id))
        else:
            cursor.execute("SELECT transaction_id FROM clipsmore_tbl WHERE alias = ?", (alias,))
        
        return cursor.fetchone() is None
    
    def _get_next_tree_position(self, cursor, more_bus_id: int, more_comp_id: Optional[int] = None) -> int:
        """Get next available tree position.

        Args:
            cursor: Database cursor
            more_bus_id: Business case ID
            more_comp_id: Component ID (optional)

        Returns:
            Next available tree position
        """
        print(f'[DEBUG] _get_next_tree_position called for more_bus_id={more_bus_id}, more_comp_id={more_comp_id}')
        if more_comp_id:
            cursor.execute("""
                SELECT COALESCE(MAX(tree_position), -1) + 1 
                FROM clipsmore_tbl 
                WHERE more_bus_id = ? AND more_comp_id = ?
            """, (more_bus_id, more_comp_id))
        else:
            cursor.execute("""
                SELECT COALESCE(MAX(tree_position), -1) + 1 
                FROM clipsmore_tbl 
                WHERE more_bus_id = ? AND more_comp_id IS NULL
            """, (more_bus_id,))
        
        result = cursor.fetchone()
        return result[0] if result else 0

    def clear_all_assignments(self) -> int:
        """Remove all assignments from the database.

        Returns:
            int: Number of assignments deleted
        """
        print('[DEBUG] ClipsMoreEnhancedOperations.clear_all_assignments called')
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM clipsmore_tbl")
                deleted_count = cursor.rowcount
                conn.commit()
                print(f'[DEBUG] Cleared all assignments, deleted: {deleted_count}')
                return deleted_count
        except Exception as e:
            print(f'[ERROR] Failed to clear all assignments: {e}')
            return 0

# NOTE: All new code should include debug print statements at the start of every function/method.
