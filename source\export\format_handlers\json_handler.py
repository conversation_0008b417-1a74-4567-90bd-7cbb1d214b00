#!/usr/bin/env python3
"""
JSON Format Handler for ClipsMore Export System
Handles export of clipboard data to JSON format with complete structure and metadata.
"""

import json
import os
from datetime import datetime
from typing import Dict, List, Any, Optional


class JSONHandler:
    """
    JSON format handler for exporting ClipsMore data.
    Provides complete structured export with metadata and relationships.
    """
    
    def __init__(self):
        """Initialize the JSON handler."""
        print('[DEBUG] JSONHandler.__init__ called')
        self.format_name = "JSON"
        self.file_extension = ".json"
    
    def export(self, data: List[Dict[str, Any]], output_path: str, 
               config: Dict[str, Any] = None) -> bool:
        """
        Export data to JSON format.
        
        Args:
            data: List of clip records to export
            output_path: Output file path
            config: Export configuration options
                - indent: JSON indentation (default: 2)
                - include_metadata: Include export metadata (default: True)
                - hierarchical: Group by business cases/components (default: False)
                - pretty_print: Format for readability (default: True)
        
        Returns:
            True if export successful, False otherwise
        """
        print(f'[DEBUG] JSONHandler.export called for {len(data)} records')
        
        try:
            config = config or {}
            
            # Prepare export data structure
            export_data = self._prepare_export_data(data, config)
            
            # Ensure output path has correct extension
            if not output_path.endswith(self.file_extension):
                output_path += self.file_extension
            
            # Write JSON data to file
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(
                    export_data,
                    f,
                    indent=config.get('indent', 2) if config.get('pretty_print', True) else None,
                    ensure_ascii=False,
                    default=self._json_serializer
                )
            
            print(f'[DEBUG] JSON export completed: {output_path}')
            return True
            
        except Exception as e:
            print(f'[ERROR] JSON export failed: {e}')
            return False
    
    def _prepare_export_data(self, data: List[Dict[str, Any]], 
                           config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Prepare data structure for JSON export.
        
        Args:
            data: Raw clip data
            config: Export configuration
        
        Returns:
            Structured data ready for JSON export
        """
        print('[DEBUG] JSONHandler._prepare_export_data called')
        
        export_structure = {}
        
        # Add metadata if requested
        if config.get('include_metadata', True):
            export_structure['metadata'] = self._create_metadata(data, config)
        
        # Organize data based on configuration
        if config.get('hierarchical', False):
            export_structure['data'] = self._create_hierarchical_structure(data)
        else:
            export_structure['data'] = self._create_flat_structure(data)
        
        return export_structure
    
    def _create_metadata(self, data: List[Dict[str, Any]], 
                        config: Dict[str, Any]) -> Dict[str, Any]:
        """Create export metadata."""
        print('[DEBUG] JSONHandler._create_metadata called')
        
        metadata = {
            'export_info': {
                'format': self.format_name,
                'version': '1.0',
                'exported_at': datetime.now().isoformat(),
                'total_records': len(data),
                'application': 'ClipsMore',
                'application_version': '2.0'
            },
            'statistics': self._calculate_statistics(data),
            'export_config': config
        }
        
        return metadata
    
    def _calculate_statistics(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate statistics about the exported data."""
        print('[DEBUG] JSONHandler._calculate_statistics called')
        
        stats = {
            'total_clips': len(data),
            'assigned_clips': 0,
            'unassigned_clips': 0,
            'business_cases': set(),
            'components': set(),
            'date_range': {
                'earliest': None,
                'latest': None
            }
        }
        
        for record in data:
            # Count assigned vs unassigned
            if record.get('more_bus_id') or record.get('more_comp_id'):
                stats['assigned_clips'] += 1
            else:
                stats['unassigned_clips'] += 1
            
            # Collect business cases and components
            if record.get('bus_case'):
                stats['business_cases'].add(record['bus_case'])
            if record.get('bus_component'):
                stats['components'].add(record['bus_component'])
            
            # Track date range
            timestamp = record.get('timestamp')
            if timestamp:
                if not stats['date_range']['earliest'] or timestamp < stats['date_range']['earliest']:
                    stats['date_range']['earliest'] = timestamp
                if not stats['date_range']['latest'] or timestamp > stats['date_range']['latest']:
                    stats['date_range']['latest'] = timestamp
        
        # Convert sets to counts
        stats['unique_business_cases'] = len(stats['business_cases'])
        stats['unique_components'] = len(stats['components'])
        stats['business_cases'] = list(stats['business_cases'])
        stats['components'] = list(stats['components'])
        
        return stats
    
    def _create_flat_structure(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Create flat structure for JSON export."""
        print('[DEBUG] JSONHandler._create_flat_structure called')
        
        clips = []
        for record in data:
            clip = {
                'id': record.get('transaction_id'),
                'clip_id': record.get('clip_id'),
                'alias': record.get('alias'),
                'content': record.get('content'),
                'timestamp': record.get('timestamp'),
                'business_case': record.get('bus_case'),
                'component': record.get('bus_component'),
                'business_case_id': record.get('more_bus_id'),
                'component_id': record.get('more_comp_id')
            }
            clips.append(clip)
        
        return clips
    
    def _create_hierarchical_structure(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Create hierarchical structure grouped by business cases and components."""
        print('[DEBUG] JSONHandler._create_hierarchical_structure called')
        
        structure = {
            'business_cases': {},
            'unassigned_clips': []
        }
        
        for record in data:
            clip = {
                'id': record.get('transaction_id'),
                'clip_id': record.get('clip_id'),
                'alias': record.get('alias'),
                'content': record.get('content'),
                'timestamp': record.get('timestamp')
            }
            
            bus_case = record.get('bus_case')
            component = record.get('bus_component')
            
            if bus_case:
                # Initialize business case if not exists
                if bus_case not in structure['business_cases']:
                    structure['business_cases'][bus_case] = {
                        'id': record.get('more_bus_id'),
                        'name': bus_case,
                        'components': {},
                        'direct_clips': []
                    }
                
                if component:
                    # Add to component
                    if component not in structure['business_cases'][bus_case]['components']:
                        structure['business_cases'][bus_case]['components'][component] = {
                            'id': record.get('more_comp_id'),
                            'name': component,
                            'clips': []
                        }
                    structure['business_cases'][bus_case]['components'][component]['clips'].append(clip)
                else:
                    # Add directly to business case
                    structure['business_cases'][bus_case]['direct_clips'].append(clip)
            else:
                # Unassigned clip
                structure['unassigned_clips'].append(clip)
        
        return structure
    
    def _json_serializer(self, obj):
        """Custom JSON serializer for special data types."""
        if isinstance(obj, datetime):
            return obj.isoformat()
        raise TypeError(f"Object of type {type(obj)} is not JSON serializable")
    
    def get_format_info(self) -> Dict[str, Any]:
        """Get information about this format handler."""
        return {
            'name': self.format_name,
            'extension': self.file_extension,
            'description': 'Complete structured export with metadata and relationships',
            'features': [
                'Complete data structure preservation',
                'Metadata and statistics',
                'Hierarchical organization option',
                'UTF-8 encoding support',
                'Pretty printing option'
            ],
            'config_options': {
                'indent': 'JSON indentation level (default: 2)',
                'include_metadata': 'Include export metadata (default: True)',
                'hierarchical': 'Group by business cases/components (default: False)',
                'pretty_print': 'Format for readability (default: True)'
            }
        }
