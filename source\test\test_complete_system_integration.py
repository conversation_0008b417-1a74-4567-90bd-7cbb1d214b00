#!/usr/bin/env python3
"""
Complete System Integration Test
Tests the entire export, backup, and import system working together.
"""

import unittest
import tempfile
import os
import sys
import json
import shutil
from pathlib import Path

# Add parent directories to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import using importlib to avoid keyword conflicts
import importlib.util

# Load export manager
spec = importlib.util.spec_from_file_location("export_manager", 
    os.path.join(os.path.dirname(os.path.dirname(__file__)), "export", "export_manager.py"))
export_manager_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(export_manager_module)

# Load backup manager
spec = importlib.util.spec_from_file_location("backup_manager", 
    os.path.join(os.path.dirname(os.path.dirname(__file__)), "backup", "backup_manager.py"))
backup_manager_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(backup_manager_module)

# Load import manager
spec = importlib.util.spec_from_file_location("import_manager", 
    os.path.join(os.path.dirname(os.path.dirname(__file__)), "import", "import_manager.py"))
import_manager_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(import_manager_module)

# Extract classes
ExportManager = export_manager_module.ExportManager
BackupManager = backup_manager_module.BackupManager
ImportManager = import_manager_module.ImportManager
ImportConfig = import_manager_module.ImportConfig


class TestCompleteSystemIntegration(unittest.TestCase):
    """Test complete system integration across export, backup, and import."""
    
    def setUp(self):
        """Set up test environment."""
        print('[DEBUG] TestCompleteSystemIntegration.setUp called')
        
        # Create temporary directory for all test operations
        self.temp_dir = tempfile.mkdtemp()
        
        # Initialize managers
        self.export_manager = ExportManager()
        self.backup_manager = BackupManager()
        self.import_manager = ImportManager()
        
        # Sample test data representing a complete ClipsMore dataset
        self.test_data = [
            {
                'clip_id': 1,
                'transaction_id': 101,
                'alias': 'project_setup',
                'content': 'npm install express mongoose dotenv',
                'timestamp': '2024-01-01T09:00:00',
                'bus_case': 'Web Development Project',
                'bus_component': 'Backend Setup',
                'more_bus_id': 1,
                'more_comp_id': 1,
                'created_date': '2024-01-01T09:00:00',
                'modified_date': '2024-01-01T09:00:00',
                'tree_position': 0
            },
            {
                'clip_id': 2,
                'transaction_id': 102,
                'alias': 'database_config',
                'content': 'mongoose.connect(process.env.MONGODB_URI)',
                'timestamp': '2024-01-01T09:15:00',
                'bus_case': 'Web Development Project',
                'bus_component': 'Database Configuration',
                'more_bus_id': 1,
                'more_comp_id': 2,
                'created_date': '2024-01-01T09:15:00',
                'modified_date': '2024-01-01T09:15:00',
                'tree_position': 1
            },
            {
                'clip_id': 3,
                'transaction_id': 103,
                'alias': 'api_endpoint',
                'content': 'app.get("/api/users", async (req, res) => {\n  const users = await User.find();\n  res.json(users);\n});',
                'timestamp': '2024-01-01T09:30:00',
                'bus_case': 'Web Development Project',
                'bus_component': 'API Development',
                'more_bus_id': 1,
                'more_comp_id': 3,
                'created_date': '2024-01-01T09:30:00',
                'modified_date': '2024-01-01T09:30:00',
                'tree_position': 2
            },
            {
                'clip_id': 4,
                'transaction_id': 104,
                'alias': 'frontend_component',
                'content': 'const UserList = () => {\n  const [users, setUsers] = useState([]);\n  \n  useEffect(() => {\n    fetchUsers();\n  }, []);\n  \n  return <div>{users.map(user => <div key={user.id}>{user.name}</div>)}</div>;\n};',
                'timestamp': '2024-01-01T10:00:00',
                'bus_case': 'Web Development Project',
                'bus_component': 'Frontend Development',
                'more_bus_id': 1,
                'more_comp_id': 4,
                'created_date': '2024-01-01T10:00:00',
                'modified_date': '2024-01-01T10:00:00',
                'tree_position': 3
            },
            {
                'clip_id': 5,
                'transaction_id': 105,
                'alias': 'deployment_script',
                'content': 'docker build -t myapp .\ndocker run -p 3000:3000 myapp',
                'timestamp': '2024-01-01T11:00:00',
                'bus_case': 'DevOps Pipeline',
                'bus_component': 'Containerization',
                'more_bus_id': 2,
                'more_comp_id': 5,
                'created_date': '2024-01-01T11:00:00',
                'modified_date': '2024-01-01T11:00:00',
                'tree_position': 4
            }
        ]
    
    def tearDown(self):
        """Clean up test environment."""
        print('[DEBUG] TestCompleteSystemIntegration.tearDown called')
        
        # Clean up temporary files
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_complete_export_backup_import_cycle(self):
        """Test complete cycle: Export → Backup → Import."""
        print('[DEBUG] test_complete_export_backup_import_cycle called')
        
        # Step 1: Export data in multiple formats
        export_results = {}
        formats = ['json', 'csv', 'html', 'xml']
        
        for format_type in formats:
            export_path = os.path.join(self.temp_dir, f'test_export.{format_type}')

            # Use correct method signature: format_type, selection_criteria, output_path
            success = self.export_manager.export_data(
                format_type=format_type,
                selection_criteria={},  # Export all data
                output_path=export_path
            )

            self.assertTrue(success, f"Export failed for format: {format_type}")
            self.assertTrue(os.path.exists(export_path), f"Export file not created: {export_path}")

            export_results[format_type] = export_path
        
        # Step 2: Create backup using correct method signature
        backup_path = os.path.join(self.temp_dir, 'test_backup.zip')

        backup_success = self.backup_manager.create_backup(
            backup_path=backup_path,
            backup_config={'include_metadata': True}
        )
        
        self.assertTrue(backup_success, "Backup creation failed")
        self.assertTrue(os.path.exists(backup_path), "Backup file not created")
        
        # Step 3: Verify backup integrity
        verification_result = self.backup_manager.verify_backup(backup_path)
        self.assertTrue(verification_result['is_valid'], "Backup verification failed")
        
        # Step 4: Test import from JSON export
        json_export_path = export_results['json']
        
        # Preview import
        preview_result = self.import_manager.preview_import(json_export_path)
        
        self.assertEqual(preview_result['format'], 'json')
        self.assertEqual(preview_result['estimated_records'], len(self.test_data))
        self.assertGreater(len(preview_result['sample_data']), 0)
        
        # Perform import
        import_config = ImportConfig()
        import_config.duplicate_strategy = 'skip'
        
        import_result = self.import_manager.import_data(json_export_path, import_config)
        
        self.assertEqual(import_result['records_imported'], len(self.test_data))
        self.assertEqual(import_result['records_skipped'], 0)
        
        print('[DEBUG] Complete export-backup-import cycle successful')
    
    def test_format_compatibility_matrix(self):
        """Test compatibility between different export formats."""
        print('[DEBUG] test_format_compatibility_matrix called')
        
        compatibility_results = {}
        
        # Test each export format
        for format_type in ['json', 'csv']:  # Focus on importable formats
            export_path = os.path.join(self.temp_dir, f'compatibility_test.{format_type}')
            
            # Export data
            export_success = self.export_manager.export_data(
                format_type=format_type,
                selection_criteria={},
                output_path=export_path
            )
            
            self.assertTrue(export_success, f"Export failed for {format_type}")
            
            # Test import compatibility
            try:
                preview = self.import_manager.preview_import(export_path)
                import_config = ImportConfig()
                import_result = self.import_manager.import_data(export_path, import_config)
                
                compatibility_results[format_type] = {
                    'export_success': export_success,
                    'import_success': True,
                    'records_processed': import_result['records_imported'],
                    'data_integrity': import_result['records_imported'] == len(self.test_data)
                }
                
            except Exception as e:
                compatibility_results[format_type] = {
                    'export_success': export_success,
                    'import_success': False,
                    'error': str(e)
                }
        
        # Verify all tested formats are compatible
        for format_type, result in compatibility_results.items():
            self.assertTrue(result['export_success'], f"Export failed for {format_type}")
            self.assertTrue(result['import_success'], f"Import failed for {format_type}")
            if 'data_integrity' in result:
                self.assertTrue(result['data_integrity'], f"Data integrity failed for {format_type}")
        
        print(f'[DEBUG] Format compatibility verified: {list(compatibility_results.keys())}')
    
    def test_backup_restore_data_integrity(self):
        """Test complete backup and restore with data integrity verification."""
        print('[DEBUG] test_backup_restore_data_integrity called')
        
        # Create backup
        backup_path = os.path.join(self.temp_dir, 'integrity_test_backup.zip')
        
        backup_success = self.backup_manager.create_backup(
            backup_path=backup_path,
            backup_config={'include_metadata': True}
        )
        
        self.assertTrue(backup_success, "Backup creation failed")
        
        # Verify backup
        verification = self.backup_manager.verify_backup(backup_path)
        self.assertTrue(verification['is_valid'], "Backup verification failed")
        
        # Extract and verify data integrity
        extracted_data = self.backup_manager.extract_backup_data(backup_path)
        
        self.assertEqual(len(extracted_data), len(self.test_data), "Data count mismatch")
        
        # Verify each record
        for i, (original, extracted) in enumerate(zip(self.test_data, extracted_data)):
            self.assertEqual(original['clip_id'], extracted['clip_id'], f"Clip ID mismatch at index {i}")
            self.assertEqual(original['alias'], extracted['alias'], f"Alias mismatch at index {i}")
            self.assertEqual(original['content'], extracted['content'], f"Content mismatch at index {i}")
            self.assertEqual(original['bus_case'], extracted['bus_case'], f"Business case mismatch at index {i}")
        
        print('[DEBUG] Backup-restore data integrity verified')
    
    def test_error_handling_and_recovery(self):
        """Test system behavior under error conditions."""
        print('[DEBUG] test_error_handling_and_recovery called')
        
        # Test export to invalid path
        invalid_export_path = "/invalid/path/test.json"
        export_success = self.export_manager.export_data(
            format_type='json',
            selection_criteria={},
            output_path=invalid_export_path
        )
        self.assertFalse(export_success, "Export should fail for invalid path")
        
        # Test backup to invalid path
        invalid_backup_path = "/invalid/path/backup.zip"
        backup_success = self.backup_manager.create_backup(
            backup_path=invalid_backup_path
        )
        self.assertFalse(backup_success, "Backup should fail for invalid path")
        
        # Test import of non-existent file
        non_existent_file = os.path.join(self.temp_dir, "nonexistent.json")
        
        with self.assertRaises(Exception):
            self.import_manager.preview_import(non_existent_file)
        
        # Test import of invalid format file
        invalid_file = os.path.join(self.temp_dir, "invalid.json")
        with open(invalid_file, 'w') as f:
            f.write("invalid json content {")
        
        with self.assertRaises(Exception):
            self.import_manager.preview_import(invalid_file)
        
        print('[DEBUG] Error handling tests completed')


def run_complete_integration_tests():
    """Run complete system integration tests."""
    print("🧪 Running Complete System Integration Tests")
    print("=" * 60)

    # Create test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()

    # Add test cases
    suite.addTests(loader.loadTestsFromTestCase(TestCompleteSystemIntegration))

    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)

    # Print summary
    print("\n" + "=" * 60)
    print("📊 COMPLETE INTEGRATION TEST SUMMARY")
    print("=" * 60)

    total_tests = result.testsRun
    failures = len(result.failures)
    errors = len(result.errors)
    passed = total_tests - failures - errors

    print(f"Total Tests: {total_tests}")
    print(f"Passed: {passed}")
    print(f"Failed: {failures}")
    print(f"Errors: {errors}")

    if failures == 0 and errors == 0:
        print("🎉 ALL INTEGRATION TESTS PASSED!")
        print("✅ Export, Backup, and Import systems are fully integrated and working correctly!")
        return True
    else:
        print("❌ SOME INTEGRATION TESTS FAILED!")
        return False


if __name__ == "__main__":
    run_complete_integration_tests()
