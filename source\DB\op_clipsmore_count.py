import os, sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


import sqlite3
from typing import Optional, Dict, Any, List, Tuple
from .op_clipsmore_tbl import ClipsMoreTableOperations, ClipError as ClipsMoreError
from .db_connection import ConnectionPoolManager

class ClipsMoreCountOperations(ClipsMoreTableOperations):
    """Extended operations for counting clips associated with business cases and components.

    Inherits:
        ClipsMoreTableOperations: Base database operations class
    """
    
    def __init__(self):
        print('[DEBUG] ClipsMoreCountOperations.__init__ called')
        
    def count_clips_for_business(self, more_bus_id: int) -> int:
        """Count clips associated with a business case with validation.

        Args:
            more_bus_id: Positive integer > 0 identifying business case

        Returns:
            int: Number of unique clips associated with business case

        Raises:
            ClipsMoreError: Database operation failed or invalid ID

        Example:
            >>> counter = ClipsMoreCountOperations()
            >>> counter.count_clips_for_business(123)
            42
        """
        print(f'[DEBUG] count_clips_for_business called with more_bus_id={more_bus_id}')
        self._validate_ids(more_bus_id=more_bus_id)
        
        try:
            with ConnectionPoolManager().get_connection() as conn:
                query = """
                    SELECT COUNT(DISTINCT clip_id) 
                    FROM clipsmore_tbl 
                    WHERE more_bus_id = ?
                """
                print(f'[DEBUG] Executing query: {query}')
                cursor = conn.cursor()
                cursor.execute(query, (more_bus_id,))
                result = cursor.fetchone()
                print(f'[DEBUG] Query result: {result}')
                
                return result[0] if result else 0
                
        except sqlite3.Error as e:
            raise ClipsMoreError(f"Database error: {str(e)}")
    
    def count_clips_for_component(self, more_comp_id: int) -> int:
        """Count clips associated with a component with validation.

        Args:
            more_comp_id: Positive integer > 0 identifying component

        Returns:
            int: Number of unique clips associated with component

        Raises:
            ClipsMoreError: Database operation failed or invalid ID

        Example:
            >>> counter = ClipsMoreCountOperations()
            >>> counter.count_clips_for_component(456)
            15
        """
        print(f'[DEBUG] count_clips_for_component called with more_comp_id={more_comp_id}')
        self._validate_ids(more_comp_id=more_comp_id)
        
        try:
            with ConnectionPoolManager().get_connection() as conn:
                query = """
                    SELECT COUNT(DISTINCT clip_id) 
                    FROM clipsmore_tbl 
                    WHERE more_comp_id = ?
                """
                print(f'[DEBUG] Executing query: {query}')
                cursor = conn.cursor()
                cursor.execute(query, (more_comp_id,))
                result = cursor.fetchone()
                print(f'[DEBUG] Query result: {result}')
                
                return result[0] if result else 0
                
        except sqlite3.Error as e:
            raise ClipsMoreError(f"Database error: {str(e)}")
# NOTE: All new code should include debug print statements at the start of every function/method.