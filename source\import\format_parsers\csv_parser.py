#!/usr/bin/env python3
"""
CSV Parser for ClipsMore Import System
Handles parsing CSV files for import operations.
"""

import csv
import os
from typing import Dict, List, Any, Optional
from pathlib import Path


class CSVParseError(Exception):
    """Custom exception for CSV parsing errors."""
    pass


class CSVParser:
    """
    CSV format parser for import operations.
    Handles various CSV dialects and formats.
    """
    
    def __init__(self):
        """Initialize the CSV parser."""
        print('[DEBUG] CSVParser.__init__ called')
        self.supported_dialects = ['excel', 'excel-tab', 'unix']
    
    def detect_dialect(self, file_path: str) -> str:
        """
        Detect the CSV dialect of the file.
        
        Args:
            file_path: Path to the CSV file
            
        Returns:
            Detected dialect name
        """
        print(f'[DEBUG] CSVParser.detect_dialect called for {file_path}')
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                # Read first few lines to detect dialect
                sample = f.read(1024)
                
            sniffer = csv.Sniffer()
            dialect = sniffer.sniff(sample)
            
            # Map to known dialect names
            if dialect.delimiter == '\t':
                detected = 'excel-tab'
            elif dialect.delimiter == ',':
                detected = 'excel'
            else:
                detected = 'unix'
            
            print(f'[DEBUG] Detected CSV dialect: {detected}')
            return detected
            
        except Exception as e:
            print(f'[DEBUG] Error detecting dialect, using default: {e}')
            return 'excel'  # Default fallback
    
    def detect_encoding(self, file_path: str) -> str:
        """
        Detect the file encoding.
        
        Args:
            file_path: Path to the CSV file
            
        Returns:
            Detected encoding
        """
        print(f'[DEBUG] CSVParser.detect_encoding called for {file_path}')
        
        # Try common encodings
        encodings = ['utf-8', 'utf-8-sig', 'latin-1', 'cp1252']
        
        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    f.read(1024)  # Try to read first 1KB
                print(f'[DEBUG] Detected encoding: {encoding}')
                return encoding
            except UnicodeDecodeError:
                continue
        
        print('[DEBUG] Using utf-8 as fallback encoding')
        return 'utf-8'  # Fallback
    
    def preview(self, file_path: str, config) -> Dict[str, Any]:
        """
        Preview CSV file contents without full parsing.
        
        Args:
            file_path: Path to the CSV file
            config: Import configuration
            
        Returns:
            Dictionary containing preview information
        """
        print(f'[DEBUG] CSVParser.preview called for {file_path}')
        
        try:
            dialect = self.detect_dialect(file_path)
            encoding = self.detect_encoding(file_path)
            
            with open(file_path, 'r', encoding=encoding) as f:
                reader = csv.DictReader(f, dialect=dialect)
                
                # Get field names
                fieldnames = reader.fieldnames or []
                
                # Read sample records
                sample_records = []
                record_count = 0
                
                for i, row in enumerate(reader):
                    record_count += 1
                    if i < 5:  # Get first 5 records for preview
                        sample_records.append(dict(row))
                    elif i >= 100:  # Don't count all records for large files
                        break
                
                # Detect field types from sample data
                detected_fields = {}
                if sample_records:
                    first_record = sample_records[0]
                    for field, value in first_record.items():
                        detected_fields[field] = {
                            'type': self._detect_field_type(value),
                            'sample_value': str(value)[:50] if value else ''
                        }
                
                return {
                    'record_count': record_count,
                    'sample_records': sample_records,
                    'detected_fields': detected_fields,
                    'duplicate_count': 0,  # Would need duplicate detection
                    'warnings': [],
                    'fieldnames': fieldnames,
                    'dialect': dialect,
                    'encoding': encoding
                }
                
        except Exception as e:
            raise CSVParseError(f"Error previewing CSV file: {e}")
    
    def _detect_field_type(self, value: str) -> str:
        """Detect the likely data type of a field value."""
        if not value or value.strip() == '':
            return 'string'
        
        # Try to detect numeric types
        try:
            int(value)
            return 'integer'
        except ValueError:
            pass
        
        try:
            float(value)
            return 'float'
        except ValueError:
            pass
        
        # Check for boolean-like values
        if value.lower() in ['true', 'false', 'yes', 'no', '1', '0']:
            return 'boolean'
        
        # Check for date-like patterns (basic)
        if '-' in value and len(value) >= 8:
            return 'date'
        
        return 'string'
    
    def parse(self, file_path: str, config) -> List[Dict[str, Any]]:
        """
        Parse CSV file and return structured data.
        
        Args:
            file_path: Path to the CSV file
            config: Import configuration
            
        Returns:
            List of dictionaries containing parsed records
        """
        print(f'[DEBUG] CSVParser.parse called for {file_path}')
        
        try:
            dialect = self.detect_dialect(file_path)
            encoding = self.detect_encoding(file_path)
            
            parsed_records = []
            
            with open(file_path, 'r', encoding=encoding) as f:
                reader = csv.DictReader(f, dialect=dialect)
                
                for row in reader:
                    # Convert row to dict and clean up
                    record = dict(row)
                    
                    # Apply field mapping if configured
                    mapped_record = self._apply_field_mapping(record, config.field_mapping)
                    
                    # Apply data type conversions
                    converted_record = self._convert_data_types(mapped_record)
                    
                    # Validate record
                    if self._validate_record(converted_record, config.validation_rules):
                        parsed_records.append(converted_record)
            
            return parsed_records
            
        except Exception as e:
            raise CSVParseError(f"Error parsing CSV file: {e}")
    
    def _apply_field_mapping(self, record: Dict[str, Any], field_mapping: Dict[str, str]) -> Dict[str, Any]:
        """Apply field mapping to a record."""
        if not field_mapping:
            return record
        
        mapped_record = {}
        for source_field, target_field in field_mapping.items():
            if source_field in record:
                mapped_record[target_field] = record[source_field]
        
        # Include unmapped fields
        for field, value in record.items():
            if field not in field_mapping and field not in mapped_record:
                mapped_record[field] = value
        
        return mapped_record
    
    def _convert_data_types(self, record: Dict[str, Any]) -> Dict[str, Any]:
        """Convert string values to appropriate data types."""
        converted_record = {}
        
        for field, value in record.items():
            if isinstance(value, str):
                # Try to convert to appropriate type
                converted_value = self._convert_value(value)
                converted_record[field] = converted_value
            else:
                converted_record[field] = value
        
        return converted_record
    
    def _convert_value(self, value: str) -> Any:
        """Convert a string value to its appropriate data type."""
        if not value or value.strip() == '':
            return None
        
        value = value.strip()
        
        # Try integer
        try:
            return int(value)
        except ValueError:
            pass
        
        # Try float
        try:
            return float(value)
        except ValueError:
            pass
        
        # Try boolean
        if value.lower() in ['true', 'yes', '1']:
            return True
        elif value.lower() in ['false', 'no', '0']:
            return False
        
        # Return as string
        return value
    
    def _validate_record(self, record: Dict[str, Any], validation_rules: Dict[str, Any]) -> bool:
        """Validate a record against validation rules."""
        if not validation_rules:
            return True
        
        # Check required fields
        required_fields = validation_rules.get('required_fields', [])
        for field in required_fields:
            if field not in record or record[field] is None:
                return False
        
        return True
    
    def get_supported_extensions(self) -> List[str]:
        """Get list of supported file extensions."""
        return ['.csv', '.tsv', '.txt']
    
    def get_format_description(self) -> str:
        """Get human-readable format description."""
        return "CSV (Comma-Separated Values) files"
