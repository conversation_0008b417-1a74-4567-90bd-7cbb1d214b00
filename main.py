import os, sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


import tkinter as tk
from source.ui_manager import UIManager

def main():
    print('[DEBUG] main() called')
    root = tk.Tk()
    app = UIManager(root)

    # Set up cleanup on window close
    def on_closing():
        print('[DEBUG] Application closing')
        app.cleanup()
        root.destroy()

    root.protocol("WM_DELETE_WINDOW", on_closing)
    root.mainloop()

if __name__ == "__main__":
    print('[DEBUG] __main__ entry point')
    main()

# NOTE: All new code should include debug print statements at the start of every function/method.