import os, sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import tkinter as tk
from tkinter import ttk
from tkinter import messagebox

# NOTE: All new code should include debug print statements at the start of every function/method.

from source.utils.scroll_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>
from source.utils.theme_manager import ThemeManager
from source.utils.utility_manager import UtilityManager
from source.utils.clip_manager import ClipManager
from source.utils.tree_manager import TreeManager
from source.utils.validation_manager import ValidationManager
from source.utils.database_manager import DatabaseManager
from source.utils.event_manager import EventManager
from source.utils.reward_manager import RewardManager
from source.utils.documentation_manager import DocumentationManager
from source.utils.debug_manager import DebugManager
from source.utils.ui_layout_manager import UILayoutManager
from source.utils.drag_drop_manager import DragDropManager
from source.utils.business_logic_manager import BusinessLogicManager
from source.utils.clipboard_monitor import ClipboardMonitor
from source.utils.undo_manager import UndoManager
from source.utils.ui_setup_manager import UISetupManager
from source.utils.tab_initialization_manager import TabInitializationManager
from source.utils.keyboard_manager import KeyboardManager
from source.utils.cleanup_manager import CleanupManager
from source.utils.more_tab_manager import MoreTabManager
from source.utils.reward_system_manager import RewardSystemManager
from source.utils.notification_manager import NotificationManager

# New refactored utility classes
from source.utils.ui_initialization_helper import UIInitializationHelper
from source.utils.ui_event_handler import UIEventHandler
from source.utils.ui_dialog_manager import UIDialogManager
from source.utils.ui_clipboard_utility import UIClipboardUtility
from source.utils.ui_state_manager import UIStateManager

class UIManager:
    def __init__(self, root):
        print('[DEBUG] UIManager.__init__ called')
        self.root = root

        # Initialize new utility managers
        self.ui_setup_manager = UISetupManager(root)
        self.keyboard_manager = KeyboardManager(root)
        self.cleanup_manager = CleanupManager()

        # Initialize core managers using setup manager
        self.theme_manager = ThemeManager(root)
        self.database_manager = DatabaseManager()
        self.validation_manager = ValidationManager(self.database_manager)

        # Initialize notification manager early so it's available to other managers
        self.notification_manager = NotificationManager(root, self.theme_manager)

        # Register core managers with setup manager
        self.ui_setup_manager.initialize_core_managers(
            self.theme_manager,
            self.database_manager,
            self.validation_manager
        )

        # Initialize UI layout manager
        self.ui_layout_manager = UILayoutManager(root, self.theme_manager)
        layout_components = self.ui_setup_manager.initialize_layout_manager(self.ui_layout_manager)
        self.main_frame = layout_components['main_frame']
        self.top_frame = layout_components['top_frame']

        # Create control buttons using setup manager
        self.theme_button, self.undo_button = self.ui_setup_manager.create_control_buttons(
            self.ui_layout_manager,
            self.toggle_theme,
            self.undo_last_action
        )

        # Initialize new utility classes
        self.ui_initialization_helper = UIInitializationHelper(self.root, self.theme_manager)
        self.ui_dialog_manager = UIDialogManager(self.root, self.database_manager, self.theme_manager, self.notification_manager)
        self.ui_clipboard_utility = UIClipboardUtility(self.root, self.database_manager, self.ui_dialog_manager)
        self.ui_state_manager = UIStateManager(self)
        self.ui_event_handler = UIEventHandler(self)

        # Add top-level debug button using helper
        self._add_top_level_debug_button()

        # Add export and backup buttons to top frame using helper
        self._add_export_backup_buttons()

        # Create tab control using setup manager
        self.tab_control, tabs = self.ui_setup_manager.create_tab_control(self.ui_layout_manager)
        self.clips_tab = tabs['clips_tab']
        self.more_tab = tabs['more_tab']
        self.about_tab = tabs['about_tab']

        # Setup responsive layout using setup manager
        self.ui_setup_manager.setup_responsive_layout(self.ui_layout_manager)

        # Initialize managers that will be set later
        self.reward_manager = RewardManager(root)
        self.reward_system_manager = None  # Will be initialized after tabs are created
        self.event_manager = None
        self.documentation_manager = None
        self.debug_manager = None
        self.drag_drop_manager = None
        self.business_logic_manager = None
        self.clipboard_monitor = None

        # Initialize export and backup managers
        self.export_manager = None
        self.backup_manager = None

        # Initialize undo manager using setup manager
        self.undo_manager = self.ui_setup_manager.initialize_undo_manager(UndoManager)

        # Initialize scroll handler using setup manager
        self.scroll_handler = self.ui_setup_manager.initialize_scroll_handler(ScrollHandler, self.tab_control)
        self.scroll_handler.set_ui_manager(self)

        # Get theme colors for backward compatibility
        self._update_theme_properties()

        # Initialize tab content using new tab initialization manager
        self.tab_initialization_manager = TabInitializationManager(
            self.theme_manager,
            self.database_manager,
            self.validation_manager
        )

        self._initialize_tabs()
        self._initialize_post_tab_managers()
        self._setup_application_bindings()
        self._register_cleanup_handlers()

    def _initialize_tabs(self):
        """Initialize all tab content using TabInitializationManager"""
        print('[DEBUG] UIManager._initialize_tabs called')

        try:
            # Initialize clips tab
            self.clip_manager = self.tab_initialization_manager.initialize_clips_tab(
                self.clips_tab, ClipManager, self
            )
            # Pass notification manager to clip manager
            if hasattr(self.clip_manager, 'set_notification_manager'):
                self.clip_manager.set_notification_manager(self.notification_manager)

            # Initialize about tab
            self.documentation_manager = self.tab_initialization_manager.initialize_about_tab(
                self.about_tab, DocumentationManager
            )

            # Initialize more tab using MoreTabManager
            self.more_tab_manager = MoreTabManager(self.theme_manager, self.database_manager, self.validation_manager)
            more_tab_components = self.more_tab_manager.initialize_more_tab(
                self.more_tab, TreeManager, DragDropManager, BusinessLogicManager, self
            )

            # Extract managers from more tab components
            self.tree_manager = more_tab_components['tree_manager']
            self.drag_drop_manager = more_tab_components['drag_drop_manager']
            self.business_logic_manager = more_tab_components['business_logic_manager']
            self.tree = more_tab_components['tree']

            # Pass notification manager to business logic manager
            if hasattr(self.business_logic_manager, 'set_notification_manager'):
                self.business_logic_manager.set_notification_manager(self.notification_manager)

            # Store UI elements for backward compatibility
            more_ui_elements = more_tab_components['ui_elements']
            self.bus_entry_var = more_ui_elements['bus_entry_var']
            self.comp_entry_var = more_ui_elements['comp_entry_var']
            self.more_error_label = more_ui_elements['more_error_label']
            self.add_component_btn = more_ui_elements['add_component_btn']
            self.search_var = more_ui_elements['search_var']

            # Initialize reward system manager for the more tab
            self.reward_system_manager = RewardSystemManager(self.more_tab)

            print('[DEBUG] All tabs initialized successfully')

        except Exception as e:
            print(f'[ERROR] Failed to initialize tabs: {e}')
            raise

    def _initialize_post_tab_managers(self):
        """Initialize managers that depend on tab content being created"""
        print('[DEBUG] UIManager._initialize_post_tab_managers called')

        try:
            # Bind tree selection event
            if hasattr(self, 'tree') and self.tree:
                self.tree.bind('<<TreeviewSelect>>', self._on_treeview_select)

            # Initialize EventManager after UI components are created
            self._initialize_event_manager()

            # Initialize DebugManager after all other managers are ready
            self._initialize_debug_manager()

            # Initialize and start clipboard monitoring
            self._initialize_clipboard_monitor()

            print('[DEBUG] Post-tab managers initialized successfully')

        except Exception as e:
            print(f'[ERROR] Failed to initialize post-tab managers: {e}')

    def _setup_application_bindings(self):
        """Setup application-wide keyboard bindings using KeyboardManager"""
        print('[DEBUG] UIManager._setup_application_bindings called')

        try:
            # Setup global bindings with tab switching, theme toggle, and search functionality
            self.keyboard_manager.setup_global_bindings(
                undo_callback=self.undo_last_action,
                tab_manager=self,  # Pass self as tab manager since we have tab switching methods
                theme_manager=self.theme_manager,
                ui_manager=self
            )
            print('[DEBUG] Application bindings setup successfully')

        except Exception as e:
            print(f'[ERROR] Failed to setup application bindings: {e}')

    def switch_to_tab(self, tab_index: int) -> bool:
        """Switch to a specific tab by index using UIEventHandler."""
        return self.ui_event_handler.handle_tab_switching(tab_index)

    def get_current_tab_name(self) -> str:
        """Get the name of the currently active tab using UIEventHandler."""
        return self.ui_event_handler.get_current_tab_name()

    def _register_cleanup_handlers(self):
        """Register cleanup handlers with CleanupManager"""
        print('[DEBUG] UIManager._register_cleanup_handlers called')

        try:
            # Register clipboard monitor cleanup
            self.cleanup_manager.register_cleanup_handler(
                lambda: self.cleanup_manager.cleanup_clipboard_monitor(self.clipboard_monitor),
                'clipboard_monitor_cleanup'
            )

            # Register reward system manager cleanup
            if self.reward_system_manager:
                self.cleanup_manager.register_manager_for_cleanup(
                    self.reward_system_manager, 'reward_system_manager'
                )

            print('[DEBUG] Cleanup handlers registered successfully')

        except Exception as e:
            print(f'[ERROR] Failed to register cleanup handlers: {e}')

    def _update_theme_properties(self):
        """Update theme properties from theme manager for backward compatibility"""
        print('[DEBUG] UIManager._update_theme_properties called')
        colors = self.theme_manager.get_theme_colors()
        self.dark_mode = colors['dark_mode']
        self.bg_color = colors['bg_color']
        self.fg_color = colors['fg_color']
        self.entry_bg = colors['entry_bg']
        self.entry_fg = colors['entry_fg']
        self.button_bg = colors['button_bg']
        self.button_fg = colors['button_fg']
        self.tree_bg = colors['tree_bg']
        self.tree_fg = colors['tree_fg']
        self.tree_select = colors['tree_select']

    def _initialize_event_manager(self):
        """Initialize EventManager with UI component references"""
        print('[DEBUG] UIManager._initialize_event_manager called')

        try:
            # Prepare UI components dictionary for EventManager
            ui_components = {
                'tree': getattr(self, 'tree', None),
                'tree_manager': self.tree_manager,
                'clip_manager': self.clip_manager,
                'database_manager': self.database_manager,
                'theme_manager': self.theme_manager,
                'validation_manager': self.validation_manager
            }

            # Initialize EventManager with reference to UIManager
            self.event_manager = EventManager(ui_components, ui_manager=self)

            # Bind events if tree is available
            if hasattr(self, 'tree') and self.tree:
                self.event_manager.bind_events()
                print('[DEBUG] EventManager events bound successfully')
            else:
                print('[WARNING] Tree not available for event binding')

        except Exception as e:
            print(f'[ERROR] Failed to initialize EventManager: {e}')

    def _initialize_debug_manager(self):
        """Initialize DebugManager with manager references"""
        print('[DEBUG] UIManager._initialize_debug_manager called')

        try:
            # Initialize DebugManager with required manager references
            self.debug_manager = DebugManager(
                self.database_manager,
                self.clip_manager,
                self.tree_manager
            )

            # Pass notification manager to debug manager
            if hasattr(self.debug_manager, 'set_notification_manager'):
                self.debug_manager.set_notification_manager(self.notification_manager)

            print('[DEBUG] DebugManager initialized successfully')

        except Exception as e:
            print(f'[ERROR] Failed to initialize DebugManager: {e}')

    def _initialize_clipboard_monitor(self):
        """Initialize and start clipboard monitoring"""
        print('[DEBUG] UIManager._initialize_clipboard_monitor called')

        try:
            # Initialize clipboard monitor with callback to refresh clips
            self.clipboard_monitor = ClipboardMonitor(
                root=self.root,
                database_manager=self.database_manager,
                callback=self._on_new_clip_detected,
                check_interval=1.0  # Check every second
            )

            # Start monitoring
            self.clipboard_monitor.start_monitoring()
            print('[DEBUG] Clipboard monitoring initialized and started')

        except Exception as e:
            print(f'[ERROR] Failed to initialize clipboard monitor: {e}')

    def _on_new_clip_detected(self, content: str):
        """Callback function called when new clipboard content is detected"""
        print(f'[DEBUG] UIManager._on_new_clip_detected called with {len(content)} characters')

        try:
            # Refresh clips display to show new clip
            if self.clip_manager:
                # Use after_idle to ensure UI update happens on main thread
                self.root.after_idle(self.clip_manager.load_clips)
                print('[DEBUG] Scheduled clips refresh after new clip detection')
            else:
                print('[WARNING] ClipManager not available for refresh')

        except Exception as e:
            print(f'[ERROR] Error handling new clip detection: {e}')

    def _add_top_level_debug_button(self):
        """Add a top-level debug button for loading all test data using UIInitializationHelper."""
        print('[DEBUG] UIManager._add_top_level_debug_button called')

        try:
            debug_button = self.ui_initialization_helper.create_top_level_debug_button(
                self.top_frame,
                self._debug_load_all_test_data
            )
            if debug_button:
                print('[DEBUG] Top-level debug button added successfully')
            else:
                print('[WARNING] Failed to create debug button')
        except Exception as e:
            print(f'[ERROR] Failed to add top-level debug button: {e}')

    def _add_export_backup_buttons(self):
        """Add export and backup buttons to the top frame using UIInitializationHelper."""
        print('[DEBUG] UIManager._add_export_backup_buttons called')

        try:
            buttons = self.ui_initialization_helper.create_export_backup_buttons(
                self.top_frame,
                self.open_export_dialog,
                self.open_backup_dialog,
                self.clear_all_data
            )

            if buttons:
                # Store references for potential theme updates
                self.export_button = buttons['export_button']
                self.backup_button = buttons['backup_button']
                self.clear_all_button = buttons['clear_all_button']
                print('[DEBUG] Export, backup, and clear all buttons added to top frame successfully')
            else:
                print('[WARNING] Failed to create export/backup buttons')

        except Exception as e:
            print(f'[ERROR] Failed to add export and backup buttons: {e}')

    def _debug_load_all_test_data(self):
        """Load all cybersecurity test data (clips, business cases, components, and transactions)."""
        print('[DEBUG] UIManager._debug_load_all_test_data called')

        if self.debug_manager:
            self.debug_manager.load_test_clips()  # This now loads everything
        else:
            print('[WARNING] DebugManager not initialized')

    def undo_last_action(self):
        """Undo the last user action using UIStateManager"""
        print('[DEBUG] UIManager.undo_last_action called')
        return self.ui_state_manager.handle_undo_action()





    def cleanup(self):
        """Cleanup resources when application is closing using CleanupManager"""
        print('[DEBUG] UIManager.cleanup called')

        try:
            # Execute all registered cleanup operations
            self.cleanup_manager.execute_cleanup(self.root)
            print('[DEBUG] Cleanup completed successfully')

        except Exception as e:
            print(f'[ERROR] Error during cleanup: {e}')

    def toggle_theme(self):
        """Toggle between light and dark mode using UIStateManager"""
        print('[DEBUG] UIManager.toggle_theme called')
        self.ui_state_manager.toggle_theme()

    def update_tab_colors(self):
        """Update colors for all tab content using UIStateManager"""
        print('[DEBUG] UIManager.update_tab_colors called')
        self.ui_state_manager.update_tab_colors()

    def load_enhanced_clips(self):
        print('[DEBUG] UIManager.load_enhanced_clips called')
        """Load and display clips using ClipManager"""
        if self.clip_manager:
            self.clip_manager.load_clips()
        else:
            print('[WARNING] ClipManager not initialized')

    def _limit_entry(self, var, maxlen):
        print('[DEBUG] UIManager._limit_entry called')
        UtilityManager.limit_entry_length(var, maxlen)

    # Legacy alias button methods moved to ClipManager

    def add_business_case(self):
        """Add business case using BusinessLogicManager"""
        print('[DEBUG] UIManager.add_business_case called')

        if self.business_logic_manager:
            result = self.business_logic_manager.add_business_case()
            # Auto-refresh Clips tab assignment dropdown if business case was created
            if result and self.clip_manager:
                print('[DEBUG] Auto-refreshing Clips tab after business case creation')
                self.clip_manager.load_clips()
        else:
            print('[WARNING] BusinessLogicManager not initialized')

    def cud_component(self):
        """Handle component CUD operations using BusinessLogicManager"""
        print('[DEBUG] UIManager.cud_component called')

        if self.business_logic_manager:
            self.business_logic_manager.handle_component_operations()
        else:
            print('[WARNING] BusinessLogicManager not initialized')

    def open_export_dialog(self):
        """Open the export dialog using UIDialogManager"""
        print('[DEBUG] UIManager.open_export_dialog called')
        self.ui_dialog_manager.open_export_dialog()

    def open_backup_dialog(self):
        """Open the backup dialog using UIDialogManager"""
        print('[DEBUG] UIManager.open_backup_dialog called')
        self.ui_dialog_manager.open_backup_dialog()

    def edit_selected(self):
        """Edit selected item using BusinessLogicManager"""
        print('[DEBUG] UIManager.edit_selected called')

        if self.business_logic_manager:
            self.business_logic_manager.edit_selected_item()
        else:
            print('[WARNING] BusinessLogicManager not initialized')

    def delete_selected(self):
        """Delete selected item using BusinessLogicManager"""
        print('[DEBUG] UIManager.delete_selected called')

        if self.business_logic_manager:
            self.business_logic_manager.delete_selected_item()
        else:
            print('[WARNING] BusinessLogicManager not initialized')

    def refresh_tree(self):
        print('[DEBUG] UIManager.refresh_tree called')
        """Refresh tree using TreeManager"""
        if self.tree_manager:
            self.tree_manager.refresh_tree()
        else:
            print('[WARNING] TreeManager not initialized')

    def _create_clip_buttons_for_item(self, item_name, assignments, y_position):
        """Create clip buttons for a business case or component"""
        print(f'[DEBUG] UIManager._create_clip_buttons_for_item called for {item_name} with {len(assignments)} assignments')

        # Create a label for the item name
        item_label = tk.Label(self.buttons_scrollable_frame,
                             text=f"{item_name}:",
                             bg=self.bg_color, fg=self.fg_color,
                             font=('Arial', 10, 'bold'),
                             anchor='w')
        item_label.grid(row=y_position, column=0, sticky='w', padx=5, pady=(10, 2))
        y_position += 1

        # Create frame for buttons in this row
        buttons_frame = tk.Frame(self.buttons_scrollable_frame, bg=self.bg_color)
        buttons_frame.grid(row=y_position, column=0, sticky='w', padx=20, pady=2)

        # Create buttons for each assignment
        for i, assignment in enumerate(assignments):
            alias = assignment.get('alias', 'Unknown')

            # Calculate button width based on alias length (minimum 8, maximum 20)
            button_width = max(8, min(20, len(alias) + 2))

            # Create the clip button
            clip_button = tk.Button(buttons_frame,
                                  text=alias,
                                  command=lambda a=alias: self._copy_clip_by_alias(a),
                                  bg="#4CAF50", fg="white",
                                  activebackground="#45a049", activeforeground="white",
                                  font=('Arial', 9, 'bold'),
                                  width=button_width,
                                  relief='raised',
                                  bd=2,
                                  cursor='hand2')

            # Pack buttons close together, aligned left
            clip_button.pack(side=tk.LEFT, padx=2, pady=1)

            # Store button reference for management
            button_key = f"{item_name}_{alias}"
            self.clip_button_widgets[button_key] = clip_button

            print(f'[DEBUG] Created clip button: {alias} for {item_name}')

        return y_position + 1

    def _show_emoji_reward(self):
        """Show a fun emoji reward popup using RewardSystemManager"""
        print('[DEBUG] UIManager._show_emoji_reward called')

        if self.reward_system_manager:
            self.reward_system_manager.show_emoji_reward()
        else:
            print('[WARNING] RewardSystemManager not initialized')

    def _hide_emoji_reward(self):
        """Hide the emoji reward popup using RewardSystemManager"""
        print('[DEBUG] UIManager._hide_emoji_reward called')

        if self.reward_system_manager:
            self.reward_system_manager.hide_emoji_reward()
        else:
            print('[WARNING] RewardSystemManager not initialized')

    def filter_tree(self):
        print('[DEBUG] UIManager.filter_tree called')
        """Filter treeview using TreeManager"""
        if self.tree_manager and hasattr(self, 'search_var'):
            query = self.search_var.get()
            self.tree_manager.filter_tree(query)
        else:
            print('[WARNING] TreeManager not initialized or search_var not available')

    def on_tree_double_click(self, event):
        """Handle double-click on tree items using UIEventHandler"""
        print('[DEBUG] UIManager.on_tree_double_click called')
        self.ui_event_handler.handle_tree_double_click(event)

    def _copy_clip_by_alias(self, alias):
        """Copy clip content to clipboard by alias using UIClipboardUtility"""
        print(f'[DEBUG] UIManager._copy_clip_by_alias called with alias={alias}')
        self.ui_clipboard_utility.copy_clip_by_alias(alias)

    def on_tree_item_press(self, event):
        """Handle mouse press on tree item using UIEventHandler"""
        print('[DEBUG] UIManager.on_tree_item_press called')
        self.ui_event_handler.handle_tree_item_press(event)

    def on_tree_item_drag(self, event):
        """Handle drag motion using UIEventHandler"""
        self.ui_event_handler.handle_tree_item_drag(event)

    def on_tree_select(self, event):
        """Handle tree selection event using UIEventHandler"""
        print('[DEBUG] UIManager.on_tree_select called')
        self.ui_event_handler.handle_tree_select(event)

    def on_bus_entry_change(self, event=None):
        """Handle business case entry change using UIEventHandler"""
        print('[DEBUG] UIManager.on_bus_entry_change called')
        self.ui_event_handler.handle_bus_entry_change(event)

    def on_comp_entry_change(self, event=None):
        """Handle component entry change using UIEventHandler"""
        print('[DEBUG] UIManager.on_comp_entry_change called')
        self.ui_event_handler.handle_comp_entry_change(event)

    def create_tooltip(self, widget, text):
        """Create a tooltip for a widget using UtilityManager"""
        UtilityManager.create_tooltip(widget, text, self.theme_manager)

    def _validate_alias_realtime(self, alias, validation_label):
        """Validate alias in real-time using ValidationManager"""
        if self.validation_manager:
            self.validation_manager.validate_alias_realtime(alias, validation_label)
        else:
            print('[WARNING] ValidationManager not initialized')
            validation_label.config(text="?", fg="gray")

    def _copy_clip_to_clipboard(self, clip_id):
        """Copy clip content to OS clipboard using UIClipboardUtility"""
        print(f'[DEBUG] UIManager._copy_clip_to_clipboard called for clip_id={clip_id}')
        self.ui_clipboard_utility.copy_clip_to_clipboard(clip_id)

    def _get_or_generate_alias(self, clip_id, clip_content):
        """Get existing alias or generate new one for clip using DatabaseManager"""
        print(f'[DEBUG] UIManager._get_or_generate_alias called for clip_id={clip_id}')

        try:
            from utils.alias_generator import AliasGenerator

            # Get existing aliases for uniqueness checking
            existing_aliases = self.database_manager.get_existing_aliases()

            # Initialize alias generator
            generator = AliasGenerator()
            generator.set_existing_aliases(existing_aliases)

            # Generate intelligent alias
            alias = generator.generate_from_content(str(clip_content) if clip_content else "")

            print(f'[DEBUG] Generated alias: {alias}')
            return alias

        except Exception as e:
            print(f'[ERROR] Failed to generate alias: {e}')
            return f"clip_{clip_id}"

    def _populate_assignment_dropdown(self, combobox, clip_id=None):
        """Populate assignment dropdown with business cases and components using DatabaseManager"""
        print(f'[DEBUG] UIManager._populate_assignment_dropdown called for clip_id={clip_id}')

        try:
            more_ops = self.database_manager.get_more_operations()
            options = ["None"]  # Option for no assignment
            max_length = len("None")
            current_assignment = "None"  # Default to None

            # Get current assignment for this clip if clip_id is provided
            if clip_id:
                try:
                    enhanced_ops = self.database_manager.get_enhanced_operations()
                    assignments = enhanced_ops.get_assignments_by_clip(clip_id)
                    if assignments:
                        # Get the most recent assignment
                        assignment = assignments[0]
                        bc_name = assignment.get('business_case_name', '')
                        comp_name = assignment.get('component_name', '')

                        if comp_name:
                            current_assignment = f"BC: {bc_name} > {comp_name}"
                        elif bc_name:
                            current_assignment = f"BC: {bc_name}"

                        print(f'[DEBUG] Found current assignment: {current_assignment}')
                except Exception as e:
                    print(f'[WARNING] Failed to get current assignment for clip {clip_id}: {e}')

            # Get business cases
            business_cases = more_ops.read_all_business_cases()
            for bc in business_cases:
                bc_name = bc.get('name', '')
                bc_option = f"BC: {bc_name}"
                options.append(bc_option)
                max_length = max(max_length, len(bc_option))

                # Get components for this business case
                bc_id = bc.get('id')
                components = more_ops.read_components_for_business_case(bc_id)
                for comp in components:
                    comp_name = comp.get('name', '')
                    comp_option = f"BC: {bc_name} > {comp_name}"
                    options.append(comp_option)
                    max_length = max(max_length, len(comp_option))

            # Set combobox values and current selection
            combobox['values'] = options
            combobox.set(current_assignment)  # Set to current assignment or "None"
            UtilityManager.auto_size_dropdown(combobox, options)

            print(f'[DEBUG] Dropdown populated with {len(options)} options, current: {current_assignment}')

        except Exception as e:
            print(f'[ERROR] Failed to populate dropdown: {e}')
            combobox['values'] = ["None"]
            combobox.set("None")

    # Assignment logic moved to ClipManager

    def _delete_clip(self, clip_id):
        """Delete clip using ClipManager"""
        print(f'[DEBUG] UIManager._delete_clip called for clip_id={clip_id}')
        if self.clip_manager:
            self.clip_manager.delete_clip(clip_id)
        else:
            print('[WARNING] ClipManager not initialized')

    def clear_all_clips(self):
        """Clear all clips using ClipManager"""
        print('[DEBUG] UIManager.clear_all_clips called')
        if self.clip_manager:
            self.clip_manager.clear_all_clips()
        else:
            print('[WARNING] ClipManager not initialized')

    def clear_all_more_data(self):
        """Clear all business cases, components, and assignments using DebugManager"""
        print('[DEBUG] UIManager.clear_all_more_data called')
        if self.debug_manager:
            self.debug_manager.clear_all_more_data()
        else:
            print('[WARNING] DebugManager not initialized')

    def clear_all_data(self):
        """Clear ALL data from the database with confirmation using UIDialogManager"""
        print('[DEBUG] UIManager.clear_all_data called')

        try:
            # Show confirmation dialog using dialog manager
            if self.ui_dialog_manager.show_clear_all_data_confirmation():
                # Perform the truncation
                success = self.database_manager.truncate_all_tables("CONFIRM_TRUNCATE_ALL")

                if success:
                    # Refresh all UI components using state manager
                    self.ui_state_manager.refresh_all_ui_components()

                # Show result using dialog manager
                self.ui_dialog_manager.show_clear_all_data_result(success)

        except Exception as e:
            print(f'[ERROR] Exception in clear_all_data: {e}')
            import tkinter.messagebox as messagebox
            messagebox.showerror("Error", f"An error occurred: {str(e)}")

    def test_notifications(self):
        """Test all notification types using DebugManager"""
        print('[DEBUG] UIManager.test_notifications called')
        if self.debug_manager:
            self.debug_manager.test_notifications()
        else:
            print('[WARNING] DebugManager not initialized')

    def on_tree_selection_changed(self, item, item_type, name):
        print(f'[DEBUG] UIManager.on_tree_selection_changed called: item={item}, type={item_type}, name={name}')
        # Only populate business case field when business case is selected
        if item_type == 'Business Case':
            self.bus_entry_var.set(name)
            self.comp_entry_var.set('')  # Clear component field
        elif item_type == 'Component':
            # Set parent business case name
            parent_item = self.tree.parent(item)
            if parent_item:
                parent_name = self.tree.item(parent_item, 'text')
                self.bus_entry_var.set(parent_name)
            # Set component field to selected component's name
            self.comp_entry_var.set(name)

    def _on_treeview_select(self, event):
        """Handle treeview selection using UIEventHandler"""
        self.ui_event_handler.handle_tree_selection_changed(event)
