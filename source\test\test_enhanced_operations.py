#!/usr/bin/env python3
"""
Test suite for ClipsMoreEnhancedOperations
Tests enhanced database operations for clip assignments.
"""

import unittest
import sqlite3
import tempfile
import os
import sys

# Add source directory to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from DB.op_clipsmore_enhanced import ClipsMoreEnhancedOperations

# NOTE: All new code should include debug print statements at the start of every function/method.

class TestClipsMoreEnhancedOperations(unittest.TestCase):
    """Test cases for ClipsMoreEnhancedOperations class."""
    
    def setUp(self):
        """Set up test database and operations instance."""
        # Create temporary database file
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        self.db_path = self.temp_db.name
        
        # Initialize database schema
        self._create_test_schema()
        
        # Create operations instance
        self.ops = ClipsMoreEnhancedOperations(self.db_path)
    
    def tearDown(self):
        """Clean up test database."""
        try:
            os.unlink(self.db_path)
        except OSError:
            pass
    
    def _create_test_schema(self):
        """Create test database schema."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Enable foreign keys
            cursor.execute("PRAGMA foreign_keys = ON")
            
            # Create clips table
            cursor.execute("""
                CREATE TABLE clips_tbl (
                    clip_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    clip BLOB NOT NULL,
                    alias TEXT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Create business cases table
            cursor.execute("""
                CREATE TABLE more_bus_tbl (
                    more_bus_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    bus_case TEXT NOT NULL UNIQUE
                )
            """)
            
            # Create components table
            cursor.execute("""
                CREATE TABLE more_comp_tbl (
                    more_comp_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    more_bus_id INTEGER NOT NULL,
                    bus_component TEXT NOT NULL,
                    FOREIGN KEY (more_bus_id) REFERENCES more_bus_tbl(more_bus_id) ON DELETE CASCADE
                )
            """)
            
            # Create enhanced clipsmore table
            cursor.execute("""
                CREATE TABLE clipsmore_tbl (
                    transaction_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    clip_id INTEGER NOT NULL,
                    alias TEXT NOT NULL UNIQUE,
                    more_bus_id INTEGER NOT NULL,
                    more_comp_id INTEGER,
                    tree_position INTEGER DEFAULT 0,
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (clip_id) REFERENCES clips_tbl(clip_id) ON DELETE CASCADE,
                    FOREIGN KEY (more_bus_id) REFERENCES more_bus_tbl(more_bus_id) ON DELETE CASCADE,
                    FOREIGN KEY (more_comp_id) REFERENCES more_comp_tbl(more_comp_id) ON DELETE SET NULL
                )
            """)
            
            # Create view
            cursor.execute("""
                CREATE VIEW clipsmore_vw AS
                SELECT 
                    cm.transaction_id,
                    cm.clip_id,
                    cm.alias,
                    cm.tree_position,
                    cm.created_date,
                    cm.modified_date,
                    c.clip as clip_content,
                    c.timestamp as clip_timestamp,
                    mb.bus_case as business_case_name,
                    cm.more_bus_id,
                    mc.bus_component as component_name,
                    cm.more_comp_id
                FROM clipsmore_tbl cm
                JOIN clips_tbl c ON cm.clip_id = c.clip_id
                JOIN more_bus_tbl mb ON cm.more_bus_id = mb.more_bus_id
                LEFT JOIN more_comp_tbl mc ON cm.more_comp_id = mc.more_comp_id
            """)
            
            conn.commit()
    
    def _insert_test_data(self):
        """Insert test data for testing - using actual database contents."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # Insert comprehensive cyber security clips
            cursor.execute("INSERT INTO clips_tbl (clip_id, clip) VALUES (1, ?)",
                         ("Cybercriminals are getting smarter. Are you?\nA 76-year-old retired lawyer lost his entire life savings to a sophisticated phishing attack that started with a single email.",))
            cursor.execute("INSERT INTO clips_tbl (clip_id, clip) VALUES (2, ?)",
                         ("Imposter scams: They can pose as government agents by phone or email, claiming your accounts are compromised and demanding immediate payment to avoid arrest.",))
            cursor.execute("INSERT INTO clips_tbl (clip_id, clip) VALUES (3, ?)",
                         ("Romance scams: These cybercriminals play the long game— building trust over weeks by phone, text, or social media before asking for money for emergencies.",))
            cursor.execute("INSERT INTO clips_tbl (clip_id, clip) VALUES (4, ?)",
                         ("Call-center scams: If you get a call or receive a computer pop up claiming your computer is experiencing problems, hang up immediately. These are fake tech support scams.",))
            cursor.execute("INSERT INTO clips_tbl (clip_id, clip) VALUES (5, ?)",
                         ("Phishing emails often contain urgent language like 'Your account will be closed' or 'Verify immediately' to pressure you into clicking malicious links.",))
            cursor.execute("INSERT INTO clips_tbl (clip_id, clip) VALUES (6, ?)",
                         ("Two-factor authentication (2FA) adds an extra layer of security by requiring a second form of verification beyond just your password.",))
            cursor.execute("INSERT INTO clips_tbl (clip_id, clip) VALUES (7, ?)",
                         ("Password managers generate and store unique, complex passwords for each of your accounts, significantly improving your security posture.",))
            cursor.execute("INSERT INTO clips_tbl (clip_id, clip) VALUES (8, ?)",
                         ("Ransomware attacks encrypt your files and demand payment for the decryption key. Regular backups are your best defense against these attacks.",))
            cursor.execute("INSERT INTO clips_tbl (clip_id, clip) VALUES (9, ?)",
                         ("Social engineering attacks manipulate human psychology rather than technical vulnerabilities to gain unauthorized access to systems or information.",))
            cursor.execute("INSERT INTO clips_tbl (clip_id, clip) VALUES (10, ?)",
                         ("Business Email Compromise (BEC) attacks target companies by impersonating executives or vendors to trick employees into transferring money or sensitive data.",))
            cursor.execute("INSERT INTO clips_tbl (clip_id, clip) VALUES (11, ?)",
                         ("Zero-day vulnerabilities are security flaws unknown to software vendors, making them particularly dangerous as no patches exist yet.",))
            cursor.execute("INSERT INTO clips_tbl (clip_id, clip) VALUES (12, ?)",
                         ("VPN (Virtual Private Network) creates a secure, encrypted connection between your device and the internet, protecting your data from eavesdropping.",))
            cursor.execute("INSERT INTO clips_tbl (clip_id, clip) VALUES (13, ?)",
                         ("Incident response plans outline the steps to take when a security breach occurs, helping minimize damage and recovery time.",))
            cursor.execute("INSERT INTO clips_tbl (clip_id, clip) VALUES (14, ?)",
                         ("Security awareness training educates employees about cyber threats and best practices, turning them into the first line of defense.",))
            cursor.execute("INSERT INTO clips_tbl (clip_id, clip) VALUES (15, ?)",
                         ("Data encryption converts readable information into coded format that can only be deciphered with the correct decryption key.",))

            # Insert comprehensive cyber security business cases
            cursor.execute("INSERT INTO more_bus_tbl (more_bus_id, bus_case) VALUES (1, ?)", ("Cyber Security",))
            cursor.execute("INSERT INTO more_bus_tbl (more_bus_id, bus_case) VALUES (2, ?)", ("Network Security",))
            cursor.execute("INSERT INTO more_bus_tbl (more_bus_id, bus_case) VALUES (3, ?)", ("Data Protection",))
            cursor.execute("INSERT INTO more_bus_tbl (more_bus_id, bus_case) VALUES (4, ?)", ("Incident Response",))
            cursor.execute("INSERT INTO more_bus_tbl (more_bus_id, bus_case) VALUES (5, ?)", ("Security Training",))

            # Insert comprehensive cyber security components
            # Cyber Security components
            cursor.execute("INSERT INTO more_comp_tbl (more_comp_id, more_bus_id, bus_component) VALUES (1, 1, ?)", ("Imposter scams",))
            cursor.execute("INSERT INTO more_comp_tbl (more_comp_id, more_bus_id, bus_component) VALUES (2, 1, ?)", ("Romance scams",))
            cursor.execute("INSERT INTO more_comp_tbl (more_comp_id, more_bus_id, bus_component) VALUES (3, 1, ?)", ("Call-center scams",))
            cursor.execute("INSERT INTO more_comp_tbl (more_comp_id, more_bus_id, bus_component) VALUES (4, 1, ?)", ("Phishing attacks",))
            cursor.execute("INSERT INTO more_comp_tbl (more_comp_id, more_bus_id, bus_component) VALUES (5, 1, ?)", ("Social engineering",))

            # Network Security components
            cursor.execute("INSERT INTO more_comp_tbl (more_comp_id, more_bus_id, bus_component) VALUES (6, 2, ?)", ("VPN security",))
            cursor.execute("INSERT INTO more_comp_tbl (more_comp_id, more_bus_id, bus_component) VALUES (7, 2, ?)", ("Firewall configuration",))
            cursor.execute("INSERT INTO more_comp_tbl (more_comp_id, more_bus_id, bus_component) VALUES (8, 2, ?)", ("Network monitoring",))

            # Data Protection components
            cursor.execute("INSERT INTO more_comp_tbl (more_comp_id, more_bus_id, bus_component) VALUES (9, 3, ?)", ("Data encryption",))
            cursor.execute("INSERT INTO more_comp_tbl (more_comp_id, more_bus_id, bus_component) VALUES (10, 3, ?)", ("Backup strategies",))
            cursor.execute("INSERT INTO more_comp_tbl (more_comp_id, more_bus_id, bus_component) VALUES (11, 3, ?)", ("Access controls",))

            # Incident Response components
            cursor.execute("INSERT INTO more_comp_tbl (more_comp_id, more_bus_id, bus_component) VALUES (12, 4, ?)", ("Response planning",))
            cursor.execute("INSERT INTO more_comp_tbl (more_comp_id, more_bus_id, bus_component) VALUES (13, 4, ?)", ("Forensic analysis",))
            cursor.execute("INSERT INTO more_comp_tbl (more_comp_id, more_bus_id, bus_component) VALUES (14, 4, ?)", ("Recovery procedures",))

            # Security Training components
            cursor.execute("INSERT INTO more_comp_tbl (more_comp_id, more_bus_id, bus_component) VALUES (15, 5, ?)", ("Awareness training",))
            cursor.execute("INSERT INTO more_comp_tbl (more_comp_id, more_bus_id, bus_component) VALUES (16, 5, ?)", ("Phishing simulations",))
            cursor.execute("INSERT INTO more_comp_tbl (more_comp_id, more_bus_id, bus_component) VALUES (17, 5, ?)", ("Security policies",))

            # Insert comprehensive clipsmore transactions
            # Cyber Security transactions
            cursor.execute("INSERT INTO clipsmore_tbl (transaction_id, clip_id, alias, more_bus_id, more_comp_id) VALUES (26, 1, 'cybercriminals_intro', 1, NULL)", ())
            cursor.execute("INSERT INTO clipsmore_tbl (transaction_id, clip_id, alias, more_bus_id, more_comp_id) VALUES (27, 2, 'imposter_scams', 1, 1)", ())
            cursor.execute("INSERT INTO clipsmore_tbl (transaction_id, clip_id, alias, more_bus_id, more_comp_id) VALUES (28, 3, 'romance_scams', 1, 2)", ())
            cursor.execute("INSERT INTO clipsmore_tbl (transaction_id, clip_id, alias, more_bus_id, more_comp_id) VALUES (29, 4, 'tech_support_scams', 1, 3)", ())
            cursor.execute("INSERT INTO clipsmore_tbl (transaction_id, clip_id, alias, more_bus_id, more_comp_id) VALUES (30, 5, 'phishing_emails', 1, 4)", ())
            cursor.execute("INSERT INTO clipsmore_tbl (transaction_id, clip_id, alias, more_bus_id, more_comp_id) VALUES (31, 9, 'social_engineering', 1, 5)", ())

            # Network Security transactions
            cursor.execute("INSERT INTO clipsmore_tbl (transaction_id, clip_id, alias, more_bus_id, more_comp_id) VALUES (32, 12, 'vpn_security', 2, 6)", ())

            # Data Protection transactions
            cursor.execute("INSERT INTO clipsmore_tbl (transaction_id, clip_id, alias, more_bus_id, more_comp_id) VALUES (33, 6, 'two_factor_auth', 3, 11)", ())
            cursor.execute("INSERT INTO clipsmore_tbl (transaction_id, clip_id, alias, more_bus_id, more_comp_id) VALUES (34, 7, 'password_managers', 3, 11)", ())
            cursor.execute("INSERT INTO clipsmore_tbl (transaction_id, clip_id, alias, more_bus_id, more_comp_id) VALUES (35, 8, 'ransomware_defense', 3, 10)", ())
            cursor.execute("INSERT INTO clipsmore_tbl (transaction_id, clip_id, alias, more_bus_id, more_comp_id) VALUES (36, 15, 'data_encryption', 3, 9)", ())

            # Incident Response transactions
            cursor.execute("INSERT INTO clipsmore_tbl (transaction_id, clip_id, alias, more_bus_id, more_comp_id) VALUES (37, 10, 'bec_attacks', 4, 12)", ())
            cursor.execute("INSERT INTO clipsmore_tbl (transaction_id, clip_id, alias, more_bus_id, more_comp_id) VALUES (38, 11, 'zero_day_vulns', 4, 13)", ())
            cursor.execute("INSERT INTO clipsmore_tbl (transaction_id, clip_id, alias, more_bus_id, more_comp_id) VALUES (39, 13, 'incident_response', 4, 12)", ())

            # Security Training transactions
            cursor.execute("INSERT INTO clipsmore_tbl (transaction_id, clip_id, alias, more_bus_id, more_comp_id) VALUES (40, 14, 'security_training', 5, 15)", ())

            conn.commit()
    
    def test_create_assignment_business_case_only(self):
        """Test creating assignment to business case only."""
        self._insert_test_data()
        
        transaction_id = self.ops.create_assignment(
            clip_id=1,
            more_bus_id=1,
            more_comp_id=None,
            alias="test_alias"
        )
        
        self.assertIsNotNone(transaction_id)
        self.assertIsInstance(transaction_id, int)
        
        # Verify assignment was created
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT clip_id, more_bus_id, more_comp_id, alias 
                FROM clipsmore_tbl 
                WHERE transaction_id = ?
            """, (transaction_id,))
            
            result = cursor.fetchone()
            self.assertIsNotNone(result)
            self.assertEqual(result[0], 1)  # clip_id
            self.assertEqual(result[1], 1)  # more_bus_id
            self.assertIsNone(result[2])    # more_comp_id
            self.assertEqual(result[3], "test_alias")  # alias
    
    def test_create_assignment_with_component(self):
        """Test creating assignment to business case and component."""
        self._insert_test_data()
        
        transaction_id = self.ops.create_assignment(
            clip_id=1,
            more_bus_id=1,
            more_comp_id=1,
            alias="test_component_alias"
        )
        
        self.assertIsNotNone(transaction_id)
        
        # Verify assignment was created with component
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT clip_id, more_bus_id, more_comp_id, alias 
                FROM clipsmore_tbl 
                WHERE transaction_id = ?
            """, (transaction_id,))
            
            result = cursor.fetchone()
            self.assertIsNotNone(result)
            self.assertEqual(result[0], 1)  # clip_id
            self.assertEqual(result[1], 1)  # more_bus_id
            self.assertEqual(result[2], 1)  # more_comp_id
            self.assertEqual(result[3], "test_component_alias")  # alias
    
    def test_create_assignment_auto_alias(self):
        """Test creating assignment with auto-generated alias."""
        self._insert_test_data()
        
        transaction_id = self.ops.create_assignment(
            clip_id=1,
            more_bus_id=1,
            more_comp_id=None,
            alias=None  # Should auto-generate
        )
        
        self.assertIsNotNone(transaction_id)
        
        # Verify alias was auto-generated
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT alias FROM clipsmore_tbl WHERE transaction_id = ?", (transaction_id,))
            result = cursor.fetchone()
            
            self.assertIsNotNone(result)
            self.assertIsNotNone(result[0])  # alias should not be None
            self.assertGreater(len(result[0]), 0)  # alias should not be empty
    
    def test_create_assignment_duplicate_alias_fails(self):
        """Test that duplicate aliases are rejected."""
        self._insert_test_data()
        
        # Create first assignment
        self.ops.create_assignment(
            clip_id=1,
            more_bus_id=1,
            alias="duplicate_alias"
        )
        
        # Try to create second assignment with same alias
        with self.assertRaises(Exception):
            self.ops.create_assignment(
                clip_id=2,
                more_bus_id=1,
                alias="duplicate_alias"
            )
    
    def test_update_assignment(self):
        """Test updating an existing assignment."""
        self._insert_test_data()
        
        # Create assignment
        transaction_id = self.ops.create_assignment(
            clip_id=1,
            more_bus_id=1,
            alias="original_alias"
        )
        
        # Update assignment
        success = self.ops.update_assignment(
            transaction_id=transaction_id,
            alias="updated_alias",
            more_bus_id=2
        )
        
        self.assertTrue(success)
        
        # Verify update
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT alias, more_bus_id 
                FROM clipsmore_tbl 
                WHERE transaction_id = ?
            """, (transaction_id,))
            
            result = cursor.fetchone()
            self.assertEqual(result[0], "updated_alias")
            self.assertEqual(result[1], 2)
    
    def test_delete_assignment(self):
        """Test deleting an assignment."""
        self._insert_test_data()
        
        # Create assignment
        transaction_id = self.ops.create_assignment(
            clip_id=1,
            more_bus_id=1,
            alias="to_delete"
        )
        
        # Delete assignment
        success = self.ops.delete_assignment(transaction_id)
        self.assertTrue(success)
        
        # Verify deletion
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM clipsmore_tbl WHERE transaction_id = ?", (transaction_id,))
            count = cursor.fetchone()[0]
            self.assertEqual(count, 0)
    
    def test_move_assignment(self):
        """Test moving assignment to different business case/component."""
        self._insert_test_data()
        
        # Create assignment
        transaction_id = self.ops.create_assignment(
            clip_id=1,
            more_bus_id=1,
            more_comp_id=1,
            alias="move_test"
        )
        
        # Move to different business case
        success = self.ops.move_assignment(
            transaction_id=transaction_id,
            target_bus_id=2,
            target_comp_id=None
        )
        
        self.assertTrue(success)
        
        # Verify move
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT more_bus_id, more_comp_id 
                FROM clipsmore_tbl 
                WHERE transaction_id = ?
            """, (transaction_id,))
            
            result = cursor.fetchone()
            self.assertEqual(result[0], 2)  # New business case
            self.assertIsNone(result[1])   # No component
    
    def test_copy_assignment(self):
        """Test copying assignment to different location."""
        self._insert_test_data()
        
        # Create original assignment
        original_id = self.ops.create_assignment(
            clip_id=1,
            more_bus_id=1,
            alias="original_copy"
        )
        
        # Copy to different business case
        new_id = self.ops.copy_assignment(
            transaction_id=original_id,
            target_bus_id=2,
            target_comp_id=None
        )
        
        self.assertIsNotNone(new_id)
        self.assertNotEqual(new_id, original_id)
        
        # Verify both assignments exist
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM clipsmore_tbl WHERE clip_id = 1")
            count = cursor.fetchone()[0]
            # Should have 1 existing assignment from test data + 1 original + 1 copy = 3 total
            self.assertEqual(count, 3)
    
    def test_get_assignments_by_business_case(self):
        """Test retrieving assignments by business case."""
        self._insert_test_data()
        
        # Create assignments
        self.ops.create_assignment(clip_id=1, more_bus_id=1, alias="bc1_alias1")
        self.ops.create_assignment(clip_id=2, more_bus_id=1, alias="bc1_alias2")

        # Get assignments for business case 1 (includes existing test data + new assignments)
        assignments = self.ops.get_assignments_by_business_case(1)

        # Should have 6 existing assignments from test data + 2 new ones = 8 total
        self.assertEqual(len(assignments), 8)
        aliases = [a['alias'] for a in assignments]
        self.assertIn('bc1_alias1', aliases)
        self.assertIn('bc1_alias2', aliases)
    
    def test_get_assignments_by_component(self):
        """Test retrieving assignments by component."""
        self._insert_test_data()
        
        # Create assignments
        self.ops.create_assignment(clip_id=1, more_bus_id=1, more_comp_id=1, alias="comp1_alias1")
        self.ops.create_assignment(clip_id=2, more_bus_id=1, more_comp_id=1, alias="comp1_alias2")
        self.ops.create_assignment(clip_id=1, more_bus_id=1, more_comp_id=2, alias="comp2_alias1")

        # Get assignments for component 1 (includes existing test data + new assignments)
        assignments = self.ops.get_assignments_by_component(1)

        # Should have 1 existing assignment from test data + 2 new ones = 3 total
        self.assertEqual(len(assignments), 3)
        aliases = [a['alias'] for a in assignments]
        self.assertIn('comp1_alias1', aliases)
        self.assertIn('comp1_alias2', aliases)
    
    def test_tree_position_management(self):
        """Test tree position assignment and management."""
        self._insert_test_data()
        
        # Create assignments with specific positions
        id1 = self.ops.create_assignment(clip_id=1, more_bus_id=1, alias="pos1", tree_position=1)
        id2 = self.ops.create_assignment(clip_id=2, more_bus_id=1, alias="pos2", tree_position=2)
        
        # Verify positions
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT tree_position 
                FROM clipsmore_tbl 
                WHERE transaction_id IN (?, ?)
                ORDER BY transaction_id
            """, (id1, id2))
            
            positions = [row[0] for row in cursor.fetchall()]
            self.assertEqual(positions, [1, 2])
    
    def test_foreign_key_constraints(self):
        """Test that foreign key constraints are enforced."""
        self._insert_test_data()

        # Test that we can create valid assignments first
        valid_id = self.ops.create_assignment(
            clip_id=1,
            more_bus_id=1,
            alias="valid_test"
        )
        self.assertIsNotNone(valid_id)

        # Test constraint validation by checking if invalid data would be caught
        # Note: The actual constraint enforcement depends on the database connection
        # and the operations implementation
        try:
            # Try to create assignment with non-existent business case
            invalid_id = self.ops.create_assignment(
                clip_id=1,
                more_bus_id=999,  # Non-existent
                alias="invalid_business"
            )
            # If we get here, check if the assignment actually exists in the database
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM clipsmore_tbl WHERE transaction_id = ?", (invalid_id,))
                count = cursor.fetchone()[0]
                # The assignment should not exist if foreign keys are properly enforced
                if count > 0:
                    self.fail("Foreign key constraint not enforced - invalid business case assignment was created")
        except Exception:
            # This is expected if foreign keys are enforced
            pass
    
    def test_view_functionality(self):
        """Test that the clipsmore_vw view works correctly."""
        self._insert_test_data()
        
        # Create assignment
        transaction_id = self.ops.create_assignment(
            clip_id=1,
            more_bus_id=1,
            more_comp_id=1,
            alias="view_test"
        )
        
        # Query view
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT transaction_id, alias, business_case_name, component_name, clip_content
                FROM clipsmore_vw
                WHERE transaction_id = ?
            """, (transaction_id,))
            
            result = cursor.fetchone()
            self.assertIsNotNone(result)
            self.assertEqual(result[0], transaction_id)
            self.assertEqual(result[1], "view_test")
            self.assertEqual(result[2], "Cyber Security")
            self.assertEqual(result[3], "Imposter scams")
            self.assertIn("Cybercriminals are getting smarter", result[4])

if __name__ == '__main__':
    unittest.main()
