"""
RewardSystemManager - Handles emoji reward system functionality

This manager centralizes the emoji reward system logic that was previously
embedded in the UIManager, providing a cleaner separation of concerns.
"""

import tkinter as tk
from typing import List, Optional


class RewardSystemManager:
    """
    Manages the emoji reward system for user feedback.
    
    This class handles the display of emoji rewards when users
    complete actions, providing positive feedback and engagement.
    """
    
    def __init__(self, parent_widget: tk.Widget):
        """
        Initialize RewardSystemManager.
        
        Args:
            parent_widget: Parent widget where rewards will be displayed
        """
        print('[DEBUG] RewardSystemManager.__init__ called')
        self.parent_widget = parent_widget
        
        # Initialize emoji reward system
        self.reward_emojis = ["🎉", "🌟", "✨", "🎊", "🏆", "🎯", "💫", "🔥", "⭐", "🎈", "🎁", "🚀"]
        self.current_emoji_index = 0
        self.reward_popup = None
        
    def show_emoji_reward(self, message: str = "Great job!", duration: int = 2000):
        """
        Show a fun emoji reward popup.
        
        Args:
            message: Message to display with the emoji
            duration: Duration in milliseconds to show the reward
        """
        print('[DEBUG] RewardSystemManager.show_emoji_reward called')
        
        try:
            # Get the current emoji and cycle to next
            emoji = self.reward_emojis[self.current_emoji_index]
            self.current_emoji_index = (self.current_emoji_index + 1) % len(self.reward_emojis)
            
            # Create popup frame in top right of parent widget
            popup_frame = tk.Frame(self.parent_widget, bg="#4CAF50", relief='raised', bd=3)
            popup_frame.place(relx=0.95, rely=0.05, anchor='ne')
            
            # Create emoji label with animation-like styling
            emoji_label = tk.Label(popup_frame, text=emoji, font=('Arial', 24),
                                 bg="#4CAF50", fg="white", padx=10, pady=5)
            emoji_label.pack()
            
            # Create reward text
            reward_text = tk.Label(popup_frame, text=message, font=('Arial', 10, 'bold'),
                                 bg="#4CAF50", fg="white", padx=10, pady=2)
            reward_text.pack()
            
            # Store reference to popup for cleanup
            self.reward_popup = popup_frame
            
            # Auto-hide after specified duration
            self.parent_widget.after(duration, self.hide_emoji_reward)
            
            print(f'[DEBUG] Showing emoji reward: {emoji} with message: {message}')
            
        except Exception as e:
            print(f'[ERROR] Failed to show emoji reward: {e}')
            
    def hide_emoji_reward(self):
        """Hide the emoji reward popup"""
        print('[DEBUG] RewardSystemManager.hide_emoji_reward called')
        
        try:
            if self.reward_popup:
                self.reward_popup.destroy()
                self.reward_popup = None
                print('[DEBUG] Emoji reward popup hidden')
        except Exception as e:
            print(f'[ERROR] Failed to hide emoji reward: {e}')
            
    def show_custom_reward(self, emoji: str, message: str, duration: int = 2000, 
                          bg_color: str = "#4CAF50", fg_color: str = "white"):
        """
        Show a custom emoji reward with specified parameters.
        
        Args:
            emoji: Custom emoji to display
            message: Custom message to display
            duration: Duration in milliseconds to show the reward
            bg_color: Background color for the popup
            fg_color: Foreground color for the text
        """
        print(f'[DEBUG] RewardSystemManager.show_custom_reward called: {emoji}')
        
        try:
            # Create popup frame in top right of parent widget
            popup_frame = tk.Frame(self.parent_widget, bg=bg_color, relief='raised', bd=3)
            popup_frame.place(relx=0.95, rely=0.05, anchor='ne')
            
            # Create emoji label
            emoji_label = tk.Label(popup_frame, text=emoji, font=('Arial', 24),
                                 bg=bg_color, fg=fg_color, padx=10, pady=5)
            emoji_label.pack()
            
            # Create reward text
            reward_text = tk.Label(popup_frame, text=message, font=('Arial', 10, 'bold'),
                                 bg=bg_color, fg=fg_color, padx=10, pady=2)
            reward_text.pack()
            
            # Store reference to popup for cleanup
            self.reward_popup = popup_frame
            
            # Auto-hide after specified duration
            self.parent_widget.after(duration, self.hide_emoji_reward)
            
            print(f'[DEBUG] Showing custom emoji reward: {emoji} with message: {message}')
            
        except Exception as e:
            print(f'[ERROR] Failed to show custom emoji reward: {e}')
            
    def show_achievement_reward(self, achievement_type: str = "general"):
        """
        Show an achievement-specific reward.
        
        Args:
            achievement_type: Type of achievement (general, completion, milestone, etc.)
        """
        print(f'[DEBUG] RewardSystemManager.show_achievement_reward called: {achievement_type}')
        
        achievement_rewards = {
            "general": {"emoji": "🎉", "message": "Great job!"},
            "completion": {"emoji": "✅", "message": "Task completed!"},
            "milestone": {"emoji": "🏆", "message": "Milestone reached!"},
            "save": {"emoji": "💾", "message": "Saved successfully!"},
            "delete": {"emoji": "🗑️", "message": "Deleted successfully!"},
            "copy": {"emoji": "📋", "message": "Copied to clipboard!"},
            "create": {"emoji": "✨", "message": "Created successfully!"},
            "update": {"emoji": "🔄", "message": "Updated successfully!"}
        }
        
        reward_info = achievement_rewards.get(achievement_type, achievement_rewards["general"])
        self.show_custom_reward(reward_info["emoji"], reward_info["message"])
        
    def cycle_emoji(self) -> str:
        """
        Get the next emoji in the cycle without showing a reward.
        
        Returns:
            The next emoji in the cycle
        """
        emoji = self.reward_emojis[self.current_emoji_index]
        self.current_emoji_index = (self.current_emoji_index + 1) % len(self.reward_emojis)
        return emoji
        
    def get_current_emoji(self) -> str:
        """
        Get the current emoji without cycling.
        
        Returns:
            The current emoji
        """
        return self.reward_emojis[self.current_emoji_index]
        
    def set_emoji_list(self, emoji_list: List[str]):
        """
        Set a custom list of emojis for rewards.
        
        Args:
            emoji_list: List of emoji strings to use for rewards
        """
        print(f'[DEBUG] RewardSystemManager.set_emoji_list called with {len(emoji_list)} emojis')
        
        if emoji_list and len(emoji_list) > 0:
            self.reward_emojis = emoji_list
            self.current_emoji_index = 0
            print('[DEBUG] Emoji list updated successfully')
        else:
            print('[WARNING] Invalid emoji list provided, keeping current list')
            
    def reset_emoji_cycle(self):
        """Reset the emoji cycle to the beginning."""
        print('[DEBUG] RewardSystemManager.reset_emoji_cycle called')
        self.current_emoji_index = 0
        
    def is_reward_showing(self) -> bool:
        """
        Check if a reward popup is currently showing.
        
        Returns:
            True if a reward is currently displayed, False otherwise
        """
        return self.reward_popup is not None
        
    def force_hide_reward(self):
        """Force hide any currently showing reward popup."""
        print('[DEBUG] RewardSystemManager.force_hide_reward called')
        
        if self.reward_popup:
            try:
                self.reward_popup.destroy()
                self.reward_popup = None
                print('[DEBUG] Reward popup force hidden')
            except Exception as e:
                print(f'[ERROR] Failed to force hide reward popup: {e}')
                
    def get_emoji_count(self) -> int:
        """
        Get the total number of available emojis.
        
        Returns:
            Number of emojis in the reward list
        """
        return len(self.reward_emojis)
        
    def get_current_emoji_index(self) -> int:
        """
        Get the current emoji index.
        
        Returns:
            Current index in the emoji list
        """
        return self.current_emoji_index
        
    def set_parent_widget(self, parent_widget: tk.Widget):
        """
        Update the parent widget for reward display.
        
        Args:
            parent_widget: New parent widget for rewards
        """
        print('[DEBUG] RewardSystemManager.set_parent_widget called')
        self.parent_widget = parent_widget
        
    def cleanup(self):
        """Cleanup any resources and hide active rewards."""
        print('[DEBUG] RewardSystemManager.cleanup called')
        
        try:
            self.force_hide_reward()
            print('[DEBUG] RewardSystemManager cleanup completed')
        except Exception as e:
            print(f'[ERROR] Error during RewardSystemManager cleanup: {e}')
