#!/usr/bin/env python3
"""
Complete Application Test
Demonstrates all enhanced features of ClipsMore v2.0
"""

import sys
import os
import sqlite3
import time

# Add source directory to path
sys.path.insert(0, os.path.dirname(__file__))

# NOTE: All new code should include debug print statements at the start of every function/method.

def test_database_schema():
    """Test that the enhanced database schema is working."""
    print("🗄️ Testing Enhanced Database Schema...")
    
    try:
        # Check if database exists and has correct schema
        db_path = "DB/clipsmore_db.db"
        if not os.path.exists(db_path):
            print("❌ Database file not found")
            return False
        
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # Check for enhanced clipsmore table
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name='clipsmore_tbl'
            """)
            
            if not cursor.fetchone():
                print("❌ Enhanced clipsmore_tbl not found")
                return False
            
            # Check for view
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='view' AND name='clipsmore_vw'
            """)
            
            if not cursor.fetchone():
                print("❌ clipsmore_vw view not found")
                return False
            
            # Check table structure
            cursor.execute("PRAGMA table_info(clipsmore_tbl)")
            columns = [row[1] for row in cursor.fetchall()]
            
            required_columns = ['transaction_id', 'clip_id', 'alias', 'more_bus_id', 
                              'more_comp_id', 'tree_position', 'created_date', 'modified_date']
            
            for col in required_columns:
                if col not in columns:
                    print(f"❌ Missing column: {col}")
                    return False
            
            print("✅ Database schema is correct")
            return True
            
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False

def test_alias_generator():
    """Test the advanced alias generator."""
    print("🧠 Testing Intelligent Alias Generator...")
    
    try:
        from utils.alias_generator import AliasGenerator
        
        generator = AliasGenerator()
        
        # Test different content types
        test_cases = [
            ("https://github.com/user/repo", "github"),
            ("def calculate_total():", "calculate"),
            ("document.pdf", "document"),
            ("Meeting notes 2024-01-15", "meeting"),
            ("API endpoint configuration", "api"),
        ]
        
        for content, expected_word in test_cases:
            alias = generator.generate_from_content(content)
            if expected_word.lower() in alias.lower():
                print(f"✅ '{content[:30]}...' → '{alias}'")
            else:
                print(f"⚠️ '{content[:30]}...' → '{alias}' (expected '{expected_word}')")
        
        # Test validation
        valid_aliases = ['test_alias', 'user123', 'api_endpoint']
        invalid_aliases = ['123test', 'test space', 'test@email']
        
        for alias in valid_aliases:
            if generator.validate_alias(alias):
                print(f"✅ Valid alias: '{alias}'")
            else:
                print(f"❌ Should be valid: '{alias}'")
        
        for alias in invalid_aliases:
            if not generator.validate_alias(alias):
                print(f"✅ Correctly rejected: '{alias}'")
            else:
                print(f"❌ Should be invalid: '{alias}'")
        
        print("✅ Alias generator working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Alias generator test failed: {e}")
        return False

def test_database_operations():
    """Test enhanced database operations."""
    print("🔧 Testing Enhanced Database Operations...")
    
    try:
        from source.DB.op_clips_tbl import ClipsTableOperations
        from source.DB.op_more_tbl import MoreTableOperations
        from source.DB.op_clipsmore_enhanced import ClipsMoreEnhancedOperations
        
        # Test basic operations
        clips_ops = ClipsTableOperations()
        more_ops = MoreTableOperations()
        enhanced_ops = ClipsMoreEnhancedOperations()
        
        # Test reading clips
        clips = clips_ops.read_all_clips()
        print(f"✅ Found {len(clips)} clips in database")
        
        # Test reading business cases
        business_cases = more_ops.read_all_business_cases()
        print(f"✅ Found {len(business_cases)} business cases")
        
        # Test enhanced operations if we have data
        if business_cases and clips:
            try:
                # Try to create a test assignment
                test_alias = f"test_assignment_{int(time.time())}"
                transaction_id = enhanced_ops.create_assignment(
                    clip_id=clips[0]['clip_id'],
                    more_bus_id=business_cases[0]['id'],
                    alias=test_alias
                )
                
                if transaction_id:
                    print(f"✅ Created test assignment with ID: {transaction_id}")
                    
                    # Clean up test assignment
                    enhanced_ops.delete_assignment(transaction_id)
                    print("✅ Cleaned up test assignment")
                else:
                    print("⚠️ Could not create test assignment")
                    
            except Exception as e:
                print(f"⚠️ Assignment test failed (may be expected): {e}")
        
        print("✅ Database operations working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Database operations test failed: {e}")
        return False

def test_ui_components():
    """Test UI components can be imported and initialized."""
    print("🖥️ Testing UI Components...")
    
    try:
        import tkinter as tk
        from ui_manager import UIManager
        
        # Create test root window
        root = tk.Tk()
        root.withdraw()  # Hide window
        
        # Test UI manager initialization
        ui_manager = UIManager(root)
        
        # Check that tabs were created
        if hasattr(ui_manager, 'clips_tab') and ui_manager.clips_tab:
            print("✅ Clips tab initialized")
        else:
            print("❌ Clips tab not initialized")
            return False
        
        if hasattr(ui_manager, 'more_tab') and ui_manager.more_tab:
            print("✅ More tab initialized")
        else:
            print("❌ More tab not initialized")
            return False
        
        if hasattr(ui_manager, 'about_tab') and ui_manager.about_tab:
            print("✅ About tab initialized")
        else:
            print("❌ About tab not initialized")
            return False
        
        # Test theme functionality
        original_theme = ui_manager.dark_mode
        ui_manager.toggle_theme()
        if ui_manager.dark_mode != original_theme:
            print("✅ Theme toggle working")
        else:
            print("⚠️ Theme toggle may not be working")
        
        # Restore original theme
        ui_manager.toggle_theme()
        
        # Clean up
        root.destroy()
        
        print("✅ UI components working correctly")
        return True
        
    except Exception as e:
        print(f"❌ UI components test failed: {e}")
        return False

def test_file_structure():
    """Test that all required files exist."""
    print("📁 Testing File Structure...")
    
    required_files = [
        "main.py",
        "ui_manager.py",
        "utils/alias_generator.py",
        "DB/op_clips_tbl.py",
        "DB/op_more_tbl.py",
        "DB/op_clipsmore_enhanced.py",
        "DB/db_connection.py",
        "DB/migration_v2.py",
    ]
    
    required_docs = [
        "../docs/user/User_Guide.md",
        "../docs/technical/README.md",
        "../docs/technical/database/ER_Diagram.md",
        "../docs/technical/architecture/System_Architecture.md",
        "../docs/technical/uml/Class_Diagrams.md",
        "../docs/technical/uml/Sequence_Diagrams.md",
        "../docs/technical/c4/C4_Model.md",
        "../docs/technical/dependencies/Dependency_Analysis.md",
    ]
    
    all_files_exist = True
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ Missing: {file_path}")
            all_files_exist = False
    
    for doc_path in required_docs:
        if os.path.exists(doc_path):
            print(f"✅ {doc_path}")
        else:
            print(f"❌ Missing: {doc_path}")
            all_files_exist = False
    
    if all_files_exist:
        print("✅ All required files present")
        return True
    else:
        print("❌ Some files are missing")
        return False

def main():
    """Run complete application test suite."""
    print("🎉 ClipsMore v2.0 Complete Application Test 🎉")
    print("=" * 50)
    
    tests = [
        ("File Structure", test_file_structure),
        ("Database Schema", test_database_schema),
        ("Alias Generator", test_alias_generator),
        ("Database Operations", test_database_operations),
        ("UI Components", test_ui_components),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running {test_name} Test...")
        print("-" * 30)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! ClipsMore v2.0 is ready! 🎉")
        print("\n🚀 To start the application, run:")
        print("   python main.py")
        print("\n📖 For documentation, see:")
        print("   docs/user/User_Guide.md")
        print("   docs/technical/README.md")
    else:
        print("⚠️ Some tests failed. Check the output above for details.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
