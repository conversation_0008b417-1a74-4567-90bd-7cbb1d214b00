#!/usr/bin/env python3
"""
Restore Manager for ClipsMore
Handles database restoration from backup files with verification and progress tracking.
"""

import os
import sys
import sqlite3
import gzip
import shutil
import hashlib
import json
from datetime import datetime
from typing import Dict, List, Any, Optional, Callable
from pathlib import Path

# Add parent directories to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from DB.db_connection import ConnectionPoolManager


class RestoreError(Exception):
    """Exception raised for restore operation errors."""
    pass


class RestoreManager:
    """
    Core restore manager for ClipsMore application.
    Handles database restoration from backup files with verification and progress tracking.
    """
    
    def __init__(self, database_manager=None):
        """Initialize the restore manager."""
        print('[DEBUG] RestoreManager.__init__ called')
        self.database_manager = database_manager
        self.connection_pool = ConnectionPoolManager()
        self.progress_callback = None
        self.cancel_requested = False
        
        # Default restore configuration
        self.default_restore_config = {
            'verification': True,
            'backup_current': True,
            'restore_type': 'full',
            'overwrite_existing': False
        }
    
    def set_progress_callback(self, callback: Callable[[int, str], None]):
        """Set callback for progress updates."""
        print('[DEBUG] RestoreManager.set_progress_callback called')
        self.progress_callback = callback
    
    def cancel_restore(self):
        """Cancel the current restore operation."""
        print('[DEBUG] RestoreManager.cancel_restore called')
        self.cancel_requested = True
    
    def _update_progress(self, percentage: int, message: str):
        """Update progress if callback is set."""
        if self.progress_callback:
            self.progress_callback(percentage, message)
    
    def _check_cancellation(self):
        """Check if restore operation was cancelled."""
        if self.cancel_requested:
            raise RestoreError("Restore operation was cancelled")
    
    def restore_from_backup(self, backup_path: str, restore_config: Dict[str, Any] = None) -> bool:
        """
        Restore database from backup file.
        
        Args:
            backup_path: Path to backup file
            restore_config: Restore configuration options
                - verification: Verify backup before restore (default: True)
                - backup_current: Create backup of current database (default: True)
                - restore_type: Type of restore ('full', 'selective')
                - overwrite_existing: Overwrite existing data (default: False)
        
        Returns:
            True if restore successful, False otherwise
        """
        print(f'[DEBUG] RestoreManager.restore_from_backup called for {backup_path}')
        
        try:
            self.cancel_requested = False
            config = {**self.default_restore_config, **(restore_config or {})}
            
            self._update_progress(0, "Starting restore...")
            
            # Validate backup file
            if not self._validate_backup_file(backup_path):
                raise RestoreError(f"Invalid backup file: {backup_path}")
            
            self._check_cancellation()
            self._update_progress(10, "Validating backup file...")
            
            # Verify backup if requested
            if config.get('verification', True):
                self._update_progress(20, "Verifying backup integrity...")
                if not self._verify_backup_integrity(backup_path):
                    raise RestoreError("Backup integrity verification failed")
            
            self._check_cancellation()
            
            # Create backup of current database if requested
            current_backup_path = None
            if config.get('backup_current', True):
                self._update_progress(30, "Creating backup of current database...")
                current_backup_path = self._backup_current_database()
            
            self._check_cancellation()
            self._update_progress(50, "Preparing for restore...")
            
            # Extract backup if compressed
            extracted_backup_path = self._extract_backup_if_compressed(backup_path)
            
            try:
                # Perform the restore
                self._update_progress(60, "Restoring database...")
                success = self._perform_database_restore(extracted_backup_path, config)
                
                if success:
                    self._update_progress(90, "Finalizing restore...")
                    self._record_restore_history(backup_path, config, True)
                    self._update_progress(100, "Restore completed successfully")
                    return True
                else:
                    raise RestoreError("Database restore operation failed")
                    
            finally:
                # Clean up extracted backup if it was compressed
                if extracted_backup_path != backup_path and os.path.exists(extracted_backup_path):
                    os.remove(extracted_backup_path)
                    
        except RestoreError:
            self._record_restore_history(backup_path, config, False)
            raise
        except Exception as e:
            self._record_restore_history(backup_path, config, False)
            raise RestoreError(f"Unexpected error during restore: {e}")
    
    def _validate_backup_file(self, backup_path: str) -> bool:
        """Validate that the backup file exists and is accessible."""
        print(f'[DEBUG] RestoreManager._validate_backup_file called for {backup_path}')
        
        if not os.path.exists(backup_path):
            print(f'[ERROR] Backup file does not exist: {backup_path}')
            return False
        
        if not os.path.isfile(backup_path):
            print(f'[ERROR] Backup path is not a file: {backup_path}')
            return False
        
        if os.path.getsize(backup_path) == 0:
            print(f'[ERROR] Backup file is empty: {backup_path}')
            return False
        
        return True
    
    def _verify_backup_integrity(self, backup_path: str) -> bool:
        """Verify backup file integrity using checksum."""
        print(f'[DEBUG] RestoreManager._verify_backup_integrity called')
        
        try:
            # Calculate current checksum
            current_checksum = self._calculate_file_checksum(backup_path)
            
            # Try to get stored checksum from backup history
            stored_checksum = self._get_backup_checksum_from_history(backup_path)
            
            if stored_checksum:
                if current_checksum == stored_checksum:
                    print('[DEBUG] Backup integrity verified - checksums match')
                    return True
                else:
                    print(f'[ERROR] Backup integrity check failed - checksum mismatch')
                    print(f'[ERROR] Expected: {stored_checksum}, Got: {current_checksum}')
                    return False
            else:
                # If no stored checksum, try to verify by opening the database
                print('[DEBUG] No stored checksum found, attempting database verification')
                return self._verify_backup_database_structure(backup_path)
                
        except Exception as e:
            print(f'[ERROR] Backup integrity verification failed: {e}')
            return False
    
    def _calculate_file_checksum(self, file_path: str) -> str:
        """Calculate SHA-256 checksum of file."""
        print(f'[DEBUG] RestoreManager._calculate_file_checksum called')
        
        sha256_hash = hashlib.sha256()
        
        # Handle compressed files
        if file_path.endswith('.gz'):
            with gzip.open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    sha256_hash.update(chunk)
        else:
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    sha256_hash.update(chunk)
        
        return sha256_hash.hexdigest()
    
    def _get_backup_checksum_from_history(self, backup_path: str) -> Optional[str]:
        """Get stored checksum for backup from history table."""
        print(f'[DEBUG] RestoreManager._get_backup_checksum_from_history called')
        
        try:
            with self.connection_pool.get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT checksum FROM backup_history 
                    WHERE backup_path = ? 
                    ORDER BY created_date DESC 
                    LIMIT 1
                """, (backup_path,))
                
                result = cursor.fetchone()
                return result[0] if result else None
                
        except Exception as e:
            print(f'[ERROR] Failed to get backup checksum from history: {e}')
            return None
    
    def _verify_backup_database_structure(self, backup_path: str) -> bool:
        """Verify backup by checking database structure."""
        print(f'[DEBUG] RestoreManager._verify_backup_database_structure called')
        
        try:
            # Extract if compressed
            temp_path = None
            db_path = backup_path
            
            if backup_path.endswith('.gz'):
                temp_path = backup_path[:-3] + '_temp.db'
                with gzip.open(backup_path, 'rb') as f_in:
                    with open(temp_path, 'wb') as f_out:
                        shutil.copyfileobj(f_in, f_out)
                db_path = temp_path
            
            try:
                # Try to connect and check basic structure
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                
                # Check if essential tables exist
                cursor.execute("""
                    SELECT name FROM sqlite_master 
                    WHERE type='table' AND name IN ('clips_tbl', 'more_bus_tbl', 'more_comp_tbl')
                """)
                
                tables = [row[0] for row in cursor.fetchall()]
                expected_tables = ['clips_tbl', 'more_bus_tbl', 'more_comp_tbl']
                
                conn.close()
                
                # Check if all essential tables are present
                missing_tables = set(expected_tables) - set(tables)
                if missing_tables:
                    print(f'[ERROR] Missing essential tables in backup: {missing_tables}')
                    return False
                
                print('[DEBUG] Backup database structure verification passed')
                return True
                
            finally:
                if temp_path and os.path.exists(temp_path):
                    os.remove(temp_path)
                    
        except Exception as e:
            print(f'[ERROR] Backup database structure verification failed: {e}')
            return False

    def _backup_current_database(self) -> str:
        """Create a backup of the current database before restore."""
        print('[DEBUG] RestoreManager._backup_current_database called')

        try:
            # Create backup filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"pre_restore_backup_{timestamp}.db"

            # Use the same backup directory structure
            backup_dir = os.path.join(os.path.expanduser("~"), "Documents", "ClipsMore", "Backups")
            os.makedirs(backup_dir, exist_ok=True)

            backup_path = os.path.join(backup_dir, backup_filename)

            # Create backup using SQLite backup API
            with self.connection_pool.get_connection() as source_conn:
                backup_conn = sqlite3.connect(backup_path)
                try:
                    source_conn.backup(backup_conn)
                    backup_conn.close()
                    print(f'[DEBUG] Current database backed up to: {backup_path}')
                    return backup_path
                except Exception as e:
                    backup_conn.close()
                    if os.path.exists(backup_path):
                        os.remove(backup_path)
                    raise e

        except Exception as e:
            print(f'[ERROR] Failed to backup current database: {e}')
            raise RestoreError(f"Failed to backup current database: {e}")

    def _extract_backup_if_compressed(self, backup_path: str) -> str:
        """Extract backup file if it's compressed."""
        print(f'[DEBUG] RestoreManager._extract_backup_if_compressed called')

        if not backup_path.endswith('.gz'):
            return backup_path

        try:
            # Create temporary extracted file
            extracted_path = backup_path[:-3] + '_temp_restore.db'

            with gzip.open(backup_path, 'rb') as f_in:
                with open(extracted_path, 'wb') as f_out:
                    shutil.copyfileobj(f_in, f_out)

            print(f'[DEBUG] Backup extracted to: {extracted_path}')
            return extracted_path

        except Exception as e:
            print(f'[ERROR] Failed to extract compressed backup: {e}')
            raise RestoreError(f"Failed to extract compressed backup: {e}")

    def _perform_database_restore(self, backup_path: str, config: Dict[str, Any]) -> bool:
        """Perform the actual database restore operation."""
        print(f'[DEBUG] RestoreManager._perform_database_restore called')

        try:
            # Get the current database path
            current_db_path = self.connection_pool.db_path

            if config.get('restore_type', 'full') == 'full':
                # Full restore - replace entire database
                return self._perform_full_restore(backup_path, current_db_path)
            else:
                # Selective restore - not implemented yet
                print('[WARNING] Selective restore not implemented, performing full restore')
                return self._perform_full_restore(backup_path, current_db_path)

        except Exception as e:
            print(f'[ERROR] Database restore failed: {e}')
            return False

    def _perform_full_restore(self, backup_path: str, current_db_path: str) -> bool:
        """Perform full database restore by replacing the current database."""
        print(f'[DEBUG] RestoreManager._perform_full_restore called')

        try:
            # Step 1: Preserve current backup history before restore
            print('[DEBUG] Preserving backup history before restore...')
            preserved_backup_history = self._preserve_backup_history()
            preserved_import_history = self._preserve_import_history()

            # Close all connections to the database
            self.connection_pool.close_all()

            # Create a temporary backup of current database
            temp_current = current_db_path + '.temp_current'
            if os.path.exists(current_db_path):
                shutil.copy2(current_db_path, temp_current)

            try:
                # Replace current database with backup
                shutil.copy2(backup_path, current_db_path)

                # Test the restored database
                test_conn = sqlite3.connect(current_db_path)
                cursor = test_conn.cursor()

                # Run a simple query to verify database integrity
                cursor.execute("SELECT COUNT(*) FROM clips_tbl")
                count = cursor.fetchone()[0]
                test_conn.close()

                print(f'[DEBUG] Database restored successfully. Clips count: {count}')

                # Step 2: Restore the preserved backup history
                print('[DEBUG] Restoring preserved backup history...')
                self._restore_backup_history(preserved_backup_history)
                self._restore_import_history(preserved_import_history)

                # Clean up temporary file
                if os.path.exists(temp_current):
                    os.remove(temp_current)

                return True

            except Exception as e:
                # Restore failed, revert to original database
                print(f'[ERROR] Restore failed, reverting: {e}')
                if os.path.exists(temp_current):
                    shutil.copy2(temp_current, current_db_path)
                    os.remove(temp_current)
                return False

        except Exception as e:
            print(f'[ERROR] Full restore operation failed: {e}')
            return False

    def _record_restore_history(self, backup_path: str, config: Dict[str, Any], success: bool):
        """Record restore operation in the import history table."""
        print('[DEBUG] RestoreManager._record_restore_history called')

        try:
            with self.connection_pool.get_connection() as conn:
                cursor = conn.cursor()

                # Record in import_history table (using it for restore history)
                # Using correct column names based on actual schema
                cursor.execute("""
                    INSERT INTO import_history (
                        source_file, source_format, records_imported,
                        import_config, import_status, import_summary
                    ) VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    backup_path,
                    'database_restore',  # Using source_format for restore type
                    1 if success else 0,  # 1 database restored or 0 if failed
                    json.dumps(config),
                    'completed' if success else 'failed',
                    f'Database restore {"successful" if success else "failed"} from {os.path.basename(backup_path)}'
                ))

                conn.commit()
                print('[DEBUG] Restore history recorded successfully')

        except Exception as e:
            print(f'[ERROR] Failed to record restore history: {e}')
            # Don't raise exception here as restore might have succeeded

    def _preserve_backup_history(self) -> List[Dict[str, Any]]:
        """Preserve current backup history before restore."""
        print('[DEBUG] RestoreManager._preserve_backup_history called')

        try:
            with self.connection_pool.get_connection() as conn:
                cursor = conn.cursor()

                # Check if backup_history table exists
                cursor.execute("""
                    SELECT name FROM sqlite_master
                    WHERE type='table' AND name='backup_history'
                """)

                if not cursor.fetchone():
                    print('[DEBUG] backup_history table does not exist, nothing to preserve')
                    return []

                # Get all backup history records
                cursor.execute("""
                    SELECT backup_id, backup_type, backup_path, file_size,
                           compression_ratio, verification_status, backup_config,
                           created_date, verified_date, restore_count, notes, checksum
                    FROM backup_history
                    ORDER BY backup_id
                """)

                records = cursor.fetchall()

                # Convert to list of dictionaries
                preserved_records = []
                for record in records:
                    preserved_records.append({
                        'backup_id': record[0],
                        'backup_type': record[1],
                        'backup_path': record[2],
                        'file_size': record[3],
                        'compression_ratio': record[4],
                        'verification_status': record[5],
                        'backup_config': record[6],
                        'created_date': record[7],
                        'verified_date': record[8],
                        'restore_count': record[9],
                        'notes': record[10],
                        'checksum': record[11]
                    })

                print(f'[DEBUG] Preserved {len(preserved_records)} backup history records')
                return preserved_records

        except Exception as e:
            print(f'[ERROR] Failed to preserve backup history: {e}')
            return []

    def _preserve_import_history(self) -> List[Dict[str, Any]]:
        """Preserve current import history before restore."""
        print('[DEBUG] RestoreManager._preserve_import_history called')

        try:
            with self.connection_pool.get_connection() as conn:
                cursor = conn.cursor()

                # Check if import_history table exists
                cursor.execute("""
                    SELECT name FROM sqlite_master
                    WHERE type='table' AND name='import_history'
                """)

                if not cursor.fetchone():
                    print('[DEBUG] import_history table does not exist, nothing to preserve')
                    return []

                # Get all import history records
                cursor.execute("""
                    SELECT import_id, source_file, source_format, records_imported,
                           records_skipped, duplicates_found, import_status, import_config,
                           import_date, completed_date, rollback_data, error_log, import_summary
                    FROM import_history
                    ORDER BY import_id
                """)

                records = cursor.fetchall()

                # Convert to list of dictionaries
                preserved_records = []
                for record in records:
                    preserved_records.append({
                        'import_id': record[0],
                        'source_file': record[1],
                        'source_format': record[2],
                        'records_imported': record[3],
                        'records_skipped': record[4],
                        'duplicates_found': record[5],
                        'import_status': record[6],
                        'import_config': record[7],
                        'import_date': record[8],
                        'completed_date': record[9],
                        'rollback_data': record[10],
                        'error_log': record[11],
                        'import_summary': record[12]
                    })

                print(f'[DEBUG] Preserved {len(preserved_records)} import history records')
                return preserved_records

        except Exception as e:
            print(f'[ERROR] Failed to preserve import history: {e}')
            return []

    def _restore_backup_history(self, preserved_records: List[Dict[str, Any]]):
        """Restore preserved backup history after database restore."""
        print('[DEBUG] RestoreManager._restore_backup_history called')

        if not preserved_records:
            print('[DEBUG] No backup history records to restore')
            return

        try:
            with self.connection_pool.get_connection() as conn:
                cursor = conn.cursor()

                # Ensure backup_history table exists (it should after restore)
                cursor.execute("""
                    SELECT name FROM sqlite_master
                    WHERE type='table' AND name='backup_history'
                """)

                if not cursor.fetchone():
                    print('[WARNING] backup_history table does not exist in restored database')
                    return

                # Clear any existing backup history in the restored database
                cursor.execute("DELETE FROM backup_history")

                # Insert preserved records
                for record in preserved_records:
                    cursor.execute("""
                        INSERT INTO backup_history (
                            backup_type, backup_path, file_size, compression_ratio,
                            verification_status, backup_config, created_date, verified_date,
                            restore_count, notes, checksum
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        record['backup_type'],
                        record['backup_path'],
                        record['file_size'],
                        record['compression_ratio'],
                        record['verification_status'],
                        record['backup_config'],
                        record['created_date'],
                        record['verified_date'],
                        record['restore_count'],
                        record['notes'],
                        record['checksum']
                    ))

                conn.commit()
                print(f'[DEBUG] Restored {len(preserved_records)} backup history records')

        except Exception as e:
            print(f'[ERROR] Failed to restore backup history: {e}')
            # Don't raise exception as the main restore was successful

    def _restore_import_history(self, preserved_records: List[Dict[str, Any]]):
        """Restore preserved import history after database restore."""
        print('[DEBUG] RestoreManager._restore_import_history called')

        if not preserved_records:
            print('[DEBUG] No import history records to restore')
            return

        try:
            with self.connection_pool.get_connection() as conn:
                cursor = conn.cursor()

                # Ensure import_history table exists (it should after restore)
                cursor.execute("""
                    SELECT name FROM sqlite_master
                    WHERE type='table' AND name='import_history'
                """)

                if not cursor.fetchone():
                    print('[WARNING] import_history table does not exist in restored database')
                    return

                # Get existing records to avoid duplicates
                cursor.execute("SELECT MAX(import_id) FROM import_history")
                max_id_result = cursor.fetchone()
                max_existing_id = max_id_result[0] if max_id_result[0] else 0

                # Insert preserved records (only those created after the backup)
                restored_count = 0
                for record in preserved_records:
                    # Only restore records that were created after the backup was made
                    if record['import_id'] > max_existing_id:
                        cursor.execute("""
                            INSERT INTO import_history (
                                source_file, source_format, records_imported, records_skipped,
                                duplicates_found, import_status, import_config, import_date,
                                completed_date, rollback_data, error_log, import_summary
                            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """, (
                            record['source_file'],
                            record['source_format'],
                            record['records_imported'],
                            record['records_skipped'],
                            record['duplicates_found'],
                            record['import_status'],
                            record['import_config'],
                            record['import_date'],
                            record['completed_date'],
                            record['rollback_data'],
                            record['error_log'],
                            record['import_summary']
                        ))
                        restored_count += 1

                conn.commit()
                print(f'[DEBUG] Restored {restored_count} import history records')

        except Exception as e:
            print(f'[ERROR] Failed to restore import history: {e}')
            # Don't raise exception as the main restore was successful
