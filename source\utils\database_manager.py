"""
🗄️ DatabaseManager - Centralizes all database operations and connections

This manager is responsible for:
- Providing centralized access to all database operation classes
- Managing database connections and error handling
- Executing database operations with proper error handling
- Providing helper methods for common database queries
- Caching frequently accessed data

Part of the UIManager refactoring to improve code organization and maintainability.
"""

from typing import List, Dict, Optional, Any, Callable


class DatabaseManager:
    """Centralizes all database operations and provides error handling"""
    
    def __init__(self):
        """Initialize DatabaseManager"""
        print('[DEBUG] DatabaseManager.__init__ called')
        
        self.db_path = "source/DB/clipsmore_db.db"
        
        # Cache for operation instances
        self._clips_ops = None
        self._more_ops = None
        self._enhanced_ops = None
        self._count_ops = None
        self._view_ops = None
        
        # Cache for frequently accessed data
        self._existing_aliases_cache = None
        self._cache_timestamp = None
    
    def get_clips_operations(self):
        """Get ClipsTableOperations instance"""
        print('[DEBUG] DatabaseManager.get_clips_operations called')
        
        if self._clips_ops is None:
            from source.DB.op_clips_tbl import ClipsTableOperations
            self._clips_ops = ClipsTableOperations()
        
        return self._clips_ops
    
    def get_more_operations(self):
        """Get MoreTableOperations instance"""
        print('[DEBUG] DatabaseManager.get_more_operations called')
        
        if self._more_ops is None:
            from source.DB.op_more_tbl import MoreTableOperations
            self._more_ops = MoreTableOperations()
        
        return self._more_ops
    
    def get_enhanced_operations(self):
        """Get ClipsMoreEnhancedOperations instance"""
        print('[DEBUG] DatabaseManager.get_enhanced_operations called')
        
        if self._enhanced_ops is None:
            from source.DB.op_clipsmore_enhanced import ClipsMoreEnhancedOperations
            self._enhanced_ops = ClipsMoreEnhancedOperations()
        
        return self._enhanced_ops
    
    def get_count_operations(self):
        """Get ClipsMoreCountOperations instance"""
        print('[DEBUG] DatabaseManager.get_count_operations called')
        
        if self._count_ops is None:
            from source.DB.op_clipsmore_count import ClipsMoreCountOperations
            self._count_ops = ClipsMoreCountOperations()
        
        return self._count_ops
    
    def get_view_operations(self):
        """Get ClipsMoreViewOperations instance"""
        print('[DEBUG] DatabaseManager.get_view_operations called')
        
        if self._view_ops is None:
            from source.DB.op_clipsmore_vw import ClipsMoreViewOperations
            self._view_ops = ClipsMoreViewOperations()
        
        return self._view_ops
    
    def execute_with_error_handling(self, operation: Callable, *args, **kwargs) -> Any:
        """Execute database operation with comprehensive error handling"""
        print(f'[DEBUG] DatabaseManager.execute_with_error_handling called for operation={operation.__name__}')
        
        try:
            result = operation(*args, **kwargs)
            print(f'[DEBUG] Operation {operation.__name__} completed successfully')
            return result
            
        except Exception as e:
            # Handle database-related exceptions generically since we're using API layer
            error_msg = str(e).lower()
            if 'integrity' in error_msg:
                print(f'[ERROR] Database integrity error in {operation.__name__}: {e}')
                raise Exception(f"Database integrity error: {str(e)}")
            elif 'operational' in error_msg or 'database' in error_msg:
                print(f'[ERROR] Database operational error in {operation.__name__}: {e}')
                raise Exception(f"Database operational error: {str(e)}")
            else:
                print(f'[ERROR] Database error in {operation.__name__}: {e}')
                raise Exception(f"Database error: {str(e)}")
            
        except Exception as e:
            print(f'[ERROR] Unexpected error in {operation.__name__}: {e}')
            raise Exception(f"Unexpected error: {str(e)}")
    
    def get_existing_aliases(self, force_refresh: bool = False) -> List[str]:
        """Get all existing aliases from database with caching"""
        print(f'[DEBUG] DatabaseManager.get_existing_aliases called with force_refresh={force_refresh}')
        
        import time
        current_time = time.time()
        
        # Use cache if available and not expired (cache for 30 seconds)
        if (not force_refresh and 
            self._existing_aliases_cache is not None and 
            self._cache_timestamp is not None and 
            current_time - self._cache_timestamp < 30):
            print('[DEBUG] Returning cached aliases')
            return self._existing_aliases_cache
        
        try:
            # Use view operations to get all assignments and extract aliases
            view_ops = self.get_view_operations()
            all_assignments = view_ops.read_clipsmore_view()

            aliases = []
            for assignment in all_assignments:
                alias = assignment.get('alias')
                if alias:
                    aliases.append(alias)

            # Update cache
            self._existing_aliases_cache = aliases
            self._cache_timestamp = current_time

            print(f'[DEBUG] Retrieved {len(aliases)} existing aliases from database')
            return aliases

        except Exception as e:
            print(f'[ERROR] Failed to get existing aliases: {e}')
            return []
    
    def get_business_case_id_by_name(self, name: str) -> Optional[int]:
        """Get business case ID by name"""
        print(f'[DEBUG] DatabaseManager.get_business_case_id_by_name called with name={name}')
        
        try:
            more_ops = self.get_more_operations()
            return more_ops.get_business_case_id_by_name(name)
            
        except Exception as e:
            print(f'[ERROR] Failed to get business case ID by name: {e}')
            return None
    
    def get_component_id_by_name(self, name: str) -> Optional[int]:
        """Get component ID by name"""
        print(f'[DEBUG] DatabaseManager.get_component_id_by_name called with name={name}')
        
        try:
            more_ops = self.get_more_operations()
            return more_ops.get_component_id_by_name(name)
            
        except Exception as e:
            print(f'[ERROR] Failed to get component ID by name: {e}')
            return None
    
    def get_component_id_by_name_in_business_case(self, comp_name: str, bus_id: int) -> Optional[int]:
        """Get component ID by name within a specific business case"""
        print(f'[DEBUG] DatabaseManager.get_component_id_by_name_in_business_case called with comp_name={comp_name}, bus_id={bus_id}')
        
        try:
            more_ops = self.get_more_operations()
            return more_ops.get_component_id_by_name_in_business_case(comp_name, bus_id)
            
        except Exception as e:
            print(f'[ERROR] Failed to get component ID by name in business case: {e}')
            return None
    
    def get_business_case_id_of_component(self, comp_id: int) -> Optional[int]:
        """Get business case ID that contains the specified component"""
        print(f'[DEBUG] DatabaseManager.get_business_case_id_of_component called with comp_id={comp_id}')
        
        try:
            more_ops = self.get_more_operations()
            return more_ops.get_business_case_id_of_component(comp_id)
            
        except Exception as e:
            print(f'[ERROR] Failed to get business case ID of component: {e}')
            return None
    
    def get_clip_content_by_alias(self, alias: str) -> Optional[str]:
        """Get clip content by alias using the view operations API"""
        print(f'[DEBUG] DatabaseManager.get_clip_content_by_alias called with alias={alias}')

        try:
            # Use the view operations API instead of direct database access
            view_ops = self.get_view_operations()

            # Query the view for the specific alias
            results = view_ops.read_clipsmore_view()

            # Find the matching alias
            for result in results:
                if result.get('alias') == alias:
                    content = result.get('clip')
                    print(f'[DEBUG] Found content for alias: {alias}')
                    return content

            print(f'[WARNING] No content found for alias: {alias}')
            return None

        except Exception as e:
            print(f'[ERROR] Failed to get clip content by alias: {e}')
            return None
    
    def get_assignment_by_alias(self, alias: str) -> Optional[Dict[str, Any]]:
        """Get assignment information by alias"""
        print(f'[DEBUG] DatabaseManager.get_assignment_by_alias called with alias={alias}')
        
        try:
            # Use enhanced operations API to find assignment by alias
            enhanced_ops = self.get_enhanced_operations()
            all_assignments = enhanced_ops.get_all_assignments()

            for assignment in all_assignments:
                if assignment.get('alias') == alias:
                    print(f'[DEBUG] Found assignment for alias: {alias}')
                    return assignment

            print(f'[WARNING] No assignment found for alias: {alias}')
            return None
                    
        except Exception as e:
            print(f'[ERROR] Failed to get assignment by alias: {e}')
            return None
    
    def clear_cache(self):
        """Clear all cached data"""
        print('[DEBUG] DatabaseManager.clear_cache called')

        self._existing_aliases_cache = None
        self._cache_timestamp = None

    def execute_database_operation_with_notification(self, operation: Callable, notification_manager=None, success_message: str = None, error_prefix: str = "Database operation failed", *args, **kwargs) -> Any:
        """Execute database operation with standardized error handling and notifications"""
        print(f'[DEBUG] DatabaseManager.execute_database_operation_with_notification called for operation={operation.__name__}')

        try:
            result = operation(*args, **kwargs)
            print(f'[DEBUG] Operation {operation.__name__} completed successfully')

            if success_message and notification_manager:
                notification_manager.show_success(success_message)

            return result

        except Exception as e:
            error_msg = f"{error_prefix}: {str(e)}"
            print(f'[ERROR] {error_msg}')

            if notification_manager:
                notification_manager.show_error(error_msg)
            else:
                # Fallback to console logging if no notification manager
                print(f'[ERROR] {error_msg}')

            raise e

    def handle_database_error(self, error: Exception, operation_name: str, notification_manager=None) -> str:
        """Standardized database error handling with user-friendly messages"""
        print(f'[DEBUG] DatabaseManager.handle_database_error called for operation={operation_name}')

        error_msg = str(error)
        user_friendly_msg = error_msg

        # Convert technical errors to user-friendly messages
        if "UNIQUE constraint failed" in error_msg:
            if "alias" in error_msg.lower():
                user_friendly_msg = "This alias is already in use. Please choose a different alias."
            elif "name" in error_msg.lower():
                user_friendly_msg = "This name is already in use. Please choose a different name."
            else:
                user_friendly_msg = "This item already exists. Please use a different value."
        elif "FOREIGN KEY constraint failed" in error_msg:
            user_friendly_msg = "Cannot complete operation due to data dependencies. Please check related items."
        elif "NOT NULL constraint failed" in error_msg:
            user_friendly_msg = "Required information is missing. Please fill in all required fields."
        elif "database is locked" in error_msg.lower():
            user_friendly_msg = "Database is temporarily unavailable. Please try again in a moment."

        print(f'[ERROR] {operation_name}: {user_friendly_msg}')

        if notification_manager:
            notification_manager.show_error(user_friendly_msg)

        return user_friendly_msg

    def get_business_case_id_by_name(self, name: str) -> Optional[int]:
        """Get business case ID by name - consolidated method"""
        print(f'[DEBUG] DatabaseManager.get_business_case_id_by_name called with name={name}')

        try:
            more_ops = self.get_more_operations()
            business_cases = more_ops.read_all_business_cases()

            for bc in business_cases:
                if bc.get('name', '').lower() == name.lower():
                    return bc.get('id')

            print(f'[DEBUG] Business case not found: {name}')
            return None

        except Exception as e:
            print(f'[ERROR] Failed to get business case ID by name: {e}')
            return None

    def get_component_id_by_name(self, name: str, business_case_id: Optional[int] = None) -> Optional[int]:
        """Get component ID by name, optionally within a specific business case - consolidated method"""
        print(f'[DEBUG] DatabaseManager.get_component_id_by_name called with name={name}, business_case_id={business_case_id}')

        try:
            more_ops = self.get_more_operations()

            if business_case_id:
                # Search within specific business case
                components = more_ops.read_components_for_business_case(business_case_id)
            else:
                # Search all components
                business_cases = more_ops.read_all_business_cases()
                components = []
                for bc in business_cases:
                    bc_components = more_ops.read_components_for_business_case(bc.get('id'))
                    components.extend(bc_components)

            for comp in components:
                if comp.get('name', '').lower() == name.lower():
                    return comp.get('id')

            print(f'[DEBUG] Component not found: {name}')
            return None

        except Exception as e:
            print(f'[ERROR] Failed to get component ID by name: {e}')
            return None

    def get_business_case_id_of_component(self, component_id: int) -> Optional[int]:
        """Get the business case ID that contains the specified component - consolidated method"""
        print(f'[DEBUG] DatabaseManager.get_business_case_id_of_component called with component_id={component_id}')

        try:
            more_ops = self.get_more_operations()
            business_cases = more_ops.read_all_business_cases()

            for bc in business_cases:
                components = more_ops.read_components_for_business_case(bc.get('id'))
                for comp in components:
                    if comp.get('id') == component_id:
                        return bc.get('id')

            print(f'[DEBUG] Business case not found for component ID: {component_id}')
            return None

        except Exception as e:
            print(f'[ERROR] Failed to get business case ID of component: {e}')
            return None
    
    def get_database_stats(self) -> Dict[str, int]:
        """Get database statistics"""
        print('[DEBUG] DatabaseManager.get_database_stats called')
        
        stats = {
            'clips_count': 0,
            'business_cases_count': 0,
            'components_count': 0,
            'assignments_count': 0
        }
        
        try:
            # Use API operations instead of direct database access
            clips_ops = self.get_clips_operations()
            more_ops = self.get_more_operations()
            enhanced_ops = self.get_enhanced_operations()

            # Count clips using clips operations
            all_clips = clips_ops.read_clips()
            stats['clips_count'] = len(all_clips)

            # Count business cases and components using more operations
            all_more = more_ops.read_more()
            business_cases = [item for item in all_more if item.get('more_comp_id') is None]
            components = [item for item in all_more if item.get('more_comp_id') is not None]
            stats['business_cases_count'] = len(business_cases)
            stats['components_count'] = len(components)

            # Count assignments using enhanced operations
            all_assignments = enhanced_ops.get_all_assignments()
            stats['assignments_count'] = len(all_assignments)

            print(f'[DEBUG] Database stats: {stats}')
            return stats

        except Exception as e:
            print(f'[ERROR] Failed to get database stats: {e}')
            return stats

    def truncate_all_tables(self, confirmation: str) -> bool:
        """Truncate ALL tables in the database with confirmation.

        This is a destructive operation that removes all data from all tables
        including clips, business cases, components, assignments, export templates,
        backup history, and import history.

        Args:
            confirmation: Must be "CONFIRM_TRUNCATE_ALL" to proceed

        Returns:
            bool: True if successful, False otherwise
        """
        print(f'[DEBUG] DatabaseManager.truncate_all_tables called with confirmation: {confirmation}')

        if confirmation != "CONFIRM_TRUNCATE_ALL":
            print('[ERROR] Invalid confirmation for truncate all tables')
            return False

        try:
            # Use API operations instead of direct database access
            total_deleted = 0

            # Clear assignments first (to avoid foreign key issues)
            enhanced_ops = self.get_enhanced_operations()
            assignments_deleted = enhanced_ops.clear_all_assignments()
            total_deleted += assignments_deleted
            print(f'[DEBUG] Cleared assignments: {assignments_deleted} rows deleted')

            # Clear components (references business cases)
            more_ops = self.get_more_operations()
            components_deleted = more_ops.clear_all_components()
            total_deleted += components_deleted
            print(f'[DEBUG] Cleared components: {components_deleted} rows deleted')

            # Clear business cases
            business_cases_deleted = more_ops.clear_all_business_cases()
            total_deleted += business_cases_deleted
            print(f'[DEBUG] Cleared business cases: {business_cases_deleted} rows deleted')

            # Clear clips
            clips_ops = self.get_clips_operations()
            clips_deleted = clips_ops.delete_all_clips("CONFIRM")
            total_deleted += clips_deleted
            print(f'[DEBUG] Cleared clips: {clips_deleted} rows deleted')

            print(f'[DEBUG] Successfully truncated all tables. Total rows deleted: {total_deleted}')
            return True

        except Exception as e:
            print(f'[ERROR] Failed to truncate all tables: {e}')
            return False
    
    def test_connection(self) -> bool:
        """Test database connection"""
        print('[DEBUG] DatabaseManager.test_connection called')
        
        try:
            # Test connection by trying to get clips operations
            clips_ops = self.get_clips_operations()
            # Try a simple read operation to test the connection
            clips_ops.read_clips(limit=1)
            print('[DEBUG] Database connection test successful')
            return True

        except Exception as e:
            print(f'[ERROR] Database connection test failed: {e}')
            return False
    
    def backup_database(self, backup_path: str) -> bool:
        """Create a backup of the database"""
        print(f'[DEBUG] DatabaseManager.backup_database called with backup_path={backup_path}')
        
        try:
            import shutil
            shutil.copy2(self.db_path, backup_path)
            print(f'[DEBUG] Database backed up to: {backup_path}')
            return True
            
        except Exception as e:
            print(f'[ERROR] Database backup failed: {e}')
            return False
