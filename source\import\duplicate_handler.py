#!/usr/bin/env python3
"""
Duplicate Handler for ClipsMore Import System
Handles detection and resolution of duplicate records during import.
"""

import hashlib
import sqlite3
from typing import Dict, List, Any, Tuple, Optional
from DB.db_connection import ConnectionPoolManager


class DuplicateHandler:
    """
    Handles duplicate detection and resolution during import operations.
    """
    
    def __init__(self):
        """Initialize the duplicate handler."""
        print('[DEBUG] DuplicateHandler.__init__ called')
        self.connection_pool = ConnectionPoolManager()
        self.duplicate_strategies = {
            'skip': self._skip_duplicate,
            'replace': self._replace_duplicate,
            'merge': self._merge_duplicate
        }
    
    def detect_duplicates(self, import_records: List[Dict[str, Any]], 
                         strategy: str = 'content') -> Dict[str, Any]:
        """
        Detect duplicates in import records and existing database records.
        
        Args:
            import_records: List of records to import
            strategy: Detection strategy ('content', 'alias', 'combined')
            
        Returns:
            Dictionary containing duplicate analysis
        """
        print(f'[DEBUG] DuplicateHandler.detect_duplicates called with strategy: {strategy}')
        
        duplicate_info = {
            'internal_duplicates': [],  # Duplicates within import data
            'external_duplicates': [],  # Duplicates with existing data
            'total_duplicates': 0,
            'unique_records': []
        }
        
        if strategy == 'content':
            duplicate_info = self._detect_content_duplicates(import_records)
        elif strategy == 'alias':
            duplicate_info = self._detect_alias_duplicates(import_records)
        elif strategy == 'combined':
            duplicate_info = self._detect_combined_duplicates(import_records)
        
        return duplicate_info
    
    def _detect_content_duplicates(self, records: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Detect duplicates based on content similarity."""
        print('[DEBUG] DuplicateHandler._detect_content_duplicates called')
        
        content_hashes = {}
        internal_duplicates = []
        unique_records = []
        
        for i, record in enumerate(records):
            # Create content hash
            content = record.get('content', '') or record.get('clip', '')
            content_hash = self._create_content_hash(content)
            
            if content_hash in content_hashes:
                # Found internal duplicate
                original_index = content_hashes[content_hash]
                internal_duplicates.append({
                    'original_index': original_index,
                    'duplicate_index': i,
                    'match_type': 'content',
                    'similarity': 1.0
                })
            else:
                content_hashes[content_hash] = i
                unique_records.append(record)
        
        # Check for external duplicates (against existing database)
        external_duplicates = self._check_external_duplicates(unique_records, 'content')
        
        return {
            'internal_duplicates': internal_duplicates,
            'external_duplicates': external_duplicates,
            'total_duplicates': len(internal_duplicates) + len(external_duplicates),
            'unique_records': unique_records
        }
    
    def _detect_alias_duplicates(self, records: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Detect duplicates based on alias matching."""
        print('[DEBUG] DuplicateHandler._detect_alias_duplicates called')
        
        alias_map = {}
        internal_duplicates = []
        unique_records = []
        
        for i, record in enumerate(records):
            alias = record.get('alias', '')
            if not alias:
                unique_records.append(record)
                continue
            
            if alias in alias_map:
                # Found internal duplicate
                original_index = alias_map[alias]
                internal_duplicates.append({
                    'original_index': original_index,
                    'duplicate_index': i,
                    'match_type': 'alias',
                    'similarity': 1.0
                })
            else:
                alias_map[alias] = i
                unique_records.append(record)
        
        # Check for external duplicates
        external_duplicates = self._check_external_duplicates(unique_records, 'alias')
        
        return {
            'internal_duplicates': internal_duplicates,
            'external_duplicates': external_duplicates,
            'total_duplicates': len(internal_duplicates) + len(external_duplicates),
            'unique_records': unique_records
        }
    
    def _detect_combined_duplicates(self, records: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Detect duplicates using combined content and alias matching."""
        print('[DEBUG] DuplicateHandler._detect_combined_duplicates called')
        
        # First check content duplicates
        content_result = self._detect_content_duplicates(records)
        
        # Then check alias duplicates on the unique records
        alias_result = self._detect_alias_duplicates(content_result['unique_records'])
        
        # Combine results
        return {
            'internal_duplicates': content_result['internal_duplicates'] + alias_result['internal_duplicates'],
            'external_duplicates': content_result['external_duplicates'] + alias_result['external_duplicates'],
            'total_duplicates': (len(content_result['internal_duplicates']) + 
                               len(content_result['external_duplicates']) +
                               len(alias_result['internal_duplicates']) + 
                               len(alias_result['external_duplicates'])),
            'unique_records': alias_result['unique_records']
        }
    
    def _create_content_hash(self, content: str) -> str:
        """Create a hash of the content for duplicate detection."""
        if not content:
            return ''
        
        # Normalize content (remove extra whitespace, convert to lowercase)
        normalized = ' '.join(content.lower().split())
        
        # Create SHA-256 hash
        return hashlib.sha256(normalized.encode('utf-8')).hexdigest()
    
    def _check_external_duplicates(self, records: List[Dict[str, Any]], 
                                 match_type: str) -> List[Dict[str, Any]]:
        """Check for duplicates against existing database records."""
        print(f'[DEBUG] DuplicateHandler._check_external_duplicates called with match_type: {match_type}')
        
        external_duplicates = []
        
        try:
            with self.connection_pool.get_connection() as conn:
                cursor = conn.cursor()
                
                for i, record in enumerate(records):
                    if match_type == 'content':
                        # Check content similarity
                        content = record.get('content', '') or record.get('clip', '')
                        if content:
                            content_hash = self._create_content_hash(content)
                            
                            # Query for similar content (simplified - would need better similarity matching)
                            cursor.execute("""
                                SELECT clip_id, clip FROM clips_tbl 
                                WHERE LENGTH(clip) = ? 
                                LIMIT 100
                            """, (len(content),))
                            
                            for clip_id, existing_content in cursor.fetchall():
                                if isinstance(existing_content, bytes):
                                    existing_content = existing_content.decode('utf-8', errors='ignore')
                                
                                existing_hash = self._create_content_hash(existing_content)
                                if existing_hash == content_hash:
                                    external_duplicates.append({
                                        'import_index': i,
                                        'existing_clip_id': clip_id,
                                        'match_type': 'content',
                                        'similarity': 1.0
                                    })
                                    break
                    
                    elif match_type == 'alias':
                        # Check alias matching
                        alias = record.get('alias', '')
                        if alias:
                            cursor.execute("""
                                SELECT clip_id FROM clips_tbl 
                                WHERE alias = ?
                            """, (alias,))
                            
                            result = cursor.fetchone()
                            if result:
                                external_duplicates.append({
                                    'import_index': i,
                                    'existing_clip_id': result[0],
                                    'match_type': 'alias',
                                    'similarity': 1.0
                                })
                
        except sqlite3.Error as e:
            print(f'[ERROR] Database error checking external duplicates: {e}')
        
        return external_duplicates
    
    def resolve_duplicates(self, duplicate_info: Dict[str, Any], 
                          strategy: str = 'skip') -> List[Dict[str, Any]]:
        """
        Resolve duplicates using the specified strategy.
        
        Args:
            duplicate_info: Duplicate detection results
            strategy: Resolution strategy ('skip', 'replace', 'merge')
            
        Returns:
            List of records to import after duplicate resolution
        """
        print(f'[DEBUG] DuplicateHandler.resolve_duplicates called with strategy: {strategy}')
        
        if strategy not in self.duplicate_strategies:
            raise ValueError(f"Unknown duplicate strategy: {strategy}")
        
        resolver = self.duplicate_strategies[strategy]
        return resolver(duplicate_info)
    
    def _skip_duplicate(self, duplicate_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Skip duplicate records, keep only unique ones."""
        print('[DEBUG] DuplicateHandler._skip_duplicate called')
        
        # Return only unique records
        return duplicate_info['unique_records']
    
    def _replace_duplicate(self, duplicate_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Replace existing duplicates with new records."""
        print('[DEBUG] DuplicateHandler._replace_duplicate called')
        
        # For now, return unique records (replacement logic would need database updates)
        # In a full implementation, this would mark existing records for update
        return duplicate_info['unique_records']
    
    def _merge_duplicate(self, duplicate_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Merge duplicate records with existing ones."""
        print('[DEBUG] DuplicateHandler._merge_duplicate called')
        
        # For now, return unique records (merge logic would combine data)
        # In a full implementation, this would merge fields from duplicates
        return duplicate_info['unique_records']
    
    def get_duplicate_statistics(self, duplicate_info: Dict[str, Any]) -> Dict[str, Any]:
        """Get statistics about duplicate detection results."""
        print('[DEBUG] DuplicateHandler.get_duplicate_statistics called')
        
        return {
            'total_records_analyzed': len(duplicate_info.get('unique_records', [])) + duplicate_info.get('total_duplicates', 0),
            'unique_records': len(duplicate_info.get('unique_records', [])),
            'internal_duplicates': len(duplicate_info.get('internal_duplicates', [])),
            'external_duplicates': len(duplicate_info.get('external_duplicates', [])),
            'total_duplicates': duplicate_info.get('total_duplicates', 0),
            'duplicate_percentage': (duplicate_info.get('total_duplicates', 0) / 
                                   max(1, len(duplicate_info.get('unique_records', [])) + 
                                       duplicate_info.get('total_duplicates', 0))) * 100
        }
