#!/usr/bin/env python3
"""
Export Dialog for ClipsMore
Provides user interface for exporting clipboard data in various formats.
"""

import os
import sys
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

# Add parent directories to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from export.export_manager import ExportManager
from export.format_handlers.json_handler import JSONHandler
from export.format_handlers.csv_handler import CSVHandler


class ExportDialog(tk.Toplevel):
    """
    Export dialog for ClipsMore application.
    Provides comprehensive interface for exporting clipboard data.
    """
    
    def __init__(self, parent, database_manager=None, theme_manager=None):
        """Initialize the export dialog."""
        print('[DEBUG] ExportDialog.__init__ called')
        super().__init__(parent)
        
        self.parent = parent
        self.database_manager = database_manager
        self.theme_manager = theme_manager
        self.export_manager = ExportManager(database_manager)
        
        # Initialize format handlers
        self.export_manager.register_format_handler('json', JSONHandler())
        self.export_manager.register_format_handler('csv', CSVHandler())
        
        # Dialog state
        self.export_in_progress = False
        self.selected_format = tk.StringVar(value='json')
        self.output_path = tk.StringVar()
        
        # Selection criteria variables
        self.date_from = tk.StringVar()
        self.date_to = tk.StringVar()
        self.include_unassigned = tk.BooleanVar(value=True)
        self.content_filter = tk.StringVar()
        
        # Setup dialog
        self._setup_dialog()
        self._create_interface()
        self._apply_theme()
        
        # Set progress callback
        self.export_manager.set_progress_callback(self._update_progress)
    
    def _setup_dialog(self):
        """Setup dialog properties."""
        print('[DEBUG] ExportDialog._setup_dialog called')
        
        self.title("Export ClipsMore Data")
        self.geometry("600x500")
        self.resizable(True, True)
        
        # Center on parent
        self.transient(self.parent)
        self.grab_set()
        
        # Position relative to parent
        parent_x = self.parent.winfo_rootx()
        parent_y = self.parent.winfo_rooty()
        parent_width = self.parent.winfo_width()
        parent_height = self.parent.winfo_height()
        
        x = parent_x + (parent_width // 2) - 300
        y = parent_y + (parent_height // 2) - 250
        
        self.geometry(f"600x500+{x}+{y}")
    
    def _create_interface(self):
        """Create the dialog interface."""
        print('[DEBUG] ExportDialog._create_interface called')
        
        # Main container
        main_frame = ttk.Frame(self)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Create notebook for tabs
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # Format selection tab
        format_frame = ttk.Frame(notebook)
        notebook.add(format_frame, text="Format & Output")
        self._create_format_tab(format_frame)
        
        # Filter options tab
        filter_frame = ttk.Frame(notebook)
        notebook.add(filter_frame, text="Filters")
        self._create_filter_tab(filter_frame)
        
        # Preview tab
        preview_frame = ttk.Frame(notebook)
        notebook.add(preview_frame, text="Preview")
        self._create_preview_tab(preview_frame)
        
        # Progress and buttons
        self._create_progress_section(main_frame)
        self._create_button_section(main_frame)
    
    def _create_format_tab(self, parent):
        """Create format selection tab."""
        print('[DEBUG] ExportDialog._create_format_tab called')
        
        # Format selection
        format_group = ttk.LabelFrame(parent, text="Export Format")
        format_group.pack(fill=tk.X, padx=5, pady=5)
        
        formats = [
            ('json', 'JSON - Complete structured export'),
            ('csv', 'CSV - Flat table format')
        ]
        
        for value, text in formats:
            ttk.Radiobutton(
                format_group,
                text=text,
                variable=self.selected_format,
                value=value,
                command=self._on_format_changed
            ).pack(anchor=tk.W, padx=10, pady=2)
        
        # Output path selection
        output_group = ttk.LabelFrame(parent, text="Output Location")
        output_group.pack(fill=tk.X, padx=5, pady=5)
        
        path_frame = ttk.Frame(output_group)
        path_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Entry(
            path_frame,
            textvariable=self.output_path,
            width=50
        ).pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        ttk.Button(
            path_frame,
            text="Browse...",
            command=self._browse_output_path
        ).pack(side=tk.RIGHT, padx=(5, 0))
        
        # Format-specific options
        self.format_options_frame = ttk.LabelFrame(parent, text="Format Options")
        self.format_options_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self._create_format_options()
    
    def _create_filter_tab(self, parent):
        """Create filter options tab."""
        print('[DEBUG] ExportDialog._create_filter_tab called')
        
        # Date range filter
        date_group = ttk.LabelFrame(parent, text="Date Range")
        date_group.pack(fill=tk.X, padx=5, pady=5)
        
        date_frame = ttk.Frame(date_group)
        date_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(date_frame, text="From:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        ttk.Entry(date_frame, textvariable=self.date_from, width=15).grid(row=0, column=1, padx=(0, 10))
        
        ttk.Label(date_frame, text="To:").grid(row=0, column=2, sticky=tk.W, padx=(0, 5))
        ttk.Entry(date_frame, textvariable=self.date_to, width=15).grid(row=0, column=3)
        
        # Quick date buttons
        quick_frame = ttk.Frame(date_group)
        quick_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(quick_frame, text="Last 7 days", command=lambda: self._set_date_range(7)).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(quick_frame, text="Last 30 days", command=lambda: self._set_date_range(30)).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(quick_frame, text="All time", command=self._clear_date_range).pack(side=tk.LEFT)
        
        # Content filter
        content_group = ttk.LabelFrame(parent, text="Content Filter")
        content_group.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(content_group, text="Search text:").pack(anchor=tk.W, padx=10, pady=(5, 0))
        ttk.Entry(content_group, textvariable=self.content_filter).pack(fill=tk.X, padx=10, pady=5)
        
        # Assignment filter
        assignment_group = ttk.LabelFrame(parent, text="Assignment Filter")
        assignment_group.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Checkbutton(
            assignment_group,
            text="Include unassigned clips",
            variable=self.include_unassigned
        ).pack(anchor=tk.W, padx=10, pady=5)
    
    def _create_preview_tab(self, parent):
        """Create preview tab."""
        print('[DEBUG] ExportDialog._create_preview_tab called')
        
        # Preview controls
        control_frame = ttk.Frame(parent)
        control_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(
            control_frame,
            text="Generate Preview",
            command=self._generate_preview
        ).pack(side=tk.LEFT)
        
        self.preview_count_label = ttk.Label(control_frame, text="")
        self.preview_count_label.pack(side=tk.RIGHT)
        
        # Preview text area
        preview_frame = ttk.Frame(parent)
        preview_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.preview_text = tk.Text(preview_frame, wrap=tk.WORD, state=tk.DISABLED)
        preview_scrollbar = ttk.Scrollbar(preview_frame, orient=tk.VERTICAL, command=self.preview_text.yview)
        self.preview_text.configure(yscrollcommand=preview_scrollbar.set)
        
        self.preview_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        preview_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def _create_progress_section(self, parent):
        """Create progress section."""
        print('[DEBUG] ExportDialog._create_progress_section called')
        
        progress_frame = ttk.Frame(parent)
        progress_frame.pack(fill=tk.X, pady=(10, 0))
        
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            progress_frame,
            variable=self.progress_var,
            maximum=100
        )
        self.progress_bar.pack(fill=tk.X, pady=(0, 5))
        
        self.progress_label = ttk.Label(progress_frame, text="Ready to export")
        self.progress_label.pack(anchor=tk.W)
    
    def _create_button_section(self, parent):
        """Create button section."""
        print('[DEBUG] ExportDialog._create_button_section called')
        
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(
            button_frame,
            text="Cancel",
            command=self._cancel_export
        ).pack(side=tk.RIGHT, padx=(5, 0))
        
        self.export_button = ttk.Button(
            button_frame,
            text="Export",
            command=self._start_export
        )
        self.export_button.pack(side=tk.RIGHT)
    
    def _create_format_options(self):
        """Create format-specific options."""
        print('[DEBUG] ExportDialog._create_format_options called')
        
        # Clear existing options
        for widget in self.format_options_frame.winfo_children():
            widget.destroy()
        
        format_type = self.selected_format.get()
        
        if format_type == 'json':
            self._create_json_options()
        elif format_type == 'csv':
            self._create_csv_options()
    
    def _create_json_options(self):
        """Create JSON format options."""
        print('[DEBUG] ExportDialog._create_json_options called')
        
        self.json_hierarchical = tk.BooleanVar(value=False)
        self.json_include_metadata = tk.BooleanVar(value=True)
        self.json_pretty_print = tk.BooleanVar(value=True)
        
        ttk.Checkbutton(
            self.format_options_frame,
            text="Hierarchical structure (group by business cases)",
            variable=self.json_hierarchical
        ).pack(anchor=tk.W, padx=10, pady=2)
        
        ttk.Checkbutton(
            self.format_options_frame,
            text="Include metadata and statistics",
            variable=self.json_include_metadata
        ).pack(anchor=tk.W, padx=10, pady=2)
        
        ttk.Checkbutton(
            self.format_options_frame,
            text="Pretty print (formatted for readability)",
            variable=self.json_pretty_print
        ).pack(anchor=tk.W, padx=10, pady=2)
    
    def _create_csv_options(self):
        """Create CSV format options."""
        print('[DEBUG] ExportDialog._create_csv_options called')
        
        self.csv_include_header = tk.BooleanVar(value=True)
        self.csv_encoding = tk.StringVar(value='utf-8')
        self.csv_delimiter = tk.StringVar(value=',')
        
        ttk.Checkbutton(
            self.format_options_frame,
            text="Include column headers",
            variable=self.csv_include_header
        ).pack(anchor=tk.W, padx=10, pady=2)
        
        # Encoding selection
        encoding_frame = ttk.Frame(self.format_options_frame)
        encoding_frame.pack(fill=tk.X, padx=10, pady=2)
        
        ttk.Label(encoding_frame, text="Encoding:").pack(side=tk.LEFT)
        encoding_combo = ttk.Combobox(
            encoding_frame,
            textvariable=self.csv_encoding,
            values=['utf-8', 'utf-8-sig', 'latin1', 'cp1252'],
            state='readonly',
            width=15
        )
        encoding_combo.pack(side=tk.LEFT, padx=(5, 0))
        
        # Delimiter selection
        delimiter_frame = ttk.Frame(self.format_options_frame)
        delimiter_frame.pack(fill=tk.X, padx=10, pady=2)
        
        ttk.Label(delimiter_frame, text="Delimiter:").pack(side=tk.LEFT)
        delimiter_combo = ttk.Combobox(
            delimiter_frame,
            textvariable=self.csv_delimiter,
            values=[',', ';', '\t', '|'],
            state='readonly',
            width=10
        )
        delimiter_combo.pack(side=tk.LEFT, padx=(5, 0))
    
    def _apply_theme(self):
        """Apply theme to dialog."""
        if self.theme_manager:
            # Apply theme colors and styles
            pass
    
    def _on_format_changed(self):
        """Handle format selection change."""
        print('[DEBUG] ExportDialog._on_format_changed called')
        self._create_format_options()
        self._update_output_path_extension()
    
    def _update_output_path_extension(self):
        """Update output path extension based on selected format."""
        current_path = self.output_path.get()
        if current_path:
            base_path = os.path.splitext(current_path)[0]
            format_type = self.selected_format.get()
            
            if format_type == 'json':
                self.output_path.set(base_path + '.json')
            elif format_type == 'csv':
                self.output_path.set(base_path + '.csv')
    
    def _browse_output_path(self):
        """Browse for output file path."""
        print('[DEBUG] ExportDialog._browse_output_path called')
        
        format_type = self.selected_format.get()
        
        if format_type == 'json':
            filetypes = [("JSON files", "*.json"), ("All files", "*.*")]
            default_ext = ".json"
        elif format_type == 'csv':
            filetypes = [("CSV files", "*.csv"), ("All files", "*.*")]
            default_ext = ".csv"
        else:
            filetypes = [("All files", "*.*")]
            default_ext = ""
        
        filename = filedialog.asksaveasfilename(
            parent=self,
            title="Save Export As",
            filetypes=filetypes,
            defaultextension=default_ext
        )
        
        if filename:
            self.output_path.set(filename)
    
    def _set_date_range(self, days: int):
        """Set date range for last N days."""
        print(f'[DEBUG] ExportDialog._set_date_range called for {days} days')
        
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        self.date_from.set(start_date.strftime("%Y-%m-%d"))
        self.date_to.set(end_date.strftime("%Y-%m-%d"))
    
    def _clear_date_range(self):
        """Clear date range filters."""
        print('[DEBUG] ExportDialog._clear_date_range called')
        self.date_from.set("")
        self.date_to.set("")
    
    def _generate_preview(self):
        """Generate export preview."""
        print('[DEBUG] ExportDialog._generate_preview called')
        
        try:
            # Get selection criteria
            criteria = self._get_selection_criteria()
            
            # Collect preview data (limited)
            preview_data = self.export_manager.collect_export_data(criteria)
            
            # Limit preview to first 10 records
            preview_data = preview_data[:10]
            
            # Update preview
            self.preview_text.config(state=tk.NORMAL)
            self.preview_text.delete(1.0, tk.END)
            
            if preview_data:
                preview_text = f"Preview of first {len(preview_data)} records:\n\n"
                
                for i, record in enumerate(preview_data, 1):
                    preview_text += f"{i}. {record.get('alias', 'No alias')} - "
                    preview_text += f"{record.get('content', '')[:50]}...\n"
                    preview_text += f"   Business Case: {record.get('bus_case', 'None')}\n"
                    preview_text += f"   Component: {record.get('bus_component', 'None')}\n\n"
                
                self.preview_text.insert(1.0, preview_text)
                self.preview_count_label.config(text=f"Total records to export: {len(preview_data)}")
            else:
                self.preview_text.insert(1.0, "No records match the current filters.")
                self.preview_count_label.config(text="Total records: 0")
            
            self.preview_text.config(state=tk.DISABLED)
            
        except Exception as e:
            messagebox.showerror("Preview Error", f"Failed to generate preview: {e}")
    
    def _get_selection_criteria(self) -> Dict[str, Any]:
        """Get current selection criteria."""
        criteria = {}
        
        if self.date_from.get():
            criteria['date_from'] = self.date_from.get()
        
        if self.date_to.get():
            criteria['date_to'] = self.date_to.get()
        
        if self.content_filter.get():
            criteria['content_filter'] = self.content_filter.get()
        
        criteria['include_unassigned'] = self.include_unassigned.get()
        
        return criteria
    
    def _get_export_config(self) -> Dict[str, Any]:
        """Get current export configuration."""
        format_type = self.selected_format.get()
        config = {}
        
        if format_type == 'json':
            config = {
                'hierarchical': self.json_hierarchical.get(),
                'include_metadata': self.json_include_metadata.get(),
                'pretty_print': self.json_pretty_print.get()
            }
        elif format_type == 'csv':
            config = {
                'include_header': self.csv_include_header.get(),
                'encoding': self.csv_encoding.get(),
                'delimiter': self.csv_delimiter.get()
            }
        
        return config
    
    def _start_export(self):
        """Start the export process."""
        print('[DEBUG] ExportDialog._start_export called')
        
        # Validate inputs
        if not self.output_path.get():
            messagebox.showerror("Export Error", "Please select an output file path.")
            return
        
        if not self.export_manager.validate_export_path(self.output_path.get()):
            messagebox.showerror("Export Error", "Cannot write to the selected output path.")
            return
        
        try:
            self.export_in_progress = True
            self.export_button.config(state=tk.DISABLED)
            
            # Get export parameters
            format_type = self.selected_format.get()
            criteria = self._get_selection_criteria()
            config = self._get_export_config()
            
            # Start export
            success = self.export_manager.export_data(
                format_type,
                criteria,
                self.output_path.get(),
                config
            )
            
            if success:
                messagebox.showinfo("Export Complete", f"Data exported successfully to:\n{self.output_path.get()}")
                self.destroy()
            else:
                messagebox.showerror("Export Error", "Export failed. Please check the logs.")
            
        except Exception as e:
            messagebox.showerror("Export Error", f"Export failed: {e}")
        finally:
            self.export_in_progress = False
            self.export_button.config(state=tk.NORMAL)
    
    def _cancel_export(self):
        """Cancel export or close dialog."""
        print('[DEBUG] ExportDialog._cancel_export called')
        
        if self.export_in_progress:
            self.export_manager.cancel_export()
        else:
            self.destroy()
    
    def _update_progress(self, percentage: int, message: str):
        """Update progress display."""
        self.progress_var.set(percentage)
        self.progress_label.config(text=message)
        self.update_idletasks()
