"""
EventManager - Centralized event handling for ClipsMore application

This module handles all UI events including:
- Tree view interactions (click, double-click, drag & drop)
- Context menus
- Entry field changes
- Button events

Author: ClipsMore Development Team
Date: 2025-06-16
"""

import tkinter as tk
from tkinter import messagebox
from typing import Dict, Any, Optional


class EventManager:
    """
    Centralized event handling manager for the ClipsMore application.
    
    Handles all UI events including tree interactions, drag & drop,
    context menus, and form field changes.
    """
    
    def __init__(self, ui_components: Dict[str, Any], ui_manager=None):
        """
        Initialize EventManager with UI component references.
        
        Args:
            ui_components: Dictionary containing references to UI components
                          Expected keys: tree, tree_manager, clip_manager, 
                          database_manager, theme_manager, validation_manager
        """
        print('[DEBUG] EventManager.__init__ called')
        
        # Store component references
        self.ui_components = ui_components
        self.tree = ui_components.get('tree')
        self.tree_manager = ui_components.get('tree_manager')
        self.clip_manager = ui_components.get('clip_manager')
        self.database_manager = ui_components.get('database_manager')
        self.theme_manager = ui_components.get('theme_manager')
        self.validation_manager = ui_components.get('validation_manager')
        self.ui_manager = ui_manager
        
        # Event state tracking
        self.drag_source = None
        self.selected_tree_item = None
        self.selected_tree_item_type = None
        self.selected_tree_item_name = None
        
        print('[DEBUG] EventManager initialized successfully')
    
    def bind_events(self):
        """
        Bind all event handlers to their respective UI components.
        """
        print('[DEBUG] EventManager.bind_events called')
        
        if self.tree:
            # Tree view events
            self.tree.bind('<ButtonRelease-1>', self.handle_tree_click)
            self.tree.bind('<B1-Motion>', self.handle_drag_motion)
            self.tree.bind('<ButtonPress-1>', self.handle_tree_press)
            self.tree.bind('<Button-3>', self.handle_context_menu)
            self.tree.bind('<Double-1>', self.handle_double_click)
            
            print('[DEBUG] Tree event bindings completed')
        else:
            print('[WARNING] Tree component not available for event binding')
    
    def handle_tree_click(self, event: tk.Event):
        """
        Handle tree item click events.
        
        Args:
            event: Tkinter event object
        """
        print('[DEBUG] EventManager.handle_tree_click called')
        
        # Reset cursor
        if self.tree:
            self.tree.config(cursor="")
        
        if hasattr(self, 'drag_source') and self.drag_source:
            target_item = self.tree.identify_row(event.y)
            
            if target_item and target_item != self.drag_source['item']:
                target_values = self.tree.item(target_item, 'values')
                target_text = self.tree.item(target_item, 'text')
                
                if target_values and target_values[0] in ['Business Case', 'Component']:
                    # Valid drop - show context menu
                    self._show_drag_context_menu(event, target_item, target_values[0], target_text)
            
            # Clear drag source
            self.drag_source = None
        
        # Handle regular tree item selection
        item = self.tree.identify_row(event.y)
        if item:
            item_type = self.tree.item(item, 'values')[0] if self.tree.item(item, 'values') else None
            name = self.tree.item(item, 'text')
            
            # Update selection tracking
            self.selected_tree_item = item
            self.selected_tree_item_type = item_type
            self.selected_tree_item_name = name
            
            # Update UI components based on selection
            self._update_ui_for_selection(item_type, name)
    
    def handle_drag_motion(self, event: tk.Event):
        """
        Handle drag motion events.
        
        Args:
            event: Tkinter event object
        """
        if hasattr(self, 'drag_source') and self.drag_source:
            # Calculate drag distance
            dx = abs(event.x - self.drag_source['start_x'])
            dy = abs(event.y - self.drag_source['start_y'])
            
            # Only start visual drag feedback if moved enough
            if dx > 5 or dy > 5:
                # Change cursor to indicate dragging
                if self.tree:
                    self.tree.config(cursor="hand2")
                
                # Highlight potential drop targets
                target_item = self.tree.identify_row(event.y)
                if target_item and target_item != self.drag_source['item']:
                    target_values = self.tree.item(target_item, 'values')
                    if target_values and target_values[0] in ['Business Case', 'Component']:
                        # Valid drop target
                        self.tree.selection_set(target_item)
    
    def handle_tree_press(self, event: tk.Event):
        """
        Handle tree item press events for drag initiation.
        
        Args:
            event: Tkinter event object
        """
        print('[DEBUG] EventManager.handle_tree_press called')
        
        if not self.tree:
            return
        
        item = self.tree.identify_row(event.y)
        if item:
            item_values = self.tree.item(item, 'values')
            if item_values and item_values[0] == 'Clip Button':
                # Store drag source information
                self.drag_source = {
                    'item': item,
                    'type': 'clip_button',
                    'alias': self.tree.item(item, 'text').replace('📎 ', '').strip(),
                    'start_x': event.x,
                    'start_y': event.y
                }
                print(f'[DEBUG] Drag initiated for clip button: {self.drag_source["alias"]}')
            else:
                self.drag_source = None
    
    def handle_context_menu(self, event: tk.Event):
        """
        Handle right-click context menu events.
        
        Args:
            event: Tkinter event object
        """
        print('[DEBUG] EventManager.handle_context_menu called')
        
        if not self.tree or not self.theme_manager:
            return
        
        item = self.tree.identify_row(event.y)
        if item:
            self.tree.selection_set(item)
            context_menu = tk.Menu(self.tree.master, tearoff=0,
                                 bg=self.theme_manager.button_bg,
                                 fg=self.theme_manager.button_fg,
                                 activebackground=self.theme_manager.tree_select,
                                 activeforeground=self.theme_manager.fg_color)
            
            context_menu.add_command(label="Move", command=lambda: self._handle_move(item))
            context_menu.add_command(label="Copy", command=lambda: self._handle_copy(item))
            
            try:
                context_menu.post(event.x_root, event.y_root)
            except tk.TclError:
                pass  # Menu might be destroyed already
    
    def handle_double_click(self, event: tk.Event):
        """
        Handle double-click events on tree items.
        
        Args:
            event: Tkinter event object
        """
        print('[DEBUG] EventManager.handle_double_click called')
        
        if not self.tree:
            return
        
        item = self.tree.identify_row(event.y)
        if not item:
            return
        
        item_values = self.tree.item(item, 'values')
        item_text = self.tree.item(item, 'text')
        
        # Check if it's a clip button
        if item_values and item_values[0] == 'Clip Button':
            # Extract alias from the text (remove the 📎 emoji)
            alias = item_text.replace('📎 ', '').strip()
            self._copy_clip_by_alias(alias)
    
    def handle_entry_change(self, entry_type: str, event: Optional[tk.Event] = None):
        """
        Handle entry field change events.
        
        Args:
            entry_type: Type of entry ('business_case' or 'component')
            event: Optional Tkinter event object
        """
        print(f'[DEBUG] EventManager.handle_entry_change called for {entry_type}')
        
        if entry_type == 'business_case':
            self._handle_business_case_change()
        elif entry_type == 'component':
            self._handle_component_change()
    
    def _update_ui_for_selection(self, item_type: str, name: str):
        """
        Update UI components based on tree selection.
        
        Args:
            item_type: Type of selected item
            name: Name of selected item
        """
        if self.ui_manager and self.selected_tree_item:
            self.ui_manager.on_tree_selection_changed(self.selected_tree_item, item_type, name)
    
    def _show_drag_context_menu(self, event: tk.Event, target_item: str, target_type: str, target_name: str):
        """
        Show context menu for drag and drop operations.
        
        Args:
            event: Tkinter event object
            target_item: Target tree item ID
            target_type: Type of target item
            target_name: Name of target item
        """
        print(f'[DEBUG] EventManager._show_drag_context_menu called for {target_type}: {target_name}')
        
        if not hasattr(self, 'drag_source') or not self.drag_source or not self.theme_manager:
            return
        
        alias = self.drag_source['alias']
        
        # Create context menu
        context_menu = tk.Menu(self.tree.master, tearoff=0,
                              bg=self.theme_manager.button_bg,
                              fg=self.theme_manager.button_fg,
                              activebackground=self.theme_manager.tree_select,
                              activeforeground=self.theme_manager.fg_color)
        
        context_menu.add_command(
            label=f"Move '{alias}' to {target_name}",
            command=lambda: self._handle_drag_move(alias, target_item, target_type, target_name)
        )
        
        context_menu.add_command(
            label=f"Copy '{alias}' to {target_name}",
            command=lambda: self._handle_drag_copy(alias, target_item, target_type, target_name)
        )
        
        context_menu.add_separator()
        context_menu.add_command(label="Cancel", command=lambda: context_menu.destroy())
        
        # Show menu at cursor position
        try:
            context_menu.post(event.x_root, event.y_root)
        except tk.TclError:
            pass  # Menu might be destroyed already
    
    def _copy_clip_by_alias(self, alias: str):
        """
        Copy clip content to clipboard by alias.
        
        Args:
            alias: Clip alias to copy
        """
        print(f'[DEBUG] EventManager._copy_clip_by_alias called with alias={alias}')
        
        if self.clip_manager:
            # Delegate to ClipManager
            self.clip_manager.copy_clip_by_alias(alias)
        else:
            print('[WARNING] ClipManager not available for copying clip by alias')
    
    def _handle_move(self, item: str):
        """
        Handle move operation from context menu.
        
        Args:
            item: Tree item ID
        """
        print(f'[DEBUG] EventManager._handle_move called with item={item}')
        # Implementation for move operation
        pass
    
    def _handle_copy(self, item: str):
        """
        Handle copy operation from context menu.
        
        Args:
            item: Tree item ID
        """
        print(f'[DEBUG] EventManager._handle_copy called with item={item}')
        # Implementation for copy operation
        pass
    
    def _handle_drag_move(self, alias: str, target_item: str, target_type: str, target_name: str):
        """
        Handle move operation from drag and drop.
        
        Args:
            alias: Clip alias to move
            target_item: Target tree item ID
            target_type: Type of target item
            target_name: Name of target item
        """
        print(f'[DEBUG] EventManager._handle_drag_move called: {alias} -> {target_name}')
        # Implementation for drag move operation
        pass
    
    def _handle_drag_copy(self, alias: str, target_item: str, target_type: str, target_name: str):
        """
        Handle copy operation from drag and drop.
        
        Args:
            alias: Clip alias to copy
            target_item: Target tree item ID
            target_type: Type of target item
            target_name: Name of target item
        """
        print(f'[DEBUG] EventManager._handle_drag_copy called: {alias} -> {target_name}')
        # Implementation for drag copy operation
        pass
    
    def _handle_business_case_change(self):
        """Handle business case entry field changes."""
        print('[DEBUG] EventManager._handle_business_case_change called')
        # Implementation for business case changes
        pass
    
    def _handle_component_change(self):
        """Handle component entry field changes."""
        print('[DEBUG] EventManager._handle_component_change called')
        # Implementation for component changes
        pass

