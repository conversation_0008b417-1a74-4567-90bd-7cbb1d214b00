import tkinter as tk

class UIStateManager:
    """Manager class for UI state operations extracted from UIManager"""
    
    def __init__(self, ui_manager):
        print('[DEBUG] UIStateManager.__init__ called')
        self.ui_manager = ui_manager
    
    def toggle_theme(self):
        """Toggle between light and dark mode using ThemeManager and UILayoutManager"""
        print('[DEBUG] UIStateManager.toggle_theme called')
        
        try:
            # Toggle theme using theme manager
            self.ui_manager.dark_mode = self.ui_manager.theme_manager.toggle_theme()
            self._update_theme_properties()
            
            # Apply theme to layout using UILayoutManager
            theme_colors = self.ui_manager.theme_manager.get_theme_colors()
            self.ui_manager.ui_layout_manager.apply_layout_theme(theme_colors)
            
            # Update DragDropManager theme if available
            if self.ui_manager.drag_drop_manager:
                self.ui_manager.drag_drop_manager.update_theme()
            
            # Update NotificationManager theme
            if self.ui_manager.notification_manager:
                self.ui_manager.notification_manager.update_theme()
            
            # Update tab colors
            self.update_tab_colors()
            
            print('[DEBUG] Theme toggled successfully')
            
        except Exception as e:
            print(f'[ERROR] Failed to toggle theme: {e}')
    
    def update_tab_colors(self):
        """Update colors for all tab content using UILayoutManager"""
        print('[DEBUG] UIStateManager.update_tab_colors called')
        
        try:
            tabs = {
                'clips_tab': self.ui_manager.clips_tab,
                'more_tab': self.ui_manager.more_tab,
                'about_tab': self.ui_manager.about_tab
            }
            self.ui_manager.ui_layout_manager.update_tab_colors(tabs)
            print('[DEBUG] Tab colors updated successfully')
            
        except Exception as e:
            print(f'[ERROR] Failed to update tab colors: {e}')
    
    def update_undo_button_state(self):
        """Update the undo button enabled/disabled state"""
        print('[DEBUG] UIStateManager.update_undo_button_state called')
        
        try:
            if hasattr(self.ui_manager, 'undo_button') and self.ui_manager.undo_button:
                if self.ui_manager.undo_manager.can_undo():
                    self.ui_manager.undo_button.config(state=tk.NORMAL)
                    last_action = self.ui_manager.undo_manager.get_last_action_description()
                    if last_action:
                        self.ui_manager.undo_button.config(text=f"↶ Undo: {last_action[:20]}...")
                    else:
                        self.ui_manager.undo_button.config(text="↶ Undo (Ctrl+Z)")
                else:
                    self.ui_manager.undo_button.config(state=tk.DISABLED)
                    self.ui_manager.undo_button.config(text="↶ Undo (Ctrl+Z)")
                    
            print('[DEBUG] Undo button state updated successfully')
            
        except Exception as e:
            print(f'[ERROR] Failed to update undo button state: {e}')
    
    def refresh_after_undo(self):
        """Refresh UI components after an undo operation"""
        print('[DEBUG] UIStateManager.refresh_after_undo called')
        
        try:
            # Refresh clips display
            if hasattr(self.ui_manager, 'clip_manager') and self.ui_manager.clip_manager:
                self.ui_manager.clip_manager.load_clips()
            
            # Refresh tree display
            if hasattr(self.ui_manager, 'tree_manager') and self.ui_manager.tree_manager:
                self.ui_manager.tree_manager.refresh_tree()
                
            print('[DEBUG] UI refreshed after undo successfully')
            
        except Exception as e:
            print(f'[ERROR] Failed to refresh UI after undo: {e}')
    
    def refresh_all_ui_components(self):
        """Refresh all UI components - consolidated method for all UI refresh operations"""
        print('[DEBUG] UIStateManager.refresh_all_ui_components called')

        try:
            # Refresh clips display
            if hasattr(self.ui_manager, 'clip_manager') and self.ui_manager.clip_manager:
                self.ui_manager.clip_manager.load_clips()

            # Refresh tree display
            if hasattr(self.ui_manager, 'tree_manager') and self.ui_manager.tree_manager:
                self.ui_manager.tree_manager.refresh_tree()

            # Refresh all tabs if tab manager is available
            if hasattr(self.ui_manager, 'tab_manager') and self.ui_manager.tab_manager:
                self.ui_manager.tab_manager.refresh_all_tabs()

            # Update undo button state
            self.update_undo_button_state()

            # Clear any cached data
            if hasattr(self.ui_manager, 'database_manager') and self.ui_manager.database_manager:
                self.ui_manager.database_manager.clear_cache()

            print('[DEBUG] All UI components refreshed successfully')

        except Exception as e:
            print(f'[ERROR] Failed to refresh all UI components: {e}')

    def refresh_after_database_change(self):
        """Refresh UI components after database changes (create, update, delete operations)"""
        print('[DEBUG] UIStateManager.refresh_after_database_change called')

        try:
            # This is the same as refresh_all_ui_components but with clearer intent
            self.refresh_all_ui_components()

        except Exception as e:
            print(f'[ERROR] Failed to refresh UI after database change: {e}')
    
    def handle_undo_action(self):
        """Handle undo action with proper state management"""
        print('[DEBUG] UIStateManager.handle_undo_action called')
        
        try:
            if self.ui_manager.undo_manager.undo_last_action():
                # Update undo button state
                self.update_undo_button_state()
                
                # Show feedback to user
                last_action = self.ui_manager.undo_manager.get_last_action_description()
                if last_action:
                    print(f'[DEBUG] Action undone successfully. Next undoable action: {last_action}')
                else:
                    print('[DEBUG] Action undone successfully. No more actions to undo.')
                
                # Refresh UI components that might have changed
                self.refresh_after_undo()
                
                return True
            else:
                print('[DEBUG] No actions to undo')
                return False
                
        except Exception as e:
            print(f'[ERROR] Failed to handle undo action: {e}')
            return False
    
    def _update_theme_properties(self):
        """Update theme properties from theme manager for backward compatibility"""
        print('[DEBUG] UIStateManager._update_theme_properties called')
        
        try:
            colors = self.ui_manager.theme_manager.get_theme_colors()
            self.ui_manager.dark_mode = colors['dark_mode']
            self.ui_manager.bg_color = colors['bg_color']
            self.ui_manager.fg_color = colors['fg_color']
            self.ui_manager.entry_bg = colors['entry_bg']
            self.ui_manager.entry_fg = colors['entry_fg']
            self.ui_manager.button_bg = colors['button_bg']
            self.ui_manager.button_fg = colors['button_fg']
            self.ui_manager.tree_bg = colors['tree_bg']
            self.ui_manager.tree_fg = colors['tree_fg']
            self.ui_manager.tree_select = colors['tree_select']
            
            print('[DEBUG] Theme properties updated successfully')
            
        except Exception as e:
            print(f'[ERROR] Failed to update theme properties: {e}')
    
    def update_button_themes(self, buttons):
        """Update theme for a collection of buttons"""
        print('[DEBUG] UIStateManager.update_button_themes called')
        
        try:
            theme_colors = self.ui_manager.theme_manager.get_theme_colors()
            
            for button_name, button in buttons.items():
                if button and hasattr(button, 'config'):
                    # Apply theme-appropriate colors while preserving special button colors
                    if button_name in ['export_button', 'backup_button', 'clear_all_button']:
                        # Keep special colors for these buttons
                        continue
                    else:
                        button.config(
                            bg=theme_colors['button_bg'],
                            fg=theme_colors['button_fg'],
                            activebackground=theme_colors['button_bg'],
                            activeforeground=theme_colors['button_fg']
                        )
                        
            print('[DEBUG] Button themes updated successfully')
            
        except Exception as e:
            print(f'[ERROR] Failed to update button themes: {e}')
