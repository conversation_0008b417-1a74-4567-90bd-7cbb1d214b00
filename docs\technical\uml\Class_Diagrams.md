# 📐 ClipsMore UML Class Diagrams

🏠 [ClipsMore](../../../README.md) > 🏗️ [Technical Documentation](../README.md) > 📐 UML Class Diagrams

## 🔍 Overview
This document provides comprehensive UML class diagrams for the ClipsMore application, showing the object-oriented design, relationships, and key interfaces across all system components. 🏗️

## 🏛️ Main Application Classes

### 🎛️ UI Manager and Core Components

```mermaid
classDiagram
    class UIManager {
        -root: tk.Tk
        -dark_mode: bool
        -bg_color: str
        -fg_color: str
        -clips_tab: ttk.Frame
        -more_tab: ttk.Frame
        -about_tab: ttk.Frame
        -tree: ttk.Treeview
        -clips_canvas: tk.Canvas
        -clips_scrollable_frame: tk.Frame
        +__init__(root: tk.Tk)
        +set_theme(): void
        +toggle_theme(): void
        +init_clips_tab(): void
        +init_more_tab(): void
        +init_about_tab(): void
        +load_enhanced_clips(): void
        +refresh_tree(): void
        +_create_clip_widget(clip: dict, index: int): void
        +_copy_clip_to_clipboard(clip_id: int): void
        +_assign_clip(clip_id: int, alias: str, assignment: str): void
        +_delete_clip(clip_id: int): void
        +clear_all_clips(): void
    }
    
    class ThemeManager {
        <<interface>>
        +set_light_theme(): void
        +set_dark_theme(): void
        +update_widget_colors(widget: tk.Widget): void
    }

    class DocumentationManager {
        -doc_files: List[Tuple[str, str]]
        -notebook: ttk.Notebook
        -text_widgets: Dict[str, tk.Text]
        +__init__(parent: tk.Widget)
        +create_documentation_interface(): ttk.Notebook
        +load_documentation_tabs(): void
        +create_doc_tab(title: str, file_path: str): ttk.Frame
        +render_markdown(content: str, text_widget: tk.Text): void
        +apply_text_formatting(text_widget: tk.Text): void
    }
    
    class ClipWidget {
        -clip_id: int
        -clip_content: str
        -alias_var: tk.StringVar
        -assignment_var: tk.StringVar
        -frame: tk.Frame
        +create_header(): void
        +create_content_preview(): void
        +create_management_controls(): void
        +update_alias(alias: str): void
        +get_assignment(): str
    }
    
    UIManager ..|> ThemeManager : implements
    UIManager --> ClipWidget : creates
```

## 🗄️ Database Operations Classes

### 🔧 Core Database Operations

```mermaid
classDiagram
    class ConnectionPoolManager {
        -DEFAULT_DB_PATH: str
        -pool: List[Connection]
        -pool_size: int
        -current_connections: int
        +__init__(db_path: str, pool_size: int)
        +get_connection(): Connection
        +release_connection(conn: Connection): void
        +close_all_connections(): void
        +_create_connection(): Connection
        +_initialize_pool(): void
    }
    
    class ClipsTableOperations {
        -db_path: str
        +__init__(db_path: Optional[str])
        +create_clip(content: str, alias: Optional[str]): int
        +read_all_clips(): List[Dict[str, Any]]
        +read_clip_by_id(clip_id: int): Optional[Dict[str, Any]]
        +update_clip_alias(clip_id: int, alias: str): bool
        +delete_clip(clip_id: int): bool
        +truncate_clips_table(): bool
        +get_clip_count(): int
    }
    
    class MoreTableOperations {
        -db_path: str
        +__init__(db_path: Optional[str])
        +create_business_case(name: str): int
        +read_all_business_cases(): List[Dict[str, Any]]
        +update_business_case(bus_id: int, name: str): bool
        +delete_business_case(bus_id: int): bool
        +create_component(bus_id: int, name: str): int
        +read_components_for_business_case(bus_id: int): List[Dict[str, Any]]
        +update_component(comp_id: int, name: str): bool
        +delete_component(comp_id: int): bool
        +get_business_case_id_by_name(name: str): Optional[int]
        +get_component_id_by_name_in_business_case(name: str, bus_id: int): Optional[int]
    }
    
    ClipsTableOperations --> ConnectionPoolManager : uses
    MoreTableOperations --> ConnectionPoolManager : uses
```

### ⚡ Enhanced Operations and Transaction Management

```mermaid
classDiagram
    class ClipsMoreEnhancedOperations {
        -db_path: str
        +__init__(db_path: Optional[str])
        +create_assignment(clip_id: int, more_bus_id: int, more_comp_id: Optional[int], alias: Optional[str], tree_position: Optional[int]): int
        +update_assignment(transaction_id: int, alias: Optional[str], more_bus_id: Optional[int], more_comp_id: Optional[int], tree_position: Optional[int]): bool
        +delete_assignment(transaction_id: int): bool
        +move_assignment(transaction_id: int, target_bus_id: int, target_comp_id: Optional[int], new_tree_position: Optional[int]): bool
        +copy_assignment(transaction_id: int, target_bus_id: int, target_comp_id: Optional[int]): int
        +get_assignments_by_business_case(more_bus_id: int): List[Dict[str, Any]]
        +get_assignments_by_component(more_comp_id: int): List[Dict[str, Any]]
        -_generate_unique_alias(cursor: Cursor, clip_id: int): str
        -_generate_copy_alias(cursor: Cursor, original_alias: str): str
        -_is_alias_unique(cursor: Cursor, alias: str, exclude_transaction_id: Optional[int]): bool
        -_get_next_tree_position(cursor: Cursor, more_bus_id: int, more_comp_id: Optional[int]): int
    }
    
    class ClipsMoreError {
        <<exception>>
        +message: str
        +__init__(message: str)
    }
    
    class DatabaseMigrationV2 {
        -db_path: str
        -backup_path: str
        +__init__(db_path: Optional[str])
        +backup_database(): bool
        +check_current_schema(): dict
        +migrate_clipsmore_table(): bool
        +update_clipsmore_view(): bool
        +run_migration(): bool
        -_generate_alias_for_clip(cursor: Cursor, clip_id: int, fallback_num: int): str
    }
    
    ClipsMoreEnhancedOperations --> ClipsMoreError : throws
    DatabaseMigrationV2 --> ConnectionPoolManager : uses
```

## 📊 Data Model Classes

### 🏗️ Entity Classes

```mermaid
classDiagram
    class Clip {
        +clip_id: int
        +content: str
        +alias: Optional[str]
        +timestamp: datetime
        +__init__(clip_id: int, content: str, alias: Optional[str], timestamp: datetime)
        +to_dict(): Dict[str, Any]
        +from_dict(data: Dict[str, Any]): Clip
    }
    
    class BusinessCase {
        +more_bus_id: int
        +name: str
        +components: List[Component]
        +__init__(more_bus_id: int, name: str)
        +add_component(component: Component): void
        +remove_component(comp_id: int): bool
        +get_component_by_id(comp_id: int): Optional[Component]
        +to_dict(): Dict[str, Any]
    }
    
    class Component {
        +more_comp_id: int
        +more_bus_id: int
        +name: str
        +business_case: BusinessCase
        +__init__(more_comp_id: int, more_bus_id: int, name: str)
        +to_dict(): Dict[str, Any]
    }
    
    class ClipAssignment {
        +transaction_id: int
        +clip_id: int
        +alias: str
        +more_bus_id: int
        +more_comp_id: Optional[int]
        +tree_position: int
        +created_date: datetime
        +modified_date: datetime
        +__init__(transaction_id: int, clip_id: int, alias: str, more_bus_id: int, more_comp_id: Optional[int], tree_position: int)
        +update_position(new_position: int): void
        +update_alias(new_alias: str): void
        +to_dict(): Dict[str, Any]
    }
    
    BusinessCase "1" --> "*" Component : contains
    Clip "1" --> "*" ClipAssignment : has assignments
    BusinessCase "1" --> "*" ClipAssignment : contains assignments
    Component "0..1" --> "*" ClipAssignment : contains assignments
```

## 🖥️ UI Component Classes

### 🧩 Widget Hierarchy

```mermaid
classDiagram
    class TabManager {
        <<interface>>
        +create_tab(): ttk.Frame
        +initialize_content(): void
        +refresh_display(): void
        +handle_theme_change(): void
    }
    
    class ClipsTab {
        -parent: ttk.Frame
        -clips_canvas: tk.Canvas
        -scrollable_frame: tk.Frame
        -clip_widgets: List[ClipWidget]
        +__init__(parent: ttk.Frame)
        +load_clips(): void
        +create_clip_widget(clip: dict): ClipWidget
        +refresh_clips(): void
        +clear_all_clips(): void
        +handle_scroll(event: tk.Event): void
    }
    
    class MoreTab {
        -parent: ttk.Frame
        -tree: ttk.Treeview
        -search_var: tk.StringVar
        -bus_entry_var: tk.StringVar
        -comp_entry_var: tk.StringVar
        +__init__(parent: ttk.Frame)
        +initialize_tree(): void
        +refresh_tree(): void
        +add_business_case(): void
        +add_component(): void
        +handle_tree_selection(event: tk.Event): void
        +show_context_menu(event: tk.Event): void
    }
    
    class AboutTab {
        -parent: ttk.Frame
        -readme_frame: tk.Frame
        -readme_widget: tk.Text
        +__init__(parent: ttk.Frame)
        +display_readme_content(): void
        +handle_theme_change(): void
    }
    
    TabManager <|.. ClipsTab : implements
    TabManager <|.. MoreTab : implements
    TabManager <|.. AboutTab : implements
    
    UIManager --> ClipsTab : manages
    UIManager --> MoreTab : manages
    UIManager --> AboutTab : manages
```

### ⚡ Event Handling Classes

```mermaid
classDiagram
    class EventHandler {
        <<interface>>
        +handle_event(event: tk.Event): void
    }
    
    class DragDropHandler {
        -tree: ttk.Treeview
        -drag_item: Optional[str]
        -drop_target: Optional[str]
        +__init__(tree: ttk.Treeview)
        +on_drag_start(event: tk.Event): void
        +on_drag_motion(event: tk.Event): void
        +on_drop(event: tk.Event): void
        +show_context_menu(event: tk.Event): void
        +handle_move_operation(): void
        +handle_copy_operation(): void
        +handle_cancel_operation(): void
    }
    
    class ClipboardMonitor {
        -callback: Callable
        -last_content: str
        -monitoring: bool
        +__init__(callback: Callable)
        +start_monitoring(): void
        +stop_monitoring(): void
        +check_clipboard(): void
        -_on_clipboard_change(content: str): void
    }
    
    class AliasGenerator {
        +generate_from_content(content: str): str
        +ensure_uniqueness(alias: str, existing_aliases: List[str]): str
        +validate_alias(alias: str): bool
        -_extract_meaningful_words(content: str): List[str]
        -_create_fallback_alias(index: int): str
    }
    
    EventHandler <|.. DragDropHandler : implements
    UIManager --> DragDropHandler : uses
    UIManager --> ClipboardMonitor : uses
    UIManager --> AliasGenerator : uses
```

## 🔧 Utility and Helper Classes

### ⚙️ Configuration and Settings

```mermaid
classDiagram
    class ConfigManager {
        -config_path: str
        -settings: Dict[str, Any]
        +__init__(config_path: str)
        +load_settings(): Dict[str, Any]
        +save_settings(settings: Dict[str, Any]): bool
        +get_setting(key: str, default: Any): Any
        +set_setting(key: str, value: Any): void
        +reset_to_defaults(): void
    }
    
    class ThemeConfig {
        +light_theme: Dict[str, str]
        +dark_theme: Dict[str, str]
        +current_theme: str
        +get_color(element: str): str
        +set_theme(theme_name: str): void
        +create_custom_theme(colors: Dict[str, str]): void
    }
    
    class ValidationUtils {
        <<utility>>
        +validate_alias(alias: str): bool
        +validate_business_case_name(name: str): bool
        +validate_component_name(name: str): bool
        +sanitize_input(input_str: str): str
        +check_length_limits(text: str, max_length: int): bool
    }
    
    class FileUtils {
        <<utility>>
        +ensure_directory_exists(path: str): bool
        +backup_file(source: str, destination: str): bool
        +read_text_file(path: str): str
        +write_text_file(path: str, content: str): bool
        +get_file_size(path: str): int
    }
    
    UIManager --> ConfigManager : uses
    UIManager --> ThemeConfig : uses
    ClipsMoreEnhancedOperations --> ValidationUtils : uses
    DatabaseMigrationV2 --> FileUtils : uses
```

## 🎨 Design Patterns Implementation

### 👁️ Observer Pattern for UI Updates

```mermaid
classDiagram
    class Observable {
        <<interface>>
        +add_observer(observer: Observer): void
        +remove_observer(observer: Observer): void
        +notify_observers(event: str, data: Any): void
    }
    
    class Observer {
        <<interface>>
        +update(event: str, data: Any): void
    }
    
    class ClipDataModel {
        -observers: List[Observer]
        -clips: List[Clip]
        +add_clip(clip: Clip): void
        +remove_clip(clip_id: int): void
        +update_clip(clip: Clip): void
        +get_all_clips(): List[Clip]
    }
    
    class UIObserver {
        -ui_manager: UIManager
        +__init__(ui_manager: UIManager)
        +update(event: str, data: Any): void
        -_refresh_clips_display(): void
        -_refresh_tree_display(): void
    }
    
    Observable <|.. ClipDataModel : implements
    Observer <|.. UIObserver : implements
    ClipDataModel --> Observer : notifies
    UIManager --> UIObserver : creates
```

### 🏭 Factory Pattern for Widget Creation

```mermaid
classDiagram
    class WidgetFactory {
        <<abstract>>
        +create_widget(widget_type: str, parent: tk.Widget, **kwargs): tk.Widget
    }
    
    class ClipWidgetFactory {
        +create_clip_widget(parent: tk.Widget, clip: dict): ClipWidget
        +create_copy_button(parent: tk.Widget, clip_id: int): tk.Button
        +create_alias_entry(parent: tk.Widget, alias: str): tk.Entry
        +create_assignment_dropdown(parent: tk.Widget): ttk.Combobox
        +create_assign_button(parent: tk.Widget, callback: Callable): tk.Button
    }
    
    class TreeWidgetFactory {
        +create_tree_view(parent: tk.Widget): ttk.Treeview
        +create_tree_item(tree: ttk.Treeview, parent: str, text: str, values: tuple): str
        +create_context_menu(parent: tk.Widget): tk.Menu
    }
    
    WidgetFactory <|-- ClipWidgetFactory
    WidgetFactory <|-- TreeWidgetFactory
    UIManager --> ClipWidgetFactory : uses
    UIManager --> TreeWidgetFactory : uses
```

## 📋 Class Relationships Summary

### 🔗 Inheritance Relationships
- 🏭 `ClipWidgetFactory` extends `WidgetFactory`
- 🏭 `TreeWidgetFactory` extends `WidgetFactory`
- 👁️ `ClipDataModel` implements `Observable`
- 👁️ `UIObserver` implements `Observer`
- ⚡ `DragDropHandler` implements `EventHandler`

### 🧩 Composition Relationships
- 🎛️ `UIManager` contains `ClipsTab`, `MoreTab`, `AboutTab`
- 📚 `AboutTab` contains `DocumentationManager` with 11 documentation files
- 🏢 `BusinessCase` contains multiple `Component` objects
- 📊 `ClipDataModel` contains multiple `Clip` objects

### 🔗 Dependency Relationships
- 🗄️ All database operation classes depend on `ConnectionPoolManager`
- 🖥️ UI classes depend on database operation classes
- 🎛️ `UIManager` depends on various utility and helper classes
- 🏭 Widget factories are used by UI components for object creation

### 🤝 Association Relationships
- 📋 `Clip` is associated with multiple `ClipAssignment` objects
- 🏢 `BusinessCase` is associated with multiple `ClipAssignment` objects
- 🧩 `Component` is optionally associated with multiple `ClipAssignment` objects

## See Also

- **🏛️ [System Architecture](../architecture/System_Architecture.md)** - Overall system design and component architecture
- **🗄️ [Database Schema](../database/ER_Diagram.md)** - Database structure and relationships
- **🔄 [UML Sequence Diagrams](Sequence_Diagrams.md)** - Interaction flows for key use cases
- **🌐 [C4 Model](../c4/C4_Model.md)** - Hierarchical system visualization
- **🔗 [Dependencies](../dependencies/Dependency_Analysis.md)** - Module and class dependency analysis
- **🏗️ [Technical Documentation Index](../README.md)** - All technical documentation
- **🏠 [Back to ClipsMore](../../../README.md)** - Main project overview

---

🏠 **[Back to ClipsMore](../../../README.md)** | 🏗️ **[Technical Documentation](../README.md)**
