import os, sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import sqlite3
from typing import Optional, Dict, Any, List, Tuple
from .db_connection import ConnectionPoolManager

class ClipError(Exception):
    """Base exception for clip-related operations."""

class ClipNotFoundError(ClipError):
    """Raised when a requested clip relationship is not found."""

class ClipsMoreNotFoundError(ClipError):
    """Raised when a requested ClipsMore relationship is not found."""

class ClipsMoreValidationError(ClipError):
    """Raised when invalid data is provided for clipsmore relationships."""

class ClipsMoreDestructiveOperationError(ClipError):
    """Raised when attempting destructive operations without confirmation."""

class ClipsMoreTableOperations:
    """Manage relationships between clips and business cases/components.

    Provides CRUD operations for associating clips with business cases and components
    in the ClipsMore application database.

    Attributes:
        db_path: Path to SQLite database file
    """

    def __init__(self, db_path: Optional[str] = None) -> None:
        """Initialize with optional custom database path.

        Args:
            db_path: Alternate path to SQLite database file. Uses default if None.
        """
        print('[DEBUG] ClipsMoreTableOperations.__init__ called')
        self.db_path = db_path or ConnectionPoolManager.DEFAULT_DB_PATH

    def create_entry(self, data: Dict) -> int:
        """Create a new relationship entry.

        Args:
            data: Dictionary containing clip_id and more_bus_id (and optionally more_comp_id)

        Returns:
            int: ID of the created relationship
        """
        print(f'[DEBUG] create_entry called with data: {data}')
        return self.create_clipsmore(data)

    def read_entry(self, entry_id: int) -> Dict[str, Any]:
        """Read a specific relationship by its ID.

        Args:
            entry_id: The ID of the relationship to read

        Returns:
            dict: Relationship data
        """
        print(f'[DEBUG] read_entry called with entry_id: {entry_id}')
        # Validate ID before proceeding
        if not isinstance(entry_id, int) or entry_id <= 0:
            raise ClipsMoreValidationError("entry_id must be a positive integer > 0")

        try:
            with ConnectionPoolManager().get_connection() as conn:
                cursor = conn.cursor()
                query = """
                    SELECT clip_id, more_bus_id, more_comp_id
                    FROM clipsmore_tbl
                    WHERE rowid = ?
                """
                cursor.execute(query, (entry_id,))
                result = cursor.fetchone()

                if not result:
                    raise ClipsMoreNotFoundError(f"No relationship found for ID: {entry_id}")

                return {
                    'clip_id': result[0],
                    'more_bus_id': result[1],
                    'more_comp_id': result[2]
                }

        except sqlite3.Error as e:
            raise ClipError(f"Database error: {str(e)}") from e

    def update_entry(self, entry_id: int, **updates) -> int:
        """Update an existing relationship.

        Args:
            entry_id: ID of the relationship to update
            updates: Keyword arguments with fields to update

        Returns:
            int: Number of rows affected
        """
        print(f'[DEBUG] update_entry called with entry_id: {entry_id}, updates: {updates}')
        return self.update_clipsmore(entry_id, **updates)

    def delete_entry(self, entry_id: int) -> int:
        """Delete a specific relationship.

        Args:
            entry_id: ID of the relationship to delete

        Returns:
            int: Number of rows deleted
        """
        print(f'[DEBUG] delete_entry called with entry_id: {entry_id}')
        return self.delete_clipsmore(entry_id, "CONFIRM")

    def find_duplicate_entry(self, clip_id: int, more_bus_id: int) -> Optional[int]:
        """Check if a duplicate entry exists.

        Args:
            clip_id: Clip ID to check
            more_bus_id: Business case ID to check

        Returns:
            int | None: Entry ID if found, otherwise None
        """
        print(f'[DEBUG] find_duplicate_entry called with clip_id: {clip_id}, more_bus_id: {more_bus_id}')
        try:
            with ConnectionPoolManager().get_connection() as conn:
                cursor = conn.cursor()
                query = """
                    SELECT rowid
                    FROM clipsmore_tbl
                    WHERE clip_id = ? AND more_bus_id = ?
                """
                cursor.execute(query, (clip_id, more_bus_id))
                result = cursor.fetchone()

                return result[0] if result else None

        except sqlite3.Error as e:
            raise ClipError(f"Database error: {str(e)}") from e

    def read_all_entries(self) -> List[Dict[str, Any]]:
        """Read all relationship entries.

        Returns:
            list: List of all relationships
        """
        print('[DEBUG] read_all_entries called')
        try:
            with ConnectionPoolManager().get_connection() as conn:
                cursor = conn.cursor()
                query = """
                    SELECT rowid, clip_id, more_bus_id, more_comp_id
                    FROM clipsmore_tbl
                """
                cursor.execute(query)
                results = cursor.fetchall()

                return [
                    {
                        'entry_id': row[0],
                        'clip_id': row[1],
                        'more_bus_id': row[2],
                        'more_comp_id': row[3]
                    }
                    for row in results
                ]

        except sqlite3.Error as e:
            raise ClipError(f"Database error: {str(e)}") from e

    def _validate_ids(self,
                     entry_id: Optional[int] = None,
                     clip_id: Optional[int] = None,
                     more_bus_id: Optional[int] = None,
                     more_comp_id: Optional[int] = None) -> None:
        """Validate database ID parameters.

        Args:
            entry_id: Optional relationship identifier (>0)
            clip_id: Optional clip identifier (>0)
            more_bus_id: Optional business case identifier (>0)
            more_comp_id: Optional component identifier (>0)

        Raises:
            ClipsMoreValidationError: If any ID fails validation
        """
        print(f'[DEBUG] _validate_ids called with entry_id: {entry_id}, clip_id: {clip_id}, more_bus_id: {more_bus_id}, more_comp_id: {more_comp_id}')
        if entry_id is not None:
            if not isinstance(entry_id, int) or entry_id <= 0:
                raise ClipsMoreValidationError("entry_id must be a positive integer > 0")

        if clip_id is not None:
            if not isinstance(clip_id, int) or clip_id <= 0:
                raise ClipsMoreValidationError("clip_id must be a positive integer > 0")

        if more_bus_id is not None:
            if not isinstance(more_bus_id, int) or more_bus_id <= 0:
                raise ClipsMoreValidationError("more_bus_id must be a positive integer > 0")

        if more_comp_id is not None:
            if not isinstance(more_comp_id, int) or more_comp_id <= 0:
                raise ClipsMoreValidationError("more_comp_id must be a positive integer > 0")

    def create_clipsmore(self, clip_data: Dict) -> int:
        """Create new clip-business case/component relationship from data dict.

        Args:
            clip_data: Dictionary containing:
                - clip_id: Positive integer >0 identifying clip
                - more_bus_id: Positive integer >0 identifying business case
                - more_comp_id: Optional positive integer >0 identifying component

        Returns:
            int: ID of newly created relationship

        Raises:
            ClipsMoreValidationError: Invalid ID or foreign key constraint failure
            ClipError: General database error

        Example:
            >>> ops = ClipsMoreTableOperations()
            >>> ops.create_clipsmore({'clip_id': 123, 'more_bus_id': 456})
            1

        """
        print(f'[DEBUG] create_clipsmore called with clip_data: {clip_data}')
        # Make sure required fields are present
        if 'clip_id' not in clip_data or 'more_bus_id' not in clip_data:
            raise ClipsMoreValidationError("clip_data must contain 'clip_id' and 'more_bus_id'")

        self._validate_ids(
            clip_id=clip_data.get('clip_id'),
            more_bus_id=clip_data.get('more_bus_id'),
            more_comp_id=clip_data.get('more_comp_id')
        )

        try:
            # First check if this combination already exists
            with ConnectionPoolManager().get_connection() as conn:
                cursor = conn.cursor()
                if clip_data.get('more_comp_id') is None:
                    cursor.execute("""
                        SELECT rowid
                        FROM clipsmore_tbl
                        WHERE clip_id = ? AND more_bus_id = ? AND more_comp_id IS NULL
                    """, (
                        clip_data['clip_id'],
                        clip_data['more_bus_id']
                    ))
                else:
                    cursor.execute("""
                        SELECT rowid
                        FROM clipsmore_tbl
                        WHERE clip_id = ? AND more_bus_id = ? AND more_comp_id = ?
                    """, (
                        clip_data['clip_id'],
                        clip_data['more_bus_id'],
                        clip_data.get('more_comp_id')
                    ))
                result = cursor.fetchone()

                if result:
                    # Raise error instead of returning ID if duplicate found
                    raise ClipsMoreValidationError("Unique constraint failed: duplicate assignment")

            # If not a duplicate, proceed with insertion
            with ConnectionPoolManager().get_connection() as conn:
                query = """
                    INSERT INTO clipsmore_tbl (clip_id, more_bus_id, more_comp_id)
                    VALUES (?, ?, ?)
                """
                cursor = conn.cursor()
                cursor.execute(query, (
                    clip_data['clip_id'],
                    clip_data['more_bus_id'],
                    clip_data.get('more_comp_id')
                ))
                conn.commit()
                return cursor.lastrowid

        except sqlite3.IntegrityError as e:
            # Check if it's a foreign key constraint failure
            if "FOREIGN KEY" in str(e):
                raise ClipsMoreValidationError(f"Invalid foreign key: {str(e)}") from e
            elif "UNIQUE constraint failed" in str(e):
                # Handle duplicate entry case - raise error
                raise ClipsMoreValidationError(f"Unique constraint failed: {str(e)}") from e
            else:
                raise ClipError(f"Update failed: {str(e)}") from e
        except sqlite3.Error as e:
            raise ClipError(f"Update failed: {str(e)}") from e

    def read_clipsmore(self, entry_id_or_clip_id: int) -> Dict[str, Any]:
        """Retrieve relationship details for a clip.

        Args:
            entry_id_or_clip_id: Positive integer >0 identifying either the relationship or clip

        Returns:
            dict: Relationship data with keys:
                - clipmore_id: Relationship ID
                - clip_id: Associated clip ID
                - more_bus_id: Business case ID
                - more_comp_id: Component ID (optional)

        Raises:
            ClipsMoreValidationError: Invalid entry_id_or_clip_id
            ClipNotFoundError: No relationship exists
            ClipError: Database error

        Example:
            >>> ops = ClipsMoreTableOperations()
            >>> ops.read_clipsmore(123)
            {'clipmore_id': 1, 'clip_id': 123, 'more_bus_id': 456, 'more_comp_id': 789}
        """
        print(f'[DEBUG] read_clipsmore called with entry_id_or_clip_id: {entry_id_or_clip_id}')
        self._validate_ids(entry_id=entry_id_or_clip_id)

        try:
            with ConnectionPoolManager().get_connection() as conn:
                cursor = conn.cursor()

                # First check if it's a rowid (primary key)
                cursor.execute("""
                    SELECT c.clip_id, cm.more_bus_id, cm.more_comp_id,
                           c.timestamp, mb.bus_case, mc.bus_component
                    FROM clipsmore_tbl AS cm
                    JOIN clips_tbl AS c ON cm.clip_id = c.clip_id
                    JOIN more_bus_tbl AS mb ON cm.more_bus_id = mb.more_bus_id
                    LEFT JOIN more_comp_tbl AS mc ON cm.more_comp_id = mc.more_comp_id
                    WHERE cm.rowid = ?
                """, (entry_id_or_clip_id,))

                result = cursor.fetchone()
                if not result:
                    # If not found by rowid, try to find by clip_id
                    cursor.execute("""
                        SELECT cm.rowid, c.clip_id, cm.more_bus_id, cm.more_comp_id,
                               c.timestamp, mb.bus_case, mc.bus_component
                        FROM clipsmore_tbl AS cm
                        JOIN clips_tbl AS c ON cm.clip_id = c.clip_id
                        JOIN more_bus_tbl AS mb ON cm.more_bus_id = mb.more_bus_id
                        LEFT JOIN more_comp_tbl AS mc ON cm.more_comp_id = mc.more_comp_id
                        WHERE c.clip_id = ?
                    """, (entry_id_or_clip_id,))

                    result = cursor.fetchone()
                    if not result:
                        raise ClipNotFoundError(f"No relationship found for ID: {entry_id_or_clip_id}")

                # Process the result - handle different result formats
                if len(result) >= 7:
                    return {
                        'clipmore_id': entry_id_or_clip_id if len(result) > 6 else result[0],
                        'clip_id': result[1],
                        'more_bus_id': result[2],
                        'more_comp_id': result[3],
                        'timestamp': result[4],
                        'bus_case': result[5],
                        'bus_component': result[6]
                    }
                elif len(result) >= 6:
                    return {
                        'clipmore_id': entry_id_or_clip_id if len(result) > 6 else result[0],
                        'clip_id': result[1],
                        'more_bus_id': result[2],
                        'more_comp_id': result[3],
                        'timestamp': result[4],
                        'bus_case': result[5]
                    }
                elif len(result) >= 4:
                    return {
                        'clipmore_id': entry_id_or_clip_id if len(result) > 6 else result[0],
                        'clip_id': result[1],
                        'more_bus_id': result[2],
                        'more_comp_id': result[3]
                    }
                else:
                    raise ClipError("Invalid result format from database query")
        except sqlite3.Error as e:
            raise ClipError(f"Database error: {str(e)}") from e

    def update_clipsmore(self, entry_id_or_clip_id: int, **updates) -> int:
        """Update existing clip relationships.

            Args:
                entry_id_or_clip_id: Positive integer >0 identifying either the relationship or clip
                more_bus_id: New business case ID (optional, >0 if provided)
                more_comp_id: New component ID (optional, >0 if provided)

            Returns:
                int: Number of affected rows (typically 1)

            Raises:
                ClipsMoreValidationError: Invalid ID or no update parameters
                ClipNotFoundError: No existing relationship found
                ClipError: Database error

            Example:
                >>> ops = ClipsMoreTableOperations()
                >>> ops.update_clipsmore(123, more_bus_id=789)
                1

            """
        print(f'[DEBUG] update_clipsmore called with entry_id_or_clip_id: {entry_id_or_clip_id}, updates: {updates}')
        more_bus_id = updates.get('more_bus_id')
        more_comp_id = updates.get('more_comp_id')

        self._validate_ids(entry_id=entry_id_or_clip_id)

        if more_bus_id is None and more_comp_id is None:
            raise ClipsMoreValidationError("Must provide either 'more_bus_id' or 'more_comp_id' to update")
        try:
            # First, check if we're using a clip_id instead of an entry_id
            existing = None
            try:
                with ConnectionPoolManager().get_connection() as conn:
                    cursor = conn.cursor()
                    cursor.execute("""
                        SELECT rowid
                        FROM clipsmore_tbl
                        WHERE clip_id = ?
                    """, (entry_id_or_clip_id,))
                    result = cursor.fetchone()

                    if result:
                        entry_id = result[0]
                    else:
                        # If not found by clip_id, try to find by rowid
                        cursor.execute("""
                            SELECT rowid
                            FROM clipsmore_tbl
                            WHERE rowid = ?
                        """, (entry_id_or_clip_id,))
                        result = cursor.fetchone()
                        entry_id = entry_id_or_clip_id if result else None

                    if entry_id is None:
                        raise ClipsMoreNotFoundError(f"No relationships for ID {entry_id_or_clip_id}")
            except Exception as e:
                print(f"Error finding relationship: {e}")
                raise ClipsMoreNotFoundError(f"No relationships for ID {entry_id_or_clip_id}")

            updates_list = []
            params = []

            if more_bus_id is not None:
                updates_list.append("more_bus_id = ?")
                params.append(more_bus_id)
            if more_comp_id is not None:
                updates_list.append("more_comp_id = ?")
                params.append(more_comp_id)

            query = f"UPDATE clipsmore_tbl SET {', '.join(updates_list)} WHERE rowid = ?"
            params.append(entry_id)

            with ConnectionPoolManager().get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, tuple(params))

                if cursor.rowcount == 0:
                    raise ClipsMoreNotFoundError(f"No relationships for ID {entry_id}")

                conn.commit()
                return cursor.rowcount

        except sqlite3.IntegrityError as e:
            raise ClipsMoreValidationError(f"Invalid foreign key: {str(e)}") from e
        except sqlite3.Error as e:
            raise ClipError(f"Create failed: {str(e)}") from e

    def delete_clipsmore(self, entry_id: int, confirmation: str) -> int:
        """Delete a specific relationship.

        Args:
            entry_id: Positive integer >0 identifying the relationship
            confirm: Must be 'CONFIRM' to execute deletion

        Returns:
            int: Number of deleted relationships

        Raises:
            ClipsMoreValidationError: Invalid entry_id or missing confirmation
            ClipNotFoundError: No relationships exist for ID
            ClipError: Database error

        Example:
            >>> ops = ClipsMoreTableOperations()
            >>> ops.delete_clipsmore(123, 'CONFIRM')
            1
        """
        print(f'[DEBUG] delete_clipsmore called with entry_id: {entry_id}, confirmation: {confirmation}')
        self._validate_ids(entry_id=entry_id)

        try:
            with ConnectionPoolManager().get_connection() as conn:
                query = "DELETE FROM clipsmore_tbl WHERE rowid = ?"
                cursor = conn.cursor()
                cursor.execute(query, (entry_id,))

                if cursor.rowcount == 0:
                    raise ClipsMoreNotFoundError(f"No relationships for ID {entry_id}")

                conn.commit()
                return cursor.rowcount

        except sqlite3.Error as e:
            raise ClipError(f"Delete failed: {str(e)}") from e

    def delete_all_clips(self, confirmation: str) -> int:
        """Delete all clipsmore relationships with confirmation.

        Args:
            confirmation: Must be "CONFIRM" to proceed

        Returns:
            int: Number of relationships deleted

        Raises:
            ClipsMoreDestructiveOperationError: If confirmation is invalid
        """
        print(f'[DEBUG] delete_all_clips called with confirmation: {confirmation}')
        if confirmation != "CONFIRM":
            raise ClipsMoreDestructiveOperationError("Confirmation required for bulk delete")

        with ConnectionPoolManager().get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("DELETE FROM clipsmore_tbl")
            conn.commit()
            return cursor.rowcount

    def truncate_all_tables(self, confirmation: str) -> int:
        """Truncate both clipsmore_tbl and clips_tbl with confirmation.

        This method first truncates the clipsmore_tbl to remove all relationships,
        then truncates the clips_tbl to remove all clip data. This ensures proper
        referential integrity by removing foreign key constraints first.

        Args:
            confirmation: Must be "CONFIRM" to proceed

        Returns:
            int: Total number of rows deleted from both tables

        Raises:
            ClipsMoreDestructiveOperationError: If confirmation is invalid
        """
        print(f'[DEBUG] truncate_all_tables called with confirmation: {confirmation}')
        if confirmation != "CONFIRM":
            raise ClipsMoreDestructiveOperationError("Confirmation required for bulk truncation")

        total_deleted = 0

        # First truncate clipsmore_tbl (relationships)
        with ConnectionPoolManager().get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("DELETE FROM clipsmore_tbl")
            conn.commit()
            total_deleted += cursor.rowcount

        # Then truncate clips_tbl (clip data)
        from .op_clips_tbl import ClipsTableOperations
        total_deleted += ClipsTableOperations().delete_all_clips(confirmation)

        return total_deleted

class ClipsForComponentOperations:
    """Query clips associated with business cases and components.

    Provides read-only access to clip relationships with additional context.

    Attributes:
        db_path: Path to SQLite database file
    """

    def __init__(self, db_path: Optional[str] = None) -> None:
        """Initialize with optional database path.

        Args:
            db_path: Alternate path to SQLite database file. Uses default if None.
        """
        print('[DEBUG] ClipsForComponentOperations.__init__ called')
        self.db_path = db_path or ConnectionPoolManager.DEFAULT_DB_PATH

    def get_clips_for_component(self, more_comp_id: int) -> List[Dict[str, Any]]:
        """Retrieve clips associated with a component.

        Args:
            more_comp_id: Positive integer >0 identifying component

        Returns:
            list: Dictionaries with keys:
                - clip_id: Unique clip identifier
                - clip: Clip content text
                - alias: Clip display name/alias

        Raises:
            ClipError: Database error during operation

        Example:
            >>> ops = ClipsForComponentOperations()
            >>> ops.get_clips_for_component(456)
            [{'clip_id': 123, 'clip': 'Sample text', 'alias': 'Example Clip'}]
        """
        print(f'[DEBUG] get_clips_for_component called with more_comp_id: {more_comp_id}')
        try:
            with ConnectionPoolManager().get_connection() as conn:
                query = """
                    SELECT c.clip_id, c.clip, c.alias
                    FROM clips_tbl c
                    JOIN clipsmore_tbl cm ON c.clip_id = cm.clip_id
                    WHERE cm.more_comp_id = ?
                """
                cursor = conn.cursor()
                cursor.execute(query, (more_comp_id,))
                results = cursor.fetchall()

                return [
                    {
                        'clip_id': row[0],
                        'clip': row[1],
                        'alias': row[2]
                    }
                    for row in results
                ]

        except sqlite3.Error as e:
            raise ClipError(f"Database error: {str(e)}") from e

    def copy_component(self, comp_id: int, target_bus_id: int) -> None:
        """Copy a component and its clip associations to a new business case.
        
        Args:
            comp_id: The ID of the component to copy
            target_bus_id: The ID of the target business case
            
        Raises:
            ClipError: If any database operation fails
        """
        print(f'[DEBUG] copy_component called with comp_id: {comp_id}, target_bus_id: {target_bus_id}')
        from .op_more_tbl import MoreTableOperations
        
        try:
            # Create new component in target business case
            more_ops = MoreTableOperations()
            comp_data = more_ops.read_component(comp_id)
            
            # Create copy of component with new business case ID
            new_comp_id = more_ops.create_component({
                'more_bus_id': target_bus_id,
                'comp_name': comp_data['comp_name'] + " (Copy)",
                'comp_desc': comp_data['comp_desc'],
                'comp_type': comp_data['comp_type'],
                'comp_priority': comp_data['comp_priority'],
                'comp_status': comp_data['comp_status'],
                'comp_notes': comp_data['comp_notes']
            })
            
            # Copy all clip associations
            with ConnectionPoolManager().get_connection() as conn:
                cursor = conn.cursor()
                
                # Get all clip associations for original component
                cursor.execute("""
                    SELECT clip_id, more_bus_id, more_comp_id
                    FROM clipsmore_tbl
                    WHERE more_comp_id = ?
                """, (comp_id,))
                associations = cursor.fetchall()
                
                # Create new associations for the copied component
                for clip_id, _, _ in associations:
                    cursor.execute("""
                        INSERT INTO clipsmore_tbl (clip_id, more_bus_id, more_comp_id)
                        VALUES (?, ?, ?)
                    """, (clip_id, target_bus_id, new_comp_id))
                    
                conn.commit()
                
        except Exception as e:
            raise ClipError(f"Failed to copy component: {str(e)}") from e

        except sqlite3.Error as e:
            raise ClipError(f"Component query failed: {str(e)}") from e

    def get_clips_for_business(self, more_bus_id: int) -> List[Dict[str, Any]]:
        """Retrieve clips associated with a business case (no components).

        Args:
            more_bus_id: Positive integer >0 identifying business case

        Returns:
            list: Dictionaries with keys:
                - clip_id: Unique clip identifier
                - clip: Clip content text
                - alias: Clip display name/alias

        Raises:
            ClipError: Database error during operation

        Example:
            >>> ops = ClipsForComponentOperations()
            >>> ops.get_clips_for_business(789)
            [{'clip_id': 123, 'clip': 'Sample text', 'alias': 'Example Clip'}]
        """
        print(f'[DEBUG] get_clips_for_business called with more_bus_id: {more_bus_id}')
        try:
            with ConnectionPoolManager().get_connection() as conn:
                query = """
                    SELECT c.clip_id, c.clip, c.alias
                    FROM clips_tbl c
                    JOIN clipsmore_tbl cm ON c.clip_id = cm.clip_id
                    WHERE cm.more_bus_id = ? AND cm.more_comp_id IS NULL
                    ORDER BY c.alias
                """
                cursor = conn.cursor()
                cursor.execute(query, (more_bus_id,))
                results = cursor.fetchall()

                clips = []
                for row in results:
                    clips.append({
                        'clip_id': row[0],
                        'clip': row[1],
                        'alias': row[2] or f"Clip {row[0]}"  # Use alias or default name
                    })

                return clips

        except sqlite3.Error as e:
            raise ClipError(f"Business case query failed: {str(e)}") from e

# NOTE: All new code should include debug print statements at the start of every function/method.