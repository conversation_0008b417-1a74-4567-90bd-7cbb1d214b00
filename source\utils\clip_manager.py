"""
📋 ClipManager - Handles all clip-related operations and UI components

This manager is responsible for:
- Creating and managing clip widgets
- Loading clips from database
- Handling clip operations (copy, delete, clear)
- Managing clip assignments and aliases
- Providing clip interface components

Part of the UIManager refactoring to improve code organization and maintainability.
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Dict, List, Optional


class ClipManager:
    """Manages all clip-related operations and UI components"""
    
    def __init__(self, parent: tk.Widget, theme_manager, database_manager, validation_manager, ui_manager=None):
        """Initialize ClipManager with required dependencies"""
        print('[DEBUG] ClipManager.__init__ called')

        self.parent = parent
        self.theme_manager = theme_manager
        self.database_manager = database_manager
        self.validation_manager = validation_manager
        self.ui_manager = ui_manager
        self.notification_manager = None  # Will be set by UIManager
        
        # UI components
        self.clips_scrollable_frame = None
        self.clips_canvas = None
        self.clips_scrollbar = None
        
        # State tracking
        self.clip_widgets = {}

        # Sorting state
        self.current_sort_by = "timestamp"  # Default sort by timestamp
        self.current_sort_order = "desc"    # Default descending order (newest first)

        # Get undo manager from ui_manager if available
        self.undo_manager = None
        if ui_manager and hasattr(ui_manager, 'undo_manager'):
            self.undo_manager = ui_manager.undo_manager

    def set_notification_manager(self, notification_manager):
        """Set the notification manager for this clip manager."""
        print('[DEBUG] ClipManager.set_notification_manager called')
        self.notification_manager = notification_manager
        
    def create_clips_interface(self) -> tk.Frame:
        """Create and return the main clips interface frame"""
        print('[DEBUG] ClipManager.create_clips_interface called')
        
        # Main frame for clips tab
        clips_frame = tk.Frame(self.parent, bg=self.theme_manager.bg_color)
        
        # Header with title and clear all button
        header_frame = tk.Frame(clips_frame, bg=self.theme_manager.bg_color)
        header_frame.pack(fill='x', padx=10, pady=5)
        
        title_label = tk.Label(header_frame, text="📋 Clipboard Manager", 
                              bg=self.theme_manager.bg_color, fg=self.theme_manager.fg_color,
                              font=('Arial', 14, 'bold'))
        title_label.pack(side=tk.LEFT)
        
        # Clear all button
        clear_all_btn = tk.Button(header_frame, text="Clear All Clips",
                                 command=self.clear_all_clips,
                                 bg="#d32f2f", fg="white",
                                 activebackground="#b71c1c", activeforeground="white",
                                 font=('Arial', 9, 'bold'))
        clear_all_btn.pack(side=tk.RIGHT, padx=5)

        # Create sorting controls
        self._create_sorting_controls(clips_frame)

        # Create scrollable area for clips
        self._create_scrollable_area(clips_frame)
        
        return clips_frame

    def _create_sorting_controls(self, parent_frame: tk.Frame):
        """Create sorting controls for clips"""
        print('[DEBUG] ClipManager._create_sorting_controls called')

        # Sorting controls frame
        sort_frame = tk.Frame(parent_frame, bg=self.theme_manager.bg_color)
        sort_frame.pack(fill='x', padx=10, pady=(0, 5))

        # Sort by label
        sort_label = tk.Label(sort_frame, text="Sort by:",
                             bg=self.theme_manager.bg_color,
                             fg=self.theme_manager.fg_color,
                             font=('Arial', 10))
        sort_label.pack(side=tk.LEFT, padx=(0, 5))

        # Sort by dropdown
        self.sort_by_var = tk.StringVar(value="Timestamp")
        sort_by_options = ["Timestamp", "Clip ID", "Alias"]
        self.sort_by_combo = ttk.Combobox(sort_frame, textvariable=self.sort_by_var,
                                         values=sort_by_options, state='readonly',
                                         width=12, font=('Arial', 9))
        self.sort_by_combo.pack(side=tk.LEFT, padx=(0, 10))
        self.sort_by_combo.bind('<<ComboboxSelected>>', self._on_sort_change)

        # Sort order label
        order_label = tk.Label(sort_frame, text="Order:",
                              bg=self.theme_manager.bg_color,
                              fg=self.theme_manager.fg_color,
                              font=('Arial', 10))
        order_label.pack(side=tk.LEFT, padx=(0, 5))

        # Sort order dropdown
        self.sort_order_var = tk.StringVar(value="Descending")
        sort_order_options = ["Descending", "Ascending"]
        self.sort_order_combo = ttk.Combobox(sort_frame, textvariable=self.sort_order_var,
                                            values=sort_order_options, state='readonly',
                                            width=12, font=('Arial', 9))
        self.sort_order_combo.pack(side=tk.LEFT, padx=(0, 10))
        self.sort_order_combo.bind('<<ComboboxSelected>>', self._on_sort_change)

        # Refresh button
        refresh_btn = tk.Button(sort_frame, text="🔄 Refresh",
                               command=self.load_clips,
                               bg=self.theme_manager.button_bg,
                               fg=self.theme_manager.button_fg,
                               activebackground=self.theme_manager.tree_select,
                               activeforeground=self.theme_manager.fg_color,
                               font=('Arial', 9))
        refresh_btn.pack(side=tk.LEFT, padx=5)

        # Clips count label (will be updated when clips are loaded)
        self.clips_count_label = tk.Label(sort_frame, text="",
                                         bg=self.theme_manager.bg_color,
                                         fg=self.theme_manager.fg_color,
                                         font=('Arial', 9))
        self.clips_count_label.pack(side=tk.RIGHT, padx=5)

    def _create_scrollable_area(self, parent_frame: tk.Frame):
        """Create scrollable area for clip widgets"""
        print('[DEBUG] ClipManager._create_scrollable_area called')
        
        # Create canvas and scrollbar for clips
        self.clips_canvas = tk.Canvas(parent_frame, bg=self.theme_manager.bg_color)
        self.clips_scrollbar = ttk.Scrollbar(parent_frame, orient="vertical", 
                                           command=self.clips_canvas.yview)
        self.clips_scrollable_frame = tk.Frame(self.clips_canvas, bg=self.theme_manager.bg_color)
        
        # Configure scrolling
        self.clips_scrollable_frame.bind(
            "<Configure>",
            lambda e: self.clips_canvas.configure(scrollregion=self.clips_canvas.bbox("all"))
        )
        
        # Create window with horizontal expansion
        self.clips_canvas.create_window((0, 0), window=self.clips_scrollable_frame, anchor="nw")
        self.clips_canvas.configure(yscrollcommand=self.clips_scrollbar.set)

        # Bind canvas resize to update scrollable frame width
        def on_canvas_configure(event):
            # Update the scrollable frame width to match canvas width
            canvas_width = event.width
            self.clips_canvas.itemconfig(self.clips_canvas.find_all()[0], width=canvas_width)

        self.clips_canvas.bind('<Configure>', on_canvas_configure)
        
        # Pack canvas and scrollbar
        self.clips_canvas.pack(side="left", fill="both", expand=True, padx=(10, 0), pady=5)
        self.clips_scrollbar.pack(side="right", fill="y", pady=5)

    def _on_sort_change(self, event=None):
        """Handle sort option change"""
        print('[DEBUG] ClipManager._on_sort_change called')

        # Update internal sort state based on UI selections
        sort_by_text = self.sort_by_var.get()
        sort_order_text = self.sort_order_var.get()

        # Map UI text to database column names
        sort_by_mapping = {
            "Timestamp": "timestamp",
            "Clip ID": "clip_id",
            "Alias": "alias"
        }

        # Map UI text to sort order
        sort_order_mapping = {
            "Descending": "desc",
            "Ascending": "asc"
        }

        self.current_sort_by = sort_by_mapping.get(sort_by_text, "timestamp")
        self.current_sort_order = sort_order_mapping.get(sort_order_text, "desc")

        print(f'[DEBUG] Sort changed to: {self.current_sort_by} {self.current_sort_order}')

        # Reload clips with new sorting
        self.load_clips()

    def _get_sorted_clips(self, clips_ops):
        """Get clips with custom sorting based on current sort settings"""
        print(f'[DEBUG] ClipManager._get_sorted_clips called with sort_by={self.current_sort_by}, order={self.current_sort_order}')

        try:
            # Get all clips first
            clips = clips_ops.read_all_clips()

            if not clips:
                return clips

            # Apply custom sorting based on current settings
            if self.current_sort_by == "clip_id":
                # Sort by clip ID
                clips.sort(key=lambda x: x.get('clip_id', 0),
                          reverse=(self.current_sort_order == "desc"))
            elif self.current_sort_by == "alias":
                # Sort by alias (generated aliases that are displayed in UI)
                clips_with_aliases = []

                for clip in clips:
                    clip_id = clip.get('clip_id')
                    clip_content = clip.get('clip', '')

                    # Get the same alias that would be generated/displayed in the UI
                    alias = self._get_or_generate_alias(clip_id, clip_content)

                    # Create a copy of clip with alias for sorting
                    clip_copy = clip.copy()
                    clip_copy['sort_alias'] = alias.lower() if alias else ""
                    clips_with_aliases.append(clip_copy)

                    print(f'[DEBUG] Clip {clip_id} alias for sorting: "{alias}"')

                # Sort by alias
                clips_with_aliases.sort(key=lambda x: x.get('sort_alias', ''),
                                       reverse=(self.current_sort_order == "desc"))
                clips = clips_with_aliases

                print(f'[DEBUG] Alias sort order: {[clip.get("sort_alias", "") for clip in clips[:5]]}...')

            else:  # timestamp (default)
                # Sort by timestamp - already handled by read_all_clips() with ORDER BY timestamp DESC
                # But we might need to reverse if user wants oldest first
                if self.current_sort_order == "asc":
                    clips.reverse()

            print(f'[DEBUG] Sorted {len(clips)} clips by {self.current_sort_by} ({self.current_sort_order})')
            return clips

        except Exception as e:
            print(f'[ERROR] Failed to sort clips: {e}')
            # Fallback to default sorting
            return clips_ops.read_all_clips()

    def load_clips(self):
        """Load all clips from database and create widgets"""
        print('[DEBUG] ClipManager.load_clips called')
        
        # Clear existing widgets
        for widget in self.clips_scrollable_frame.winfo_children():
            widget.destroy()
        self.clip_widgets.clear()
        
        try:
            clips_ops = self.database_manager.get_clips_operations()

            # Get clips with custom sorting
            clips = self._get_sorted_clips(clips_ops)

            # Update clips count label
            if hasattr(self, 'clips_count_label'):
                self.clips_count_label.config(text=f"Total clips: {len(clips)}")

            if not clips:
                no_clips_label = tk.Label(self.clips_scrollable_frame,
                                        text="No clips found. Copy something to clipboard to get started!",
                                        bg=self.theme_manager.bg_color,
                                        fg=self.theme_manager.fg_color,
                                        font=('Arial', 12))
                no_clips_label.pack(pady=20)
                return

            # Create individual clip management widgets
            for i, clip in enumerate(clips):
                self.create_clip_widget(clip, i)
                
        except Exception as e:
            print(f'[ERROR] Failed to load clips: {e}')
            error_label = tk.Label(self.clips_scrollable_frame,
                                 text=f"Error loading clips: {str(e)}",
                                 bg=self.theme_manager.bg_color, 
                                 fg="red", font=('Arial', 10))
            error_label.pack(pady=10)
    
    def create_clip_widget(self, clip: Dict, index: int):
        """Create individual clip management widget"""
        print(f'[DEBUG] ClipManager.create_clip_widget called for clip {index}')
        
        clip_id = clip.get('clip_id')
        clip_content = clip.get('clip', '')
        clip_timestamp = clip.get('timestamp', '')
        
        # Main frame for this clip - optimized layout with horizontal expansion
        clip_frame = tk.Frame(self.clips_scrollable_frame,
                             bg=self.theme_manager.entry_bg,
                             relief='raised', bd=1)
        clip_frame.pack(fill='both', padx=2, pady=2, expand=True)
        
        # Store widget reference
        self.clip_widgets[clip_id] = clip_frame
        
        # Create header, content, and management sections
        self._create_clip_header(clip_frame, clip_id, clip_timestamp)
        self._create_clip_content(clip_frame, clip_content)
        self._create_clip_management(clip_frame, clip_id, clip_content)
    
    def _create_clip_header(self, clip_frame: tk.Frame, clip_id: int, timestamp: str):
        """Create header section of clip widget"""
        print(f'[DEBUG] ClipManager._create_clip_header called for clip {clip_id}')
        
        # Header row with clip info and copy button
        header_frame = tk.Frame(clip_frame, bg=self.theme_manager.entry_bg)
        header_frame.pack(fill='x', padx=3, pady=1)
        
        # Clip ID and timestamp
        info_text = f"Clip #{clip_id}"
        if timestamp:
            from source.utils.utility_manager import UtilityManager
            formatted_time = UtilityManager.format_timestamp(timestamp)
            info_text += f" • {formatted_time}"
            
        info_label = tk.Label(header_frame, text=info_text,
                             bg=self.theme_manager.entry_bg, 
                             fg=self.theme_manager.entry_fg,
                             font=('Arial', 9, 'bold'))
        info_label.pack(side=tk.LEFT)
        
        # Copy button
        copy_btn = tk.Button(header_frame, text="📋 Copy",
                           command=lambda cid=clip_id: self.copy_clip_to_clipboard(cid),
                           bg=self.theme_manager.button_bg, 
                           fg=self.theme_manager.button_fg,
                           activebackground=self.theme_manager.tree_select, 
                           activeforeground=self.theme_manager.fg_color,
                           font=('Arial', 9))
        copy_btn.pack(side=tk.RIGHT, padx=2)
    
    def _create_clip_content(self, clip_frame: tk.Frame, content: str):
        """Create content preview section of clip widget"""
        print('[DEBUG] ClipManager._create_clip_content called')
        
        # Content preview with better text display
        content_frame = tk.Frame(clip_frame, bg=self.theme_manager.entry_bg)
        content_frame.pack(fill='both', padx=3, pady=1, expand=True)
        
        # Use Text widget for better content display with scrolling
        content_text = tk.Text(content_frame, height=3, wrap=tk.WORD,
                              bg=self.theme_manager.bg_color, 
                              fg=self.theme_manager.fg_color, 
                              font=('Arial', 9),
                              relief='sunken', bd=1, state='disabled')
        content_text.pack(fill='both', expand=True)
        
        # Insert content
        content_text.config(state='normal')
        content_text.insert('1.0', content)
        content_text.config(state='disabled')
    
    def _create_clip_management(self, clip_frame: tk.Frame, clip_id: int, clip_content: str):
        """Create management section of clip widget with alias and assignment controls"""
        print(f'[DEBUG] ClipManager._create_clip_management called for clip {clip_id}')
        
        # Management row with alias, dropdown, and assign button
        mgmt_frame = tk.Frame(clip_frame, bg=self.theme_manager.entry_bg)
        mgmt_frame.pack(fill='x', padx=3, pady=1)
        
        # Alias field
        tk.Label(mgmt_frame, text="Alias:", 
                bg=self.theme_manager.entry_bg, fg=self.theme_manager.entry_fg,
                font=('Arial', 9)).pack(side=tk.LEFT, padx=(0, 5))
        
        alias_var = tk.StringVar()
        alias_entry = tk.Entry(mgmt_frame, textvariable=alias_var, width=15,
                             bg=self.theme_manager.bg_color, 
                             fg=self.theme_manager.fg_color, 
                             insertbackground=self.theme_manager.fg_color,
                             font=('Arial', 9))
        alias_entry.pack(side=tk.LEFT, padx=(0, 5))
        
        # Validation indicator
        validation_label = tk.Label(mgmt_frame, text="✓", fg="green",
                                   bg=self.theme_manager.entry_bg, 
                                   font=("Arial", 9, "bold"), width=2)
        validation_label.pack(side=tk.LEFT, padx=(0, 10))
        
        # Auto-generate alias if not already assigned
        current_alias = self._get_or_generate_alias(clip_id, clip_content)
        alias_var.set(current_alias)
        
        # Real-time validation
        def validate_alias_realtime(*args):
            print('[DEBUG] validate_alias_realtime called')
            self.validation_manager.validate_alias_realtime(alias_var.get(), validation_label)
        
        alias_var.trace_add('write', validate_alias_realtime)
        validate_alias_realtime()  # Initial validation
        
        # Assignment controls
        self._create_assignment_controls(mgmt_frame, clip_id, alias_var)
        
        # Delete button
        delete_btn = tk.Button(mgmt_frame, text="🗑️",
                             command=lambda cid=clip_id: self.delete_clip(cid),
                             bg="#d32f2f", fg="white",
                             activebackground="#b71c1c", activeforeground="white",
                             font=('Arial', 9), width=3)
        delete_btn.pack(side=tk.RIGHT, padx=2)
    
    def _create_assignment_controls(self, mgmt_frame: tk.Frame, clip_id: int, alias_var: tk.StringVar):
        """Create assignment dropdown and assign button"""
        print(f'[DEBUG] ClipManager._create_assignment_controls called for clip {clip_id}')

        # Business case/component dropdown
        tk.Label(mgmt_frame, text="Assign to:",
                bg=self.theme_manager.entry_bg, fg=self.theme_manager.entry_fg,
                font=('Arial', 9)).pack(side=tk.LEFT, padx=(0, 5))

        assignment_var = tk.StringVar()
        assignment_combo = ttk.Combobox(mgmt_frame, textvariable=assignment_var,
                                      width=25, font=('Arial', 9), state='readonly')
        assignment_combo.pack(side=tk.LEFT, padx=(0, 10))

        # Populate dropdown with business cases and components, and set current assignment
        self._populate_assignment_dropdown(assignment_combo, clip_id)

        # Assign button
        assign_btn = tk.Button(mgmt_frame, text="Assign",
                             command=lambda: self._assign_clip(clip_id, alias_var.get(),
                                                             assignment_var.get()),
                             bg=self.theme_manager.button_bg,
                             fg=self.theme_manager.button_fg,
                             activebackground=self.theme_manager.tree_select,
                             activeforeground=self.theme_manager.fg_color,
                             font=('Arial', 9))
        assign_btn.pack(side=tk.LEFT, padx=5)

    def _get_or_generate_alias(self, clip_id: int, clip_content: str) -> str:
        """Get existing alias or generate new one for clip using advanced algorithm"""
        print(f'[DEBUG] ClipManager._get_or_generate_alias called for clip_id={clip_id}')

        try:
            # First, check if clip already has an assignment with an alias
            clipsmore_ops = self.database_manager.get_enhanced_operations()
            existing_assignments = clipsmore_ops.get_assignments_by_clip(clip_id)

            if existing_assignments:
                # Use the alias from the first (most recent) assignment
                existing_alias = existing_assignments[0].get('alias')
                if existing_alias:
                    print(f'[DEBUG] Found existing alias: {existing_alias}')
                    return existing_alias

            # No existing assignment found, generate new alias
            from source.utils.alias_generator import AliasGenerator

            # Get existing aliases for uniqueness checking
            existing_aliases = self.database_manager.get_existing_aliases()

            # Initialize alias generator
            generator = AliasGenerator()
            generator.set_existing_aliases(existing_aliases)

            # Generate intelligent alias
            alias = generator.generate_from_content(str(clip_content) if clip_content else "")

            print(f'[DEBUG] Generated new alias: {alias}')
            return alias

        except Exception as e:
            print(f'[ERROR] Failed to get or generate alias: {e}')
            return f"clip_{clip_id}"

    def _populate_assignment_dropdown(self, combobox: ttk.Combobox, clip_id: int = None):
        """Populate assignment dropdown with business cases and components"""
        print(f'[DEBUG] ClipManager._populate_assignment_dropdown called for clip_id={clip_id}')

        try:
            from source.utils.utility_manager import UtilityManager

            more_ops = self.database_manager.get_more_operations()
            options = ["None"]  # Option for no assignment
            max_length = len("None")
            current_assignment = "None"  # Default to None

            # Get current assignment for this clip if clip_id is provided
            if clip_id:
                try:
                    enhanced_ops = self.database_manager.get_enhanced_operations()
                    assignments = enhanced_ops.get_assignments_by_clip(clip_id)
                    if assignments:
                        # Get the most recent assignment
                        assignment = assignments[0]
                        bc_name = assignment.get('business_case_name', '')
                        comp_name = assignment.get('component_name', '')

                        if comp_name:
                            current_assignment = f"BC: {bc_name} > {comp_name}"
                        elif bc_name:
                            current_assignment = f"BC: {bc_name}"

                        print(f'[DEBUG] Found current assignment: {current_assignment}')
                except Exception as e:
                    print(f'[WARNING] Failed to get current assignment for clip {clip_id}: {e}')

            # Get business cases
            business_cases = more_ops.read_all_business_cases()
            for bc in business_cases:
                bc_name = bc.get('name', '')
                bc_option = f"BC: {bc_name}"
                options.append(bc_option)
                max_length = max(max_length, len(bc_option))

                # Get components for this business case
                bc_id = bc.get('id')
                components = more_ops.read_components_for_business_case(bc_id)
                for comp in components:
                    comp_name = comp.get('name', '')
                    comp_option = f"BC: {bc_name} > {comp_name}"
                    options.append(comp_option)
                    max_length = max(max_length, len(comp_option))

            # Set combobox values and current selection
            combobox['values'] = options
            combobox.set(current_assignment)  # Set to current assignment or "None"
            UtilityManager.auto_size_dropdown(combobox, options)

            print(f'[DEBUG] Dropdown populated with {len(options)} options, current: {current_assignment}')

        except Exception as e:
            print(f'[ERROR] Failed to populate dropdown: {e}')
            combobox['values'] = ["None"]
            combobox.set("None")

    def _assign_clip(self, clip_id: int, alias: str, assignment: str):
        """Assign clip to business case/component with alias and advanced validation"""
        print(f'[DEBUG] ClipManager._assign_clip called for clip_id={clip_id}, alias={alias}, assignment={assignment}')

        try:
            # Validate alias using centralized ValidationManager
            if self.validation_manager:
                if not self.validation_manager.validate_alias_with_generator(alias):
                    if self.notification_manager:
                        self.notification_manager.show_error(
                            "Alias must start with a letter and contain only letters, numbers, underscores, and hyphens."
                        )
                    else:
                        messagebox.showerror("Invalid Alias",
                                           "Alias must start with a letter and contain only letters, numbers, underscores, and hyphens.")
                    return
            else:
                # Fallback to direct AliasGenerator if ValidationManager not available
                print('[WARNING] ValidationManager not available, using fallback validation')
                from source.utils.alias_generator import AliasGenerator
                generator = AliasGenerator()
                if not generator.validate_alias(alias.strip()):
                    if self.notification_manager:
                        self.notification_manager.show_error(
                            "Alias must start with a letter and contain only letters, numbers, underscores, and hyphens."
                        )
                    else:
                        messagebox.showerror("Invalid Alias",
                                           "Alias must start with a letter and contain only letters, numbers, underscores, and hyphens.")
                    return

            if assignment == "None":
                print('[DEBUG] No assignment selected')
                return

            # Parse assignment string to get business case and component
            more_ops = self.database_manager.get_more_operations()
            clipsmore_ops = self.database_manager.get_enhanced_operations()

            if assignment.startswith("BC: "):
                assignment_parts = assignment[4:].split(" > ")
                bc_name = assignment_parts[0]

                # Get business case ID
                business_cases = more_ops.read_all_business_cases()
                bc_id = None
                for bc in business_cases:
                    if bc.get('name') == bc_name:
                        bc_id = bc.get('id')
                        break

                if not bc_id:
                    print(f'[ERROR] Business case not found: {bc_name}')
                    return

                comp_id = None
                if len(assignment_parts) > 1:
                    # Component assignment
                    comp_name = assignment_parts[1]
                    components = more_ops.read_components_for_business_case(bc_id)
                    for comp in components:
                        if comp.get('name') == comp_name:
                            comp_id = comp.get('id')
                            break

                    if not comp_id:
                        print(f'[ERROR] Component not found: {comp_name}')
                        return

                # Check if clip already has an assignment to this business case/component
                existing_assignments = clipsmore_ops.get_assignments_by_clip(clip_id)
                existing_transaction_id = None

                for existing in existing_assignments:
                    if (existing.get('more_bus_id') == bc_id and
                        existing.get('more_comp_id') == comp_id):
                        existing_transaction_id = existing.get('transaction_id')
                        print(f'[DEBUG] Found existing assignment with transaction_id={existing_transaction_id}')
                        break

                try:
                    if existing_transaction_id:
                        # Update existing assignment with new alias
                        success = clipsmore_ops.update_assignment(
                            transaction_id=existing_transaction_id,
                            alias=alias.strip()
                        )

                        if success:
                            print(f'[DEBUG] Updated assignment {existing_transaction_id} with new alias: {alias.strip()}')
                            if self.notification_manager:
                                self.notification_manager.show_success(
                                    f"Clip alias updated successfully to '{alias.strip()}'"
                                )
                            else:
                                messagebox.showinfo("Assignment Updated",
                                                  f"Clip alias updated successfully to '{alias.strip()}'")
                        else:
                            raise Exception("Failed to update assignment")
                    else:
                        # Create new assignment
                        transaction_id = clipsmore_ops.create_assignment(
                            clip_id=clip_id,
                            more_bus_id=bc_id,
                            more_comp_id=comp_id,
                            alias=alias.strip()
                        )

                        print(f'[DEBUG] Created assignment with transaction_id={transaction_id}')
                        if self.notification_manager:
                            self.notification_manager.show_success(
                                f"Clip assigned successfully with alias '{alias.strip()}'"
                            )
                        else:
                            messagebox.showinfo("Assignment Created",
                                              f"Clip assigned successfully with alias '{alias.strip()}'")

                    # Refresh clips display to update assignment dropdown
                    self.load_clips()

                    # Notify UI manager to refresh tree displays (tree and alias buttons)
                    if self.ui_manager and hasattr(self.ui_manager, 'tree_manager'):
                        self.ui_manager.tree_manager.refresh_tree()

                except Exception as assign_error:
                    self._handle_assignment_error(assign_error, alias, clip_id, bc_id, comp_id)

        except Exception as e:
            print(f'[ERROR] Failed to assign clip: {e}')
            if self.notification_manager:
                self.notification_manager.show_error(f"Failed to assign clip: {str(e)}")
            else:
                messagebox.showerror("Assignment Failed", f"Failed to assign clip: {str(e)}")

    def _handle_assignment_error(self, error: Exception, alias: str, clip_id: int, bc_id: int, comp_id: Optional[int]):
        """Handle assignment errors, particularly alias conflicts"""
        print(f'[DEBUG] ClipManager._handle_assignment_error called for alias={alias}')

        error_msg = str(error)
        if "UNIQUE constraint failed" in error_msg or "alias" in error_msg.lower():
            # Handle alias conflict
            print(f'[DEBUG] Alias conflict detected, suggesting alternatives')

            from source.utils.alias_generator import AliasGenerator

            # Get existing aliases and suggest alternatives
            existing_aliases = self.database_manager.get_existing_aliases()
            generator = AliasGenerator()
            generator.set_existing_aliases(existing_aliases)
            alternatives = generator.suggest_alternatives(alias.strip(), 3)

            if alternatives:
                alt_text = ", ".join(alternatives)
                # For now, keep messagebox for user choice dialogs as notifications don't support user input
                # TODO: Consider implementing a custom dialog system for user choices
                response = messagebox.askyesno("Alias Conflict",
                                             f"The alias '{alias.strip()}' is already in use.\n\n"
                                             f"Suggested alternatives: {alt_text}\n\n"
                                             f"Would you like to use '{alternatives[0]}' instead?")

                if response:
                    # Check if we need to update existing assignment or create new one
                    clipsmore_ops = self.database_manager.get_enhanced_operations()
                    existing_assignments = clipsmore_ops.get_assignments_by_clip(clip_id)
                    existing_transaction_id = None

                    for existing in existing_assignments:
                        if (existing.get('more_bus_id') == bc_id and
                            existing.get('more_comp_id') == comp_id):
                            existing_transaction_id = existing.get('transaction_id')
                            break

                    if existing_transaction_id:
                        # Update existing assignment with alternative alias
                        success = clipsmore_ops.update_assignment(
                            transaction_id=existing_transaction_id,
                            alias=alternatives[0]
                        )
                        if success:
                            print(f'[DEBUG] Updated assignment {existing_transaction_id} with alternative alias: {alternatives[0]}')
                            if self.notification_manager:
                                self.notification_manager.show_success(
                                    f"Clip alias updated successfully to '{alternatives[0]}'"
                                )
                            else:
                                messagebox.showinfo("Assignment Updated",
                                                  f"Clip alias updated successfully to '{alternatives[0]}'")
                        else:
                            raise Exception("Failed to update assignment with alternative alias")
                    else:
                        # Create new assignment with alternative alias
                        transaction_id = clipsmore_ops.create_assignment(
                            clip_id=clip_id,
                            more_bus_id=bc_id,
                            more_comp_id=comp_id,
                            alias=alternatives[0]
                        )
                        print(f'[DEBUG] Created assignment with alternative alias: {alternatives[0]}')
                        if self.notification_manager:
                            self.notification_manager.show_success(
                                f"Clip assigned successfully with alias '{alternatives[0]}'"
                            )
                        else:
                            messagebox.showinfo("Assignment Created",
                                              f"Clip assigned successfully with alias '{alternatives[0]}'")

                    # Refresh clips display to update assignment dropdown
                    self.load_clips()

                    # Notify UI manager to refresh tree displays (tree and alias buttons)
                    if self.ui_manager and hasattr(self.ui_manager, 'tree_manager'):
                        self.ui_manager.tree_manager.refresh_tree()
                else:
                    print('[DEBUG] User declined alternative alias')
            else:
                if self.notification_manager:
                    self.notification_manager.show_error(
                        f"Alias '{alias.strip()}' is already in use. Please choose a different alias."
                    )
                else:
                    messagebox.showerror("Assignment Failed",
                                       f"Alias '{alias.strip()}' is already in use. Please choose a different alias.")
        else:
            # Other database error
            if self.notification_manager:
                self.notification_manager.show_error(f"Failed to create or update assignment: {error_msg}")
            else:
                messagebox.showerror("Assignment Failed", f"Failed to create or update assignment: {error_msg}")
            print(f'[ERROR] Assignment failed: {error_msg}')

    def copy_clip_to_clipboard(self, clip_id: int):
        """Copy clip content to OS clipboard"""
        print(f'[DEBUG] ClipManager.copy_clip_to_clipboard called for clip_id={clip_id}')

        try:
            clips_ops = self.database_manager.get_clips_operations()
            clips = clips_ops.read_all_clips()

            for clip in clips:
                if clip.get('clip_id') == clip_id:
                    content = clip.get('clip', '')
                    if content:
                        # Get root window for clipboard operations
                        root = self.parent
                        while root.master:
                            root = root.master

                        root.clipboard_clear()
                        root.clipboard_append(content)
                        print(f'[DEBUG] Copied clip {clip_id} to clipboard')

                        # Show brief feedback
                        if self.notification_manager:
                            self.notification_manager.show_success(f"Clip #{clip_id} copied to clipboard!")
                        else:
                            messagebox.showinfo("Copied", f"Clip #{clip_id} copied to clipboard!")
                        return

            print(f'[ERROR] Clip {clip_id} not found')
            if self.notification_manager:
                self.notification_manager.show_error(f"Clip #{clip_id} not found")
            else:
                messagebox.showerror("Error", f"Clip #{clip_id} not found")

        except Exception as e:
            print(f'[ERROR] Failed to copy clip: {e}')
            if self.notification_manager:
                self.notification_manager.show_error(f"Failed to copy clip: {str(e)}")
            else:
                messagebox.showerror("Error", f"Failed to copy clip: {str(e)}")

    def delete_clip(self, clip_id: int):
        """Delete clip and all its assignments with undo support"""
        print(f'[DEBUG] ClipManager.delete_clip called for clip_id={clip_id}')

        try:
            # Confirm deletion - keep messagebox for user confirmation as notifications don't support user input
            result = messagebox.askyesno("Confirm Delete",
                                       f"Are you sure you want to delete clip #{clip_id}?\n"
                                       "This will also remove all assignments.")

            if result:
                # Store clip data for undo before deletion
                clips_ops = self.database_manager.get_clips_operations()
                enhanced_ops = self.database_manager.get_enhanced_operations()

                # Get clip data
                all_clips = clips_ops.read_all_clips()
                clip_data = None
                for clip in all_clips:
                    if clip.get('clip_id') == clip_id:
                        clip_data = clip.copy()
                        break

                # Get assignments data
                assignments_data = enhanced_ops.get_assignments_by_clip(clip_id)

                if clip_data:
                    # Delete the clip
                    clips_ops.delete_clip(clip_id)
                    print(f'[DEBUG] Deleted clip {clip_id}')

                    # Add undo action if undo manager is available
                    if self.undo_manager:
                        self.undo_manager.add_action(
                            action_type="delete_clip",
                            description=f"Delete clip #{clip_id}",
                            undo_function=self._undo_delete_clip,
                            undo_data={
                                'clip_data': clip_data,
                                'assignments_data': assignments_data
                            }
                        )

                        # Update undo button state using UIStateManager
                        if self.ui_manager and hasattr(self.ui_manager, 'ui_state_manager'):
                            self.ui_manager.ui_state_manager.update_undo_button_state()

                    # Refresh clips display
                    self.load_clips()

                    # Notify UI manager to refresh tree
                    if self.ui_manager and hasattr(self.ui_manager, 'tree_manager'):
                        self.ui_manager.tree_manager.refresh_tree()

                    if self.notification_manager:
                        self.notification_manager.show_success(f"Clip #{clip_id} deleted successfully")
                    else:
                        messagebox.showinfo("Deleted", f"Clip #{clip_id} deleted successfully")
                else:
                    print(f'[WARNING] Clip {clip_id} not found for deletion')

        except Exception as e:
            print(f'[ERROR] Failed to delete clip: {e}')
            if self.notification_manager:
                self.notification_manager.show_error(f"Failed to delete clip: {str(e)}")
            else:
                messagebox.showerror("Error", f"Failed to delete clip: {str(e)}")

    def _undo_delete_clip(self, clip_data: Dict, assignments_data: List[Dict]):
        """Undo clip deletion by recreating the clip and its assignments"""
        print(f'[DEBUG] ClipManager._undo_delete_clip called for clip_id={clip_data.get("clip_id")}')

        try:
            clips_ops = self.database_manager.get_clips_operations()
            enhanced_ops = self.database_manager.get_enhanced_operations()

            # Recreate the clip with original data
            clip_content = clip_data.get('clip', '')
            timestamp = clip_data.get('timestamp', '')
            original_clip_id = clip_data.get('clip_id')

            # Create clip (this will generate a new clip_id)
            new_clip_id = clips_ops.create_clip(clip_content, timestamp)
            print(f'[DEBUG] Recreated clip with new ID {new_clip_id} (original was {original_clip_id})')

            # Recreate assignments if any existed
            for assignment in assignments_data:
                try:
                    enhanced_ops.create_assignment(
                        clip_id=new_clip_id,
                        more_bus_id=assignment.get('more_bus_id'),
                        more_comp_id=assignment.get('more_comp_id'),
                        alias=assignment.get('alias', '')
                    )
                    print(f'[DEBUG] Recreated assignment for clip {new_clip_id}')
                except Exception as assign_error:
                    print(f'[WARNING] Failed to recreate assignment: {assign_error}')

            print(f'[DEBUG] Successfully undid deletion of clip {original_clip_id} (recreated as {new_clip_id})')

            # Refresh UI after undo
            self._refresh_ui_after_undo()

        except Exception as e:
            print(f'[ERROR] Failed to undo clip deletion: {e}')
            raise

    def clear_all_clips(self):
        """Clear all clips with confirmation and undo support"""
        print('[DEBUG] ClipManager.clear_all_clips called')

        try:
            # Confirm deletion - keep messagebox for user confirmation
            result = messagebox.askyesno("Confirm Clear All",
                                       "Are you sure you want to delete ALL clips?\n"
                                       "This will also remove all assignments.")

            if result:
                clips_ops = self.database_manager.get_clips_operations()
                enhanced_ops = self.database_manager.get_enhanced_operations()

                # Store all clips and assignments data for undo before deletion
                all_clips_data = clips_ops.read_all_clips()
                all_assignments_data = []

                # Get all assignments for all clips
                for clip in all_clips_data:
                    clip_id = clip.get('clip_id')
                    assignments = enhanced_ops.get_assignments_by_clip(clip_id)
                    all_assignments_data.extend(assignments)

                # Clear all clips (this should cascade delete assignments due to foreign key constraints)
                clips_ops.truncate_clips_table()
                print('[DEBUG] Cleared all clips')

                # Add undo action if undo manager is available
                if self.undo_manager and all_clips_data:
                    self.undo_manager.add_action(
                        action_type="clear_all_clips",
                        description=f"Clear all clips ({len(all_clips_data)} clips)",
                        undo_function=self._undo_clear_all_clips,
                        undo_data={
                            'all_clips_data': all_clips_data,
                            'all_assignments_data': all_assignments_data
                        }
                    )

                    # Update undo button state using UIStateManager
                    if self.ui_manager and hasattr(self.ui_manager, 'ui_state_manager'):
                        self.ui_manager.ui_state_manager.update_undo_button_state()

                # Refresh displays
                self.load_clips()

                # Notify UI manager to refresh tree
                if self.ui_manager and hasattr(self.ui_manager, 'tree_manager'):
                    self.ui_manager.tree_manager.refresh_tree()

                if self.notification_manager:
                    self.notification_manager.show_success("All clips cleared successfully")
                else:
                    messagebox.showinfo("Cleared", "All clips cleared successfully")

        except Exception as e:
            print(f'[ERROR] Failed to clear all clips: {e}')
            if self.notification_manager:
                self.notification_manager.show_error(f"Failed to clear all clips: {str(e)}")
            else:
                messagebox.showerror("Error", f"Failed to clear all clips: {str(e)}")

    def _undo_clear_all_clips(self, all_clips_data: List[Dict], all_assignments_data: List[Dict]):
        """Undo clear all clips by recreating all clips and their assignments"""
        print(f'[DEBUG] ClipManager._undo_clear_all_clips called for {len(all_clips_data)} clips')

        try:
            clips_ops = self.database_manager.get_clips_operations()
            enhanced_ops = self.database_manager.get_enhanced_operations()

            # Map old clip IDs to new clip IDs
            clip_id_mapping = {}

            # Recreate all clips
            for clip_data in all_clips_data:
                clip_content = clip_data.get('clip', '')
                timestamp = clip_data.get('timestamp', '')
                original_clip_id = clip_data.get('clip_id')

                # Create clip (this will generate a new clip_id)
                new_clip_id = clips_ops.create_clip(clip_content, timestamp)
                clip_id_mapping[original_clip_id] = new_clip_id
                print(f'[DEBUG] Recreated clip {original_clip_id} as {new_clip_id}')

            # Recreate all assignments
            for assignment in all_assignments_data:
                try:
                    original_clip_id = assignment.get('clip_id')
                    new_clip_id = clip_id_mapping.get(original_clip_id)

                    if new_clip_id:
                        enhanced_ops.create_assignment(
                            clip_id=new_clip_id,
                            more_bus_id=assignment.get('more_bus_id'),
                            more_comp_id=assignment.get('more_comp_id'),
                            alias=assignment.get('alias', '')
                        )
                        print(f'[DEBUG] Recreated assignment for clip {new_clip_id}')
                except Exception as assign_error:
                    print(f'[WARNING] Failed to recreate assignment: {assign_error}')

            print(f'[DEBUG] Successfully undid clear all clips operation')

            # Refresh UI after undo
            self._refresh_ui_after_undo()

        except Exception as e:
            print(f'[ERROR] Failed to undo clear all clips: {e}')
            raise

    def _refresh_ui_after_undo(self):
        """Refresh UI components after an undo operation - delegates to UIStateManager"""
        print('[DEBUG] ClipManager._refresh_ui_after_undo called')

        try:
            # Use centralized UI refresh through UIStateManager
            if self.ui_manager and hasattr(self.ui_manager, 'ui_state_manager'):
                self.ui_manager.ui_state_manager.refresh_after_undo()
            else:
                # Fallback to local refresh if UIStateManager not available
                self.load_clips()
                if self.ui_manager and hasattr(self.ui_manager, 'tree_manager'):
                    self.ui_manager.tree_manager.refresh_tree()

            print('[DEBUG] UI refreshed successfully after undo')

        except Exception as e:
            print(f'[ERROR] Failed to refresh UI after undo: {e}')

    def refresh_clips(self):
        """Refresh the clips display"""
        print('[DEBUG] ClipManager.refresh_clips called')
        self.load_clips()
