import os, sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import sqlite3
from typing import Optional, Dict, Any, List, Tuple
from pathlib import Path
from .db_connection import ConnectionPoolManager

class MoreError(Exception):
    """Base exception for more operations"""
    pass

class MoreNotFoundError(MoreError):
    """Raised when a business case or component is not found"""
    pass

class MoreValidationError(MoreError):
    """Raised when business case or component data is invalid"""
    pass

class MoreTableOperations:
    """Operations for managing business cases and their components.

    Attributes:
        pool (ConnectionPoolManager): Database connection pool instance
    """

    def __init__(self, db_path: Optional[str] = None):
        """Initialize with optional custom database path."""
        print('[DEBUG] MoreTableOperations.__init__ called')
        if db_path is None:
            db_path = str(Path(__file__).parent / "clipsmore_db.db")
        print(f'[DEBUG] [MoreTableOperations.__init__] Using database file path: {db_path}')
        self.pool = ConnectionPoolManager(db_path)

    def _validate_text(self, text: str, field_name: str) -> None:
        """Validate text fields."""
        if not isinstance(text, str):
            raise MoreValidationError(f"{field_name} must be a string")
        if len(text.strip()) == 0:
            raise MoreValidationError(f"{field_name} cannot be empty")

    def _validate_ids(self, bus_id: Optional[int] = None, comp_id: Optional[int] = None) -> None:
        """Validate ID fields."""
        if bus_id is not None:
            if not isinstance(bus_id, int):
                raise MoreValidationError("Business case ID must be a positive integer")
            elif bus_id <= 0:
                raise MoreValidationError("Business case ID must be a positive integer")
        if comp_id is not None:
            if not isinstance(comp_id, int):
                raise MoreValidationError("Component ID must be a positive integer")
            elif comp_id <= 0:
                raise MoreValidationError("Component ID must be a positive integer")

    def read_all_business_cases(self) -> List[Dict[str, Any]]:
        """
        Read all business cases with UI-compatible format.

        Returns:
            List of dictionaries with 'id' and 'name' keys for UI compatibility

        Raises:
            MoreError: If database operation fails
        """
        print('[DEBUG] MoreTableOperations.read_all_business_cases called')
        try:
            with self.pool.get_connection() as conn:
                query = "SELECT more_bus_id, bus_case FROM more_bus_tbl ORDER BY bus_case"
                cursor = conn.cursor()
                cursor.execute(query)
                results = cursor.fetchall()
                # Convert to UI-compatible format
                business_cases = [{'id': row[0], 'name': row[1]} for row in results]
                print(f'[DEBUG] Found {len(business_cases)} business cases')
                return business_cases

        except sqlite3.Error as e:
            raise MoreError(f"Database error: {str(e)}")

    def read_components(self, more_bus_id: int) -> List[Dict[str, Any]]:
        """
        Read components for a specific business case.

        Args:
            more_bus_id: ID of the business case

        Returns:
            List of dictionaries containing component data

        Raises:
            MoreValidationError: If more_bus_id is invalid
            MoreNotFoundError: If business case is not found
        """
        self._validate_ids(bus_id=more_bus_id)

        try:
            print(f"[DEBUG] Reading components for business case ID: {more_bus_id}")
            with self.pool.get_connection() as conn:
                # First verify business case exists
                if not self._business_case_exists(more_bus_id):
                    print(f"Business case {more_bus_id} not found")
                    raise MoreNotFoundError(f"Business case with ID {more_bus_id} not found")

                query = """
                SELECT more_comp_id, bus_component
                FROM more_comp_tbl
                WHERE more_bus_id = ?
                ORDER BY bus_component
                """
                print(f"Executing query: {query} with ID: {more_bus_id}")
                cursor = conn.cursor()
                cursor.execute(query, (more_bus_id,))
                columns = ["more_comp_id", "bus_component"]
                results = cursor.fetchall()
                print(f"Found {len(results)} components: {results}")

                components = [dict(zip(columns, row)) for row in results]
                print(f"Returning components: {components}")
                return components

        except sqlite3.Error as e:
            raise MoreError(f"Database error: {str(e)}")

    def _business_case_exists(self, more_bus_id: int) -> bool:
        """Check if a business case exists."""
        try:
            with self.pool.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT 1 FROM more_bus_tbl WHERE more_bus_id = ?", (more_bus_id,))
                return bool(cursor.fetchone())
        except sqlite3.Error as e:
            raise MoreError(f"Database error: {str(e)}")

    def create_more(self, bus_case: str, component: Optional[str] = None) -> Tuple[int, Optional[int]]:
        """
        Create a new business case and optionally a component.

        Args:
            bus_case: The business case text
            component: Optional component text

        Returns:
            Tuple of (business case ID, component ID or None)

        Raises:
            MoreValidationError: If input data is invalid
            MoreError: If creation fails
        """
        self._validate_text(bus_case, "Business case")
        if component is not None:
            self._validate_text(component, "Component")

        try:
            with self.pool.get_connection() as conn:
                # Insert business case
                bus_query = "INSERT INTO more_bus_tbl (bus_case) VALUES (?)"
                cursor = conn.cursor()
                cursor.execute(bus_query, (bus_case,))
                bus_id = cursor.lastrowid

                comp_id = None
                if component:
                    # Insert component if provided
                    comp_query = "INSERT INTO more_comp_tbl (more_bus_id, bus_component) VALUES (?, ?)"
                    cursor.execute(comp_query, (bus_id, component))
                    comp_id = cursor.lastrowid

                conn.commit()
                print(f"[DEBUG] Created business case ID: {bus_id}, component ID: {comp_id}")
                return bus_id, comp_id

        except sqlite3.Error as e:
            raise MoreError(f"Database error: {str(e)}")

    def update_more(self, more_bus_id: int, bus_case: Optional[str] = None,
                   component_updates: Optional[Dict[int, str]] = None) -> None:
        """
        Update a business case and/or its components.

        Args:
            more_bus_id: ID of the business case to update
            bus_case: New business case text (optional)
            component_updates: Dictionary of component ID to new text (optional)

        Raises:
            MoreValidationError: If input data is invalid
            MoreNotFoundError: If business case or component is not found
            MoreError: If update fails
        """
        self._validate_ids(bus_id=more_bus_id)

        if bus_case is not None:
            self._validate_text(bus_case, "Business case")

        if component_updates:
            for comp_id, new_text in component_updates.items():
                self._validate_ids(comp_id=comp_id)
                self._validate_text(new_text, "Component")

        try:
            with self.pool.get_connection() as conn:
                cursor = conn.cursor()

                # Update business case if provided
                if bus_case is not None:
                    bus_query = "UPDATE more_bus_tbl SET bus_case = ? WHERE more_bus_id = ?"
                    cursor.execute(bus_query, (bus_case, more_bus_id))
                    if cursor.rowcount == 0:
                        raise MoreNotFoundError(f"Business case with ID {more_bus_id} not found")
                    print(f"[DEBUG] Updated business case ID: {more_bus_id}")

                # Update components if provided
                if component_updates:
                    for comp_id, new_component in component_updates.items():
                        comp_query = """
                            UPDATE more_comp_tbl
                            SET bus_component = ?
                            WHERE more_comp_id = ? AND more_bus_id = ?
                        """
                        cursor.execute(comp_query, (new_component, comp_id, more_bus_id))
                        if cursor.rowcount == 0:
                            raise MoreNotFoundError(
                                f"Component {comp_id} not found for business case {more_bus_id}")
                        print(f"[DEBUG] Updated component ID: {comp_id} for business case ID: {more_bus_id}")

                conn.commit()

        except sqlite3.Error as e:
            raise MoreError(f"Database error: {str(e)}")

    def add_component(self, more_bus_id: int, component: str) -> int:
        """
        Add a new component to an existing business case.

        Args:
            more_bus_id: ID of the business case
            component: Component text

        Returns:
            ID of the created component

        Raises:
            MoreValidationError: If input data is invalid
            MoreNotFoundError: If business case is not found
        """
        self._validate_ids(bus_id=more_bus_id)
        self._validate_text(component, "Component")

        try:
            with self.pool.get_connection() as conn:
                # Verify business case exists
                if not self._business_case_exists(more_bus_id):
                    raise MoreNotFoundError(f"Business case with ID {more_bus_id} not found")

                query = "INSERT INTO more_comp_tbl (more_bus_id, bus_component) VALUES (?, ?)"
                cursor = conn.cursor()
                cursor.execute(query, (more_bus_id, component))
                conn.commit()
                print(f"[DEBUG] Added component ID: {cursor.lastrowid} to business case ID: {more_bus_id}")
                return cursor.lastrowid

        except sqlite3.Error as e:
            raise MoreError(f"Database error: {str(e)}")

    def update_component_business_case(self, comp_id: int, new_bus_id: int) -> None:
        """
        Move a component to a different business case.
        
        Args:
            comp_id: The ID of the component to move
            new_bus_id: The ID of the target business case
            
        Raises:
            MoreValidationError: If input data is invalid
            MoreNotFoundError: If component or business case is not found
        """
        self._validate_ids(comp_id=comp_id, bus_id=new_bus_id)
        
        try:
            with self.pool.get_connection() as conn:
                cursor = conn.cursor()

                # Verify component exists
                cursor.execute("SELECT 1 FROM more_comp_tbl WHERE more_comp_id = ?", (comp_id,))
                if not cursor.fetchone():
                    raise MoreNotFoundError(f"Component with ID {comp_id} not found")

                # Verify new business case exists
                cursor.execute("SELECT 1 FROM more_bus_tbl WHERE more_bus_id = ?", (new_bus_id,))
                if not cursor.fetchone():
                    raise MoreNotFoundError(f"Business case with ID {new_bus_id} not found")

                # Update component's business case
                cursor.execute(
                    "UPDATE more_comp_tbl SET more_bus_id = ? WHERE more_comp_id = ?",
                    (new_bus_id, comp_id)
                )
                conn.commit()
                print(f"[DEBUG] Moved component ID: {comp_id} to business case ID: {new_bus_id}")
                
        except sqlite3.Error as e:
            raise MoreError(f"Database error: {str(e)}")

    def update_component_order(self, bus_id: int, new_order: List[int]) -> None:
        """
        Update the display order of components within a business case.
        
        Args:
            bus_id: The ID of the business case
            new_order: List of component IDs in the new display order
            
        Raises:
            MoreValidationError: If input data is invalid
            MoreNotFoundError: If any component is not found
        """
        self._validate_ids(bus_id=bus_id)
        if not new_order:
            return
            
        try:
            with self.pool.get_connection() as conn:
                # Verify all components belong to this business case
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT more_comp_id
                    FROM more_comp_tbl
                    WHERE more_bus_id = ?
                """, (bus_id,))
                valid_ids = {row[0] for row in cursor.fetchall()}

                # Check if all new_order IDs are valid
                invalid_ids = set(new_order) - valid_ids
                if invalid_ids:
                    raise MoreNotFoundError(
                        f"Component IDs not found in business case {bus_id}: {invalid_ids}"
                    )

                # Update display_order for each component
                for order, comp_id in enumerate(new_order):
                    cursor.execute("""
                        UPDATE more_comp_tbl
                        SET display_order = ?
                        WHERE more_comp_id = ?
                    """, (order, comp_id))

                conn.commit()
                print(f"[DEBUG] Updated component order for business case ID: {bus_id}")

        except sqlite3.Error as e:
            raise MoreError(f"Database error: {str(e)}")

    def delete_more(self, more_bus_id: int) -> int:
        """
        Delete a business case (components will be cascade deleted).

        Args:
            more_bus_id: ID of the business case to delete

        Returns:
            Number of rows affected

        Raises:
            MoreValidationError: If more_bus_id is invalid
            MoreNotFoundError: If business case is not found
        """
        self._validate_ids(bus_id=more_bus_id)

        try:
            with self.pool.get_connection() as conn:
                query = "DELETE FROM more_bus_tbl WHERE more_bus_id = ?"
                cursor = conn.cursor()
                cursor.execute(query, (more_bus_id,))

                if cursor.rowcount == 0:
                    raise MoreNotFoundError(f"Business case with ID {more_bus_id} not found")

                conn.commit()
                print(f"[DEBUG] Deleted business case ID: {more_bus_id}")
                return cursor.rowcount

        except sqlite3.Error as e:
            raise MoreError(f"Database error: {str(e)}")

    def delete_component(self, more_comp_id: int) -> int:
        """
        Delete a specific component.

        Args:
            more_comp_id: ID of the component to delete

        Returns:
            Number of rows affected

        Raises:
            MoreValidationError: If more_comp_id is invalid
            MoreNotFoundError: If component is not found
        """
        self._validate_ids(comp_id=more_comp_id)

        try:
            with self.pool.get_connection() as conn:
                query = "DELETE FROM more_comp_tbl WHERE more_comp_id = ?"
                cursor = conn.cursor()
                cursor.execute(query, (more_comp_id,))

                if cursor.rowcount == 0:
                    raise MoreNotFoundError(f"Component with ID {more_comp_id} not found")

                conn.commit()
                print(f"[DEBUG] Deleted component ID: {more_comp_id}")
                return cursor.rowcount

        except sqlite3.Error as e:
            raise MoreError(f"Database error: {str(e)}")

    def clear_all_business_data(self) -> int:
        """
        Clear all business data by truncating tables in the correct order.

        This method first truncates clipsmore_tbl, then more_coomp_tbl,
        and finally more_bus_tbl to maintain referential integrity.

        Returns:
            int: Total number of rows deleted

        Raises:
            MoreError: If database operation fails
        """
        total_deleted = 0

        try:
            # First truncate clipsmore_tbl (relationships)
            with self.pool.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM clipsmore_tbl")
                conn.commit()
                total_deleted += cursor.rowcount
                print(f"[DEBUG] Cleared clipsmore_tbl, rows deleted: {cursor.rowcount}")

            # Then truncate more_coomp_tbl (components)
            with self.pool.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM more_comp_tbl")
                conn.commit()
                total_deleted += cursor.rowcount
                print(f"[DEBUG] Cleared more_comp_tbl, rows deleted: {cursor.rowcount}")

            # Finally truncate more_bus_tbl (business cases)
            with self.pool.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM more_bus_tbl")
                conn.commit()
                total_deleted += cursor.rowcount
                print(f"[DEBUG] Cleared more_bus_tbl, rows deleted: {cursor.rowcount}")

            print(f"[DEBUG] Total rows deleted across all tables: {total_deleted}")
            return total_deleted

        except sqlite3.Error as e:
            raise MoreError(f"Database error: {str(e)}")

    def get_business_case_id_by_name(self, name: str) -> Optional[int]:
        """
        Get the business case ID for a given business case name.

        Args:
            name: The name of the business case

        Returns:
            The ID of the business case, or None if not found
        """
        print(f'[DEBUG] MoreTableOperations.get_business_case_id_by_name called with name={name}')
        try:
            with self.pool.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT more_bus_id FROM more_bus_tbl WHERE bus_case = ?", (name,))
                row = cursor.fetchone()
                if row:
                    print(f'[DEBUG] Found business case id: {row[0]} for name: {name}')
                    return row[0]
                else:
                    print(f'[ERROR] Business case not found for name: {name}')
                    return None
        except Exception as e:
            print(f'[ERROR] get_business_case_id_by_name: {e}')
            return None

    def get_component_id_by_name(self, name: str) -> Optional[int]:
        """
        Get the component ID for a given component name.

        Args:
            name: The name of the component

        Returns:
            The ID of the component, or None if not found
        """
        print(f'[DEBUG] MoreTableOperations.get_component_id_by_name called with name={name}')
        try:
            with self.pool.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT more_comp_id FROM more_comp_tbl WHERE bus_component = ?", (name,))
                row = cursor.fetchone()
                if row:
                    print(f'[DEBUG] Found component id: {row[0]} for name: {name}')
                    return row[0]
                else:
                    print(f'[ERROR] Component not found for name: {name}')
                    return None
        except Exception as e:
            print(f'[ERROR] get_component_id_by_name: {e}')
            return None

    def get_business_case_id_of_component(self, comp_id: int) -> Optional[int]:
        """
        Get the business case ID for a given component ID.

        Args:
            comp_id: The ID of the component

        Returns:
            The ID of the business case, or None if not found
        """
        print(f'[DEBUG] MoreTableOperations.get_business_case_id_of_component called with comp_id={comp_id}')
        try:
            with self.pool.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT more_bus_id FROM more_comp_tbl WHERE more_comp_id = ?", (comp_id,))
                row = cursor.fetchone()
                if row:
                    print(f'[DEBUG] Found business case id: {row[0]} for component id: {comp_id}')
                    return row[0]
                else:
                    print(f'[ERROR] Business case not found for component id: {comp_id}')
                    return None
        except Exception as e:
            print(f'[ERROR] get_business_case_id_of_component: {e}')
            return None

    def get_component_id_by_name_in_business_case(self, comp_name: str, bus_id: int) -> Optional[int]:
        """
        Get the component ID for a given component name within a specific business case.

        Args:
            comp_name: The name of the component
            bus_id: The ID of the business case

        Returns:
            The ID of the component, or None if not found
        """
        print(f'[DEBUG] MoreTableOperations.get_component_id_by_name_in_business_case called with comp_name={comp_name}, bus_id={bus_id}')
        try:
            with self.pool.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    "SELECT more_comp_id FROM more_comp_tbl WHERE bus_component = ? AND more_bus_id = ?",
                    (comp_name, bus_id)
                )
                row = cursor.fetchone()
                if row:
                    print(f'[DEBUG] Found component id: {row[0]} for name: {comp_name} in business case: {bus_id}')
                    return row[0]
                else:
                    print(f'[ERROR] Component not found for name: {comp_name} in business case: {bus_id}')
                    return None
        except Exception as e:
            print(f'[ERROR] get_component_id_by_name_in_business_case: {e}')
            return None

    def read_components_for_business_case(self, more_bus_id: int) -> List[Dict[str, Any]]:
        """
        Read components for a specific business case with UI-compatible format.

        Args:
            more_bus_id: ID of the business case

        Returns:
            List of dictionaries with 'id' and 'name' keys for UI compatibility

        Raises:
            MoreValidationError: If more_bus_id is invalid
            MoreNotFoundError: If business case is not found
        """
        print(f'[DEBUG] MoreTableOperations.read_components_for_business_case called with more_bus_id={more_bus_id}')
        components = self.read_components(more_bus_id)
        # Convert to UI-compatible format
        return [{'id': comp['more_comp_id'], 'name': comp['bus_component']} for comp in components]

    def clear_all_business_cases(self) -> int:
        """Remove all business cases from the database.

        Returns:
            int: Number of business cases deleted
        """
        print('[DEBUG] MoreTableOperations.clear_all_business_cases called')
        try:
            with self.pool.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM more_bus_tbl")
                deleted_count = cursor.rowcount
                conn.commit()
                print(f'[DEBUG] Cleared all business cases, deleted: {deleted_count}')
                return deleted_count
        except sqlite3.Error as e:
            raise MoreError(f"Database error: {str(e)}") from e

    def clear_all_components(self) -> int:
        """Remove all components from the database.

        Returns:
            int: Number of components deleted
        """
        print('[DEBUG] MoreTableOperations.clear_all_components called')
        try:
            with self.pool.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM more_comp_tbl")
                deleted_count = cursor.rowcount
                conn.commit()
                print(f'[DEBUG] Cleared all components, deleted: {deleted_count}')
                return deleted_count
        except sqlite3.Error as e:
            raise MoreError(f"Database error: {str(e)}") from e

# NOTE: All new code should include debug print statements at the start of every function/method.