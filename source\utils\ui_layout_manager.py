"""
UI Layout Manager for ClipsMore Application

This manager handles all UI layout creation and management including:
- Main application layout structure
- Tab control creation and management
- Theme toggle button creation
- Responsive layout management
- Layout theme updates

Author: ClipsMore Development Team
Version: 2.0
"""

import tkinter as tk
from tkinter import ttk
from typing import Tuple, Dict, Any, Optional

# NOTE: All new code should include debug print statements at the start of every function/method.

class UILayoutManager:
    """
    Manages the main UI layout structure for the ClipsMore application.
    
    Handles creation and management of:
    - Main application frames
    - Tab control and tabs
    - Theme toggle button
    - Responsive layout behavior
    - Layout theme updates
    """
    
    def __init__(self, root: tk.Tk, theme_manager):
        """
        Initialize UILayoutManager with root window and theme manager.
        
        Args:
            root: Main tkinter root window
            theme_manager: ThemeManager instance for styling
        """
        print('[DEBUG] UILayoutManager.__init__ called')
        
        self.root = root
        self.theme_manager = theme_manager
        
        # Layout components (will be created by create_main_layout)
        self.main_frame = None
        self.top_frame = None
        self.tab_control = None
        self.theme_button = None
        self.undo_button = None
        self.clips_tab = None
        self.more_tab = None
        self.about_tab = None
        
        print('[DEBUG] UILayoutManager initialized successfully')
    
    def create_main_layout(self) -> Dict[str, Any]:
        """
        Create the main application layout structure.
        
        Returns:
            Dict containing references to created layout components
        """
        print('[DEBUG] UILayoutManager.create_main_layout called')
        
        try:
            # Get current theme colors
            colors = self.theme_manager.get_theme_colors()
            
            # Create main frame with theme toggle at top
            self.main_frame = tk.Frame(self.root, bg=colors['bg_color'])
            self.main_frame.pack(fill=tk.BOTH, expand=True)
            
            # Create top frame for theme toggle
            self.top_frame = tk.Frame(self.main_frame, bg=colors['bg_color'], height=40)
            self.top_frame.pack(fill=tk.X, padx=10, pady=5)
            self.top_frame.pack_propagate(False)
            
            print('[DEBUG] Main layout frames created successfully')
            
            # Return layout components
            layout_components = {
                'main_frame': self.main_frame,
                'top_frame': self.top_frame
            }
            
            return layout_components
            
        except Exception as e:
            print(f'[ERROR] Failed to create main layout: {e}')
            raise
    
    def create_tab_control(self) -> Tuple[ttk.Notebook, Dict[str, ttk.Frame]]:
        """
        Create the tab control and tab frames.
        
        Returns:
            Tuple of (tab_control, tabs_dict) where tabs_dict contains tab references
        """
        print('[DEBUG] UILayoutManager.create_tab_control called')
        
        try:
            if not self.main_frame:
                raise ValueError("Main frame must be created before tab control")
            
            # Create tab control
            self.tab_control = ttk.Notebook(self.main_frame)
            
            # Create tab frames
            self.clips_tab = ttk.Frame(self.tab_control)
            self.more_tab = ttk.Frame(self.tab_control)
            self.about_tab = ttk.Frame(self.tab_control)
            
            # Add tabs to notebook
            self.tab_control.add(self.clips_tab, text='Clips')
            self.tab_control.add(self.more_tab, text='More')
            self.tab_control.add(self.about_tab, text='About')
            
            # Pack tab control
            self.tab_control.pack(expand=1, fill="both")
            
            print('[DEBUG] Tab control and tabs created successfully')
            
            # Create tabs dictionary
            tabs = {
                'clips_tab': self.clips_tab,
                'more_tab': self.more_tab,
                'about_tab': self.about_tab
            }
            
            return self.tab_control, tabs
            
        except Exception as e:
            print(f'[ERROR] Failed to create tab control: {e}')
            raise
    
    def create_theme_toggle(self, toggle_command) -> tk.Button:
        """
        Create the theme toggle button.

        Args:
            toggle_command: Function to call when theme button is clicked

        Returns:
            Created theme toggle button
        """
        print('[DEBUG] UILayoutManager.create_theme_toggle called')

        try:
            if not self.top_frame:
                raise ValueError("Top frame must be created before theme toggle")

            # Get current theme colors
            colors = self.theme_manager.get_theme_colors()

            # Determine button text based on current theme
            button_text = "☀️ Light Mode" if colors['dark_mode'] else "🌙 Dark Mode"

            # Create theme toggle button
            self.theme_button = tk.Button(
                self.top_frame,
                text=button_text,
                command=toggle_command,
                bg=colors['button_bg'],
                fg=colors['button_fg'],
                font=("Arial", 10, "bold"),
                relief=tk.RAISED,
                bd=2
            )
            self.theme_button.pack(side=tk.RIGHT, padx=5, pady=5)

            print('[DEBUG] Theme toggle button created successfully')

            return self.theme_button

        except Exception as e:
            print(f'[ERROR] Failed to create theme toggle: {e}')
            raise

    def create_undo_button(self, undo_command) -> tk.Button:
        """
        Create the undo button.

        Args:
            undo_command: Function to call when undo button is clicked

        Returns:
            Created undo button
        """
        print('[DEBUG] UILayoutManager.create_undo_button called')

        try:
            if not self.top_frame:
                raise ValueError("Top frame must be created before undo button")

            # Get current theme colors
            colors = self.theme_manager.get_theme_colors()

            # Create undo button
            self.undo_button = tk.Button(
                self.top_frame,
                text="↶ Undo (Ctrl+Z)",
                command=undo_command,
                bg=colors['button_bg'],
                fg=colors['button_fg'],
                font=("Arial", 10, "bold"),
                relief=tk.RAISED,
                bd=2,
                state=tk.DISABLED  # Initially disabled until there are actions to undo
            )
            self.undo_button.pack(side=tk.LEFT, padx=5, pady=5)

            print('[DEBUG] Undo button created successfully')

            return self.undo_button

        except Exception as e:
            print(f'[ERROR] Failed to create undo button: {e}')
            raise
    
    def setup_responsive_layout(self):
        """
        Setup responsive layout behavior for the application.
        """
        print('[DEBUG] UILayoutManager.setup_responsive_layout called')
        
        try:
            # Configure root window properties
            self.root.title("ClipsMore")
            self.root.geometry("1000x700")
            
            # Configure main frame to be responsive
            if self.main_frame:
                self.main_frame.grid_rowconfigure(0, weight=1)
                self.main_frame.grid_columnconfigure(0, weight=1)
            
            print('[DEBUG] Responsive layout setup completed')
            
        except Exception as e:
            print(f'[ERROR] Failed to setup responsive layout: {e}')
            raise
    
    def apply_layout_theme(self, theme_colors: Dict[str, Any]):
        """
        Apply theme colors to layout components.
        
        Args:
            theme_colors: Dictionary of theme colors from ThemeManager
        """
        print('[DEBUG] UILayoutManager.apply_layout_theme called')
        
        try:
            # Update main frame colors
            if self.main_frame:
                self.main_frame.config(bg=theme_colors['bg_color'])
            
            # Update top frame colors
            if self.top_frame:
                self.top_frame.config(bg=theme_colors['bg_color'])
            
            # Update theme button
            if self.theme_button:
                button_text = "☀️ Light Mode" if theme_colors['dark_mode'] else "🌙 Dark Mode"
                self.theme_button.config(
                    text=button_text,
                    bg=theme_colors['button_bg'],
                    fg=theme_colors['button_fg']
                )

            # Update undo button
            if self.undo_button:
                self.undo_button.config(
                    bg=theme_colors['button_bg'],
                    fg=theme_colors['button_fg']
                )
            
            print('[DEBUG] Layout theme applied successfully')
            
        except Exception as e:
            print(f'[ERROR] Failed to apply layout theme: {e}')
            raise
    
    def get_layout_components(self) -> Dict[str, Any]:
        """
        Get references to all layout components.
        
        Returns:
            Dictionary containing all layout component references
        """
        print('[DEBUG] UILayoutManager.get_layout_components called')
        
        return {
            'main_frame': self.main_frame,
            'top_frame': self.top_frame,
            'tab_control': self.tab_control,
            'theme_button': self.theme_button,
            'undo_button': self.undo_button,
            'clips_tab': self.clips_tab,
            'more_tab': self.more_tab,
            'about_tab': self.about_tab
        }
    
    def update_tab_colors(self, tabs: Dict[str, ttk.Frame]):
        """
        Update colors for all tab content using ThemeManager.
        
        Args:
            tabs: Dictionary of tab frames to update
        """
        print('[DEBUG] UILayoutManager.update_tab_colors called')
        
        try:
            for tab_name, tab_frame in tabs.items():
                if tab_frame:
                    self.theme_manager.update_widget_colors(tab_frame)
                    print(f'[DEBUG] Updated colors for {tab_name}')
            
            print('[DEBUG] All tab colors updated successfully')
            
        except Exception as e:
            print(f'[ERROR] Failed to update tab colors: {e}')
            raise

# NOTE: All new code should include debug print statements at the start of every function/method.
