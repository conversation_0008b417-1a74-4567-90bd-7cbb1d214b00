import tkinter as tk

class UIClipboardUtility:
    """Utility class for clipboard operations extracted from UIManager"""
    
    def __init__(self, root, database_manager, dialog_manager=None):
        print('[DEBUG] UIClipboardUtility.__init__ called')
        self.root = root
        self.database_manager = database_manager
        self.dialog_manager = dialog_manager
    
    def copy_clip_by_alias(self, alias):
        """Copy clip content to clipboard by alias"""
        print(f'[DEBUG] UIClipboardUtility.copy_clip_by_alias called with alias={alias}')
        
        try:
            # Get clip content by alias using database manager
            content = self.database_manager.get_clip_content_by_alias(alias)

            if content:
                self.root.clipboard_clear()
                self.root.clipboard_append(content)
                print(f'[DEBUG] Copied content for alias: {alias}')

                # Show success notification
                message = f"Content for '{alias}' copied to clipboard!"
                if self.dialog_manager:
                    self.dialog_manager.show_success_popup(message)
                else:
                    self._show_fallback_popup(message, '#4CAF50')
            else:
                print(f'[WARNING] No content found for alias: {alias}')
                self._show_warning("No content found", f"No content found for alias: {alias}")
                    
        except Exception as e:
            print(f'[ERROR] Failed to copy clip by alias: {e}')
            error_message = f"Failed to copy clip: {e}"
            if self.dialog_manager:
                self.dialog_manager.show_error_popup(error_message)
            else:
                self._show_fallback_popup(error_message, '#d32f2f')
    
    def copy_clip_to_clipboard(self, clip_id):
        """Copy clip content to OS clipboard using clip ID"""
        print(f'[DEBUG] UIClipboardUtility.copy_clip_to_clipboard called for clip_id={clip_id}')
        
        try:
            # Get clip content by ID using view operations
            view_ops = self.database_manager.get_view_operations()
            results = view_ops.read_clipsmore_view()

            # Find the matching clip_id
            for result in results:
                if result.get('clip_id') == clip_id:
                    content = result.get('clip')
                    alias = result.get('alias')

                    if content:
                        self.root.clipboard_clear()
                        self.root.clipboard_append(content)
                        print(f'[DEBUG] Copied content for clip_id: {clip_id}')

                        # Show success notification
                        display_name = alias if alias else f"Clip {clip_id}"
                        message = f"Content for '{display_name}' copied to clipboard!"
                        if self.dialog_manager:
                            self.dialog_manager.show_success_popup(message)
                        else:
                            self._show_fallback_popup(message, '#4CAF50')
                        return
                    else:
                        print(f'[WARNING] No content found for clip_id: {clip_id}')
                        self._show_warning("No content found", f"No content found for clip ID: {clip_id}")
                        return

            # If we get here, clip was not found
            print(f'[WARNING] Clip not found: {clip_id}')
            self._show_warning("Clip not found", f"Clip not found: {clip_id}")
                    
        except Exception as e:
            print(f'[ERROR] Failed to copy clip to clipboard: {e}')
            error_message = f"Failed to copy clip: {e}"
            if self.dialog_manager:
                self.dialog_manager.show_error_popup(error_message)
            else:
                self._show_fallback_popup(error_message, '#d32f2f')
    
    def get_clipboard_content(self):
        """Get current clipboard content"""
        print('[DEBUG] UIClipboardUtility.get_clipboard_content called')
        
        try:
            return self.root.clipboard_get()
        except tk.TclError:
            print('[DEBUG] Clipboard is empty or contains non-text data')
            return ""
        except Exception as e:
            print(f'[ERROR] Failed to get clipboard content: {e}')
            return ""
    
    def set_clipboard_content(self, content):
        """Set clipboard content"""
        print(f'[DEBUG] UIClipboardUtility.set_clipboard_content called with {len(content)} characters')
        
        try:
            self.root.clipboard_clear()
            self.root.clipboard_append(content)
            print('[DEBUG] Clipboard content set successfully')
            return True
        except Exception as e:
            print(f'[ERROR] Failed to set clipboard content: {e}')
            return False
    
    def clear_clipboard(self):
        """Clear clipboard content"""
        print('[DEBUG] UIClipboardUtility.clear_clipboard called')
        
        try:
            self.root.clipboard_clear()
            print('[DEBUG] Clipboard cleared successfully')
            return True
        except Exception as e:
            print(f'[ERROR] Failed to clear clipboard: {e}')
            return False
    
    def _show_warning(self, title, message):
        """Show warning message"""
        if self.dialog_manager:
            self.dialog_manager.show_warning_popup(message)
        else:
            self._show_fallback_popup(message, '#ff9800')
    
    def _show_fallback_popup(self, message, bg_color):
        """Show fallback popup when dialog manager is not available"""
        try:
            popup = tk.Toplevel(self.root)
            popup.overrideredirect(True)
            popup.attributes('-topmost', True)
            popup.configure(bg=bg_color)
            
            label = tk.Label(
                popup, 
                text=message, 
                bg=bg_color, 
                fg='white', 
                font=('Arial', 10, 'bold')
            )
            label.pack(ipadx=20, ipady=10)
            
            # Position popup in the center of the main window
            self.root.update_idletasks()
            x = self.root.winfo_x() + (self.root.winfo_width() // 2) - (popup.winfo_reqwidth() // 2)
            y = self.root.winfo_y() + (self.root.winfo_height() // 2) - (popup.winfo_reqheight() // 2)
            popup.geometry(f'+{x}+{y}')
            
            # Auto-dismiss after 2 seconds
            popup.after(2000, popup.destroy)
            
        except Exception as e:
            print(f'[ERROR] Failed to show fallback popup: {e}')
