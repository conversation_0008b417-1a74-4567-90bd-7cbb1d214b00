import tkinter as tk
from tkinter import messagebox, simpledialog

class UIDialogManager:
    """Manager class for dialog operations extracted from UIManager"""
    
    def __init__(self, root, database_manager, theme_manager, notification_manager=None):
        print('[DEBUG] UIDialogManager.__init__ called')
        self.root = root
        self.database_manager = database_manager
        self.theme_manager = theme_manager
        self.notification_manager = notification_manager
    
    def open_export_dialog(self):
        """Open the export dialog"""
        print('[DEBUG] UIDialogManager.open_export_dialog called')
        
        try:
            from export.export_dialog import ExportDialog
            
            dialog = ExportDialog(
                parent=self.root,
                database_manager=self.database_manager,
                theme_manager=self.theme_manager
            )
            
        except Exception as e:
            print(f'[ERROR] Failed to open export dialog: {e}')
            if self.notification_manager:
                self.notification_manager.show_error(f"Export Error: {e}")
    
    def open_backup_dialog(self):
        """Open the backup dialog"""
        print('[DEBUG] UIDialogManager.open_backup_dialog called')
        
        try:
            from backup.backup_dialog import BackupDialog
            
            dialog = BackupDialog(
                parent=self.root,
                database_manager=self.database_manager,
                theme_manager=self.theme_manager
            )
            
        except Exception as e:
            print(f'[ERROR] Failed to open backup dialog: {e}')
            if self.notification_manager:
                self.notification_manager.show_error(f"Backup Error: {e}")
    
    def show_clear_all_data_confirmation(self):
        """Show confirmation dialog for clearing all data"""
        print('[DEBUG] UIDialogManager.show_clear_all_data_confirmation called')
        
        try:
            # First confirmation - general warning
            result1 = messagebox.askyesno(
                "⚠️ DANGER: Clear All Data",
                "🚨 WARNING: This will permanently delete ALL data including:\n\n"
                "• All clips and clipboard content\n"
                "• All business cases and components\n"
                "• All assignments and relationships\n"
                "• All export templates\n"
                "• All backup and import history\n\n"
                "This action CANNOT be undone!\n\n"
                "Are you absolutely sure you want to continue?",
                icon='warning'
            )
            
            if not result1:
                print('[DEBUG] User cancelled clear all data operation')
                return False
            
            # Second confirmation - type confirmation
            result2 = messagebox.askyesno(
                "🔴 FINAL WARNING",
                "This is your FINAL WARNING!\n\n"
                "You are about to PERMANENTLY DELETE ALL DATA.\n"
                "The entire database will be emptied.\n\n"
                "Type 'Y' in the next dialog to confirm.",
                icon='error'
            )
            
            if not result2:
                print('[DEBUG] User cancelled at final warning')
                return False
            
            # Third confirmation - text input
            confirmation_text = simpledialog.askstring(
                "Type Confirmation",
                "Type 'Y' to confirm:\n(case sensitive)",
                show='*'
            )
            
            if confirmation_text != "Y":
                messagebox.showinfo("Cancelled", "Operation cancelled - confirmation text did not match.")
                print('[DEBUG] User provided incorrect confirmation text')
                return False
            
            return True
            
        except Exception as e:
            print(f'[ERROR] Exception in clear all data confirmation: {e}')
            messagebox.showerror("Error", f"An error occurred: {str(e)}")
            return False
    
    def show_clear_all_data_result(self, success):
        """Show result dialog for clear all data operation"""
        print(f'[DEBUG] UIDialogManager.show_clear_all_data_result called with success={success}')
        
        try:
            if success:
                messagebox.showinfo(
                    "✅ Complete",
                    "All data has been successfully deleted.\n"
                    "The database is now empty."
                )
                print('[DEBUG] All data cleared successfully')
            else:
                messagebox.showerror(
                    "❌ Error",
                    "Failed to clear all data.\n"
                    "Please check the logs for details."
                )
                print('[ERROR] Failed to clear all data')
                
        except Exception as e:
            print(f'[ERROR] Exception showing clear all data result: {e}')
    
    def show_popup_notification(self, message, bg_color='#4CAF50', fg_color='white', duration=2000):
        """Show a non-blocking popup notification"""
        print(f'[DEBUG] UIDialogManager.show_popup_notification called: {message}')
        
        try:
            popup = tk.Toplevel(self.root)
            popup.overrideredirect(True)
            popup.attributes('-topmost', True)
            popup.configure(bg=bg_color)
            
            label = tk.Label(
                popup, 
                text=message, 
                bg=bg_color, 
                fg=fg_color, 
                font=('Arial', 10, 'bold')
            )
            label.pack(ipadx=20, ipady=10)
            
            # Position popup in the center of the main window
            self.root.update_idletasks()
            x = self.root.winfo_x() + (self.root.winfo_width() // 2) - (popup.winfo_reqwidth() // 2)
            y = self.root.winfo_y() + (self.root.winfo_height() // 2) - (popup.winfo_reqheight() // 2)
            popup.geometry(f'+{x}+{y}')
            
            # Auto-dismiss after duration
            popup.after(duration, popup.destroy)
            
            return popup
            
        except Exception as e:
            print(f'[ERROR] Failed to show popup notification: {e}')
            return None
    
    def show_success_popup(self, message, duration=2000):
        """Show a success popup notification"""
        return self.show_popup_notification(message, '#4CAF50', 'white', duration)
    
    def show_error_popup(self, message, duration=2000):
        """Show an error popup notification"""
        return self.show_popup_notification(message, '#d32f2f', 'white', duration)
    
    def show_warning_popup(self, message, duration=2000):
        """Show a warning popup notification"""
        return self.show_popup_notification(message, '#ff9800', 'white', duration)
