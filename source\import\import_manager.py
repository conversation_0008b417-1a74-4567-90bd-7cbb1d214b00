#!/usr/bin/env python3
"""
Import Manager for ClipsMore Application
Handles importing data from various external sources and formats.
"""

import os
import json
import sqlite3
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime

from DB.db_connection import ConnectionPoolManager
from utils.alias_generator import AliasGenerator


class ImportError(Exception):
    """Custom exception for import-related errors."""
    pass


class ImportConfig:
    """Configuration class for import operations."""
    
    def __init__(self):
        print('[DEBUG] ImportConfig.__init__ called')
        self.duplicate_strategy = 'skip'  # skip, replace, merge
        self.field_mapping = {}
        self.validation_rules = {}
        self.batch_size = 1000
        self.auto_generate_aliases = True
        self.preserve_timestamps = True
        self.rollback_on_error = True


class ImportManager:
    """
    Core import manager for ClipsMore application.
    Handles import operations from various external sources.
    """
    
    def __init__(self, database_manager=None):
        """Initialize the import manager."""
        print('[DEBUG] ImportManager.__init__ called')
        self.database_manager = database_manager
        self.connection_pool = ConnectionPoolManager()
        self.format_parsers = {}
        self.progress_callback = None
        self.cancel_requested = False
        self.alias_generator = AliasGenerator()
        
        # Initialize format parsers
        self._initialize_format_parsers()
    
    def _initialize_format_parsers(self):
        """Initialize available format parsers."""
        print('[DEBUG] ImportManager._initialize_format_parsers called')

        # Import parsers dynamically to avoid keyword conflicts
        import importlib.util
        import os

        # Load JSON parser
        json_parser_path = os.path.join(os.path.dirname(__file__), 'format_parsers', 'json_parser.py')
        spec = importlib.util.spec_from_file_location("json_parser", json_parser_path)
        json_parser_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(json_parser_module)

        # Load CSV parser
        csv_parser_path = os.path.join(os.path.dirname(__file__), 'format_parsers', 'csv_parser.py')
        spec = importlib.util.spec_from_file_location("csv_parser", csv_parser_path)
        csv_parser_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(csv_parser_module)

        # Register format parsers
        self.register_format_parser('json', json_parser_module.JSONParser())
        self.register_format_parser('csv', csv_parser_module.CSVParser())
    
    def register_format_parser(self, format_type: str, parser):
        """Register a format parser for a specific format."""
        print(f'[DEBUG] ImportManager.register_format_parser called for {format_type}')
        self.format_parsers[format_type] = parser
    
    def get_available_formats(self) -> List[str]:
        """Get list of available import formats."""
        print('[DEBUG] ImportManager.get_available_formats called')
        return list(self.format_parsers.keys())
    
    def set_progress_callback(self, callback):
        """Set callback function for progress updates."""
        print('[DEBUG] ImportManager.set_progress_callback called')
        self.progress_callback = callback
    
    def _update_progress(self, percentage: int, message: str = ""):
        """Update progress if callback is set."""
        if self.progress_callback:
            self.progress_callback(percentage, message)
    
    def _check_cancellation(self):
        """Check if import operation was cancelled."""
        if self.cancel_requested:
            raise ImportError("Import operation was cancelled by user")
    
    def cancel_import(self):
        """Cancel the current import operation."""
        print('[DEBUG] ImportManager.cancel_import called')
        self.cancel_requested = True
    
    def detect_format(self, file_path: str) -> str:
        """
        Detect the format of the import file.
        
        Args:
            file_path: Path to the file to analyze
            
        Returns:
            Detected format type
        """
        print(f'[DEBUG] ImportManager.detect_format called for {file_path}')
        
        file_path = Path(file_path)
        extension = file_path.suffix.lower()
        
        # Simple extension-based detection
        format_map = {
            '.json': 'json',
            '.csv': 'csv',
            '.txt': 'generic',
            '.xml': 'xml'
        }
        
        detected_format = format_map.get(extension, 'generic')
        print(f'[DEBUG] Detected format: {detected_format}')
        return detected_format
    
    def validate_import_file(self, file_path: str) -> Tuple[bool, str]:
        """
        Validate that the import file exists and is readable.
        
        Args:
            file_path: Path to the file to validate
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        print(f'[DEBUG] ImportManager.validate_import_file called for {file_path}')
        
        try:
            file_path = Path(file_path)
            
            if not file_path.exists():
                return False, f"File does not exist: {file_path}"
            
            if not file_path.is_file():
                return False, f"Path is not a file: {file_path}"
            
            if file_path.stat().st_size == 0:
                return False, f"File is empty: {file_path}"
            
            # Try to read the file
            with open(file_path, 'r', encoding='utf-8') as f:
                f.read(1024)  # Read first 1KB to check readability
            
            print('[DEBUG] File validation successful')
            return True, ""
            
        except PermissionError:
            return False, f"Permission denied reading file: {file_path}"
        except UnicodeDecodeError:
            return False, f"File encoding not supported: {file_path}"
        except Exception as e:
            return False, f"Error validating file: {e}"
    
    def preview_import(self, file_path: str, config: ImportConfig = None) -> Dict[str, Any]:
        """
        Preview import data without actually importing.
        
        Args:
            file_path: Path to the file to preview
            config: Import configuration
            
        Returns:
            Dictionary containing preview information
        """
        print(f'[DEBUG] ImportManager.preview_import called for {file_path}')
        
        if config is None:
            config = ImportConfig()
        
        # Validate file
        is_valid, error_msg = self.validate_import_file(file_path)
        if not is_valid:
            raise ImportError(error_msg)
        
        # Detect format
        format_type = self.detect_format(file_path)
        
        if format_type not in self.format_parsers:
            raise ImportError(f"Unsupported format: {format_type}")
        
        # Parse preview data
        parser = self.format_parsers[format_type]
        preview_data = parser.preview(file_path, config)
        
        return {
            'format': format_type,
            'file_size': Path(file_path).stat().st_size,
            'estimated_records': preview_data.get('record_count', 0),
            'sample_data': preview_data.get('sample_records', []),
            'field_mapping': preview_data.get('detected_fields', {}),
            'potential_duplicates': preview_data.get('duplicate_count', 0),
            'validation_warnings': preview_data.get('warnings', [])
        }
    
    def import_data(self, file_path: str, config: ImportConfig = None) -> Dict[str, Any]:
        """
        Import data from external file.
        
        Args:
            file_path: Path to the file to import
            config: Import configuration
            
        Returns:
            Dictionary containing import results
        """
        print(f'[DEBUG] ImportManager.import_data called for {file_path}')
        
        if config is None:
            config = ImportConfig()
        
        self.cancel_requested = False
        import_id = None
        
        try:
            self._update_progress(5, "Validating import file...")
            
            # Validate file
            is_valid, error_msg = self.validate_import_file(file_path)
            if not is_valid:
                raise ImportError(error_msg)
            
            self._check_cancellation()
            self._update_progress(10, "Detecting file format...")
            
            # Detect format
            format_type = self.detect_format(file_path)
            
            if format_type not in self.format_parsers:
                raise ImportError(f"Unsupported format: {format_type}")
            
            self._check_cancellation()
            self._update_progress(15, "Creating import record...")
            
            # Create import history record
            import_id = self._create_import_record(file_path, format_type, config)
            
            self._check_cancellation()
            self._update_progress(20, "Parsing import data...")
            
            # Parse data
            parser = self.format_parsers[format_type]
            parsed_data = parser.parse(file_path, config)
            
            self._check_cancellation()
            self._update_progress(40, "Processing import data...")
            
            # Process and import data
            import_results = self._process_import_data(parsed_data, config, import_id)
            
            self._check_cancellation()
            self._update_progress(90, "Finalizing import...")
            
            # Update import record with results
            self._update_import_record(import_id, import_results, 'completed')
            
            self._update_progress(100, "Import completed successfully")
            
            return import_results
            
        except ImportError:
            if import_id:
                self._update_import_record(import_id, {}, 'failed')
            raise
        except Exception as e:
            if import_id:
                self._update_import_record(import_id, {}, 'failed', str(e))
            raise ImportError(f"Unexpected error during import: {e}")
    
    def _create_import_record(self, file_path: str, format_type: str, config: ImportConfig) -> int:
        """Create initial import history record."""
        print('[DEBUG] ImportManager._create_import_record called')
        
        try:
            with self.connection_pool.get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    INSERT INTO import_history (
                        source_file, source_format, import_status, import_config
                    ) VALUES (?, ?, ?, ?)
                """, (
                    file_path,
                    format_type,
                    'in_progress',
                    json.dumps(config.__dict__)
                ))
                
                import_id = cursor.lastrowid
                conn.commit()
                
                print(f'[DEBUG] Created import record with ID: {import_id}')
                return import_id
                
        except sqlite3.Error as e:
            raise ImportError(f"Database error creating import record: {e}")
    
    def _update_import_record(self, import_id: int, results: Dict[str, Any], 
                            status: str, error_log: str = None):
        """Update import history record with results."""
        print(f'[DEBUG] ImportManager._update_import_record called for ID {import_id}')
        
        try:
            with self.connection_pool.get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    UPDATE import_history SET
                        records_imported = ?,
                        records_skipped = ?,
                        duplicates_found = ?,
                        import_status = ?,
                        completed_date = CURRENT_TIMESTAMP,
                        error_log = ?,
                        import_summary = ?
                    WHERE import_id = ?
                """, (
                    results.get('records_imported', 0),
                    results.get('records_skipped', 0),
                    results.get('duplicates_found', 0),
                    status,
                    error_log,
                    json.dumps(results),
                    import_id
                ))
                
                conn.commit()
                print('[DEBUG] Import record updated successfully')
                
        except sqlite3.Error as e:
            print(f'[ERROR] Database error updating import record: {e}')
    
    def _process_import_data(self, parsed_data: List[Dict[str, Any]], 
                           config: ImportConfig, import_id: int) -> Dict[str, Any]:
        """Process parsed data and import into database."""
        print('[DEBUG] ImportManager._process_import_data called')
        
        # This is a placeholder for the actual import processing
        # In a real implementation, this would:
        # 1. Detect duplicates
        # 2. Apply field mapping
        # 3. Validate data
        # 4. Insert into database
        # 5. Handle errors and rollback if needed
        
        results = {
            'records_imported': len(parsed_data),
            'records_skipped': 0,
            'duplicates_found': 0,
            'errors': []
        }
        
        return results
    
    def get_import_history(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get import history records."""
        print('[DEBUG] ImportManager.get_import_history called')
        
        try:
            with self.connection_pool.get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT import_id, source_file, source_format, records_imported,
                           records_skipped, duplicates_found, import_status,
                           import_date, completed_date, import_summary
                    FROM import_history
                    ORDER BY import_date DESC
                    LIMIT ?
                """, (limit,))
                
                columns = [desc[0] for desc in cursor.description]
                history = []
                
                for row in cursor.fetchall():
                    record = dict(zip(columns, row))
                    history.append(record)
                
                return history
                
        except sqlite3.Error as e:
            raise ImportError(f"Database error retrieving import history: {e}")
