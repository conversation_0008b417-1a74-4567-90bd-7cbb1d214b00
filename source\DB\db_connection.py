from __future__ import annotations
import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import sqlite3
from contextlib import contextmanager
from pathlib import Path
from typing import Iterator, Generator, Optional
import logging
from queue import Queue
import time
from threading import Lock

class ConnectionPoolManager:
    """Thread-safe SQLite connection pool with true connection reuse.
    
    Implements Singleton pattern using thread-safe initialization.
    """
    _instance: Optional[ConnectionPoolManager] = None
    _lock: Lock = Lock()
    DEFAULT_DB_PATH: Path = Path(__file__).parent / 'clipsmore_db.db'
    MIN_POOL_SIZE: int = 2
    MAX_POOL_SIZE: int = 10

    def __new__(cls, db_path=None):
        if not hasattr(cls, '_instance') or cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance.db_path = db_path or str(cls.DEFAULT_DB_PATH)
            cls._instance._initialize_pool()
        elif db_path is not None and db_path != cls._instance.db_path:
            # If a different database path is requested, reinitialize
            cls._instance.close_all()
            cls._instance.db_path = db_path
            cls._instance._initialize_pool()
        return cls._instance

    def __init__(self, db_path=None):
        self.db_path = db_path or str(self.DEFAULT_DB_PATH)
        print('[DEBUG] ConnectionPoolManager.__init__ called')

    def _initialize_pool(self) -> None:
        """Create initial connection pool with MIN_POOL_SIZE connections."""
        print('[DEBUG] ConnectionPoolManager._initialize_pool called')
        self._pool: Queue[sqlite3.Connection] = Queue(maxsize=self.MAX_POOL_SIZE)
        self._in_use_connections: set[sqlite3.Connection] = set()
        self._pool_lock: Lock = Lock()

        for _ in range(self.MIN_POOL_SIZE):
            conn = self._create_connection()
            self._pool.put(conn)
        print('[DEBUG] Connection pool initialized')

    def _create_connection(self, db_path=None):
        print(f'[DEBUG] ConnectionPoolManager._create_connection called')
        db_path = db_path or self.db_path
        print(f'[DEBUG] Using database file path: {db_path}')
        if db_path and str(db_path).startswith('file:'):
            conn = sqlite3.connect(db_path, uri=True)
        else:
            conn = sqlite3.connect(db_path)
        # Always enable foreign key support
        conn.execute('PRAGMA foreign_keys = ON;')
        print(f'[DEBUG] Connection created: {conn}')
        return conn

    @contextmanager
    def get_connection(self, timeout=5) -> Generator[sqlite3.Connection, None, None]:
        """
        Acquire connection from pool with automatic return.

        Args:
            timeout (int): Maximum wait time in seconds for a connection

        Yields:
            sqlite3.Connection: A database connection

        Raises:
            TimeoutError: If no connection becomes available within timeout
            sqlite3.Error: For database operation errors
        """
        print('[DEBUG] ConnectionPoolManager.get_connection called')
        print(f'[DEBUG] get_connection using database file path: {self.db_path}')
        conn = None
        start_time = time.time()

        while not conn and time.time() - start_time < timeout:
            with self._pool_lock:
                if not self._pool.empty():
                    conn = self._pool.get()
                elif len(self._in_use_connections) < self.MAX_POOL_SIZE:
                    conn = self._create_connection()
            
            if not conn:
                time.sleep(0.1)  # Avoid busy waiting
        
        if not conn:
            raise TimeoutError("Database connection not available")

        with self._pool_lock:
            self._in_use_connections.add(conn)
        print(f'[DEBUG] Connection acquired: {conn}')

        try:
            # Validate connection before returning
            conn.execute("SELECT 1")
            yield conn
            conn.commit()
        except sqlite3.Error as e:
            logging.error(f"Database error: {e}")
            conn.rollback()
            raise
        finally:
            with self._pool_lock:
                self._in_use_connections.remove(conn)
                if self._pool.qsize() < self.MIN_POOL_SIZE:
                    self._pool.put(conn)
                else:
                    conn.close()
            print(f'[DEBUG] Connection released: {conn}')

    def close_all(self) -> None:
        """Close all connections in the pool."""
        print('[DEBUG] ConnectionPoolManager.close_all called')
        with self._pool_lock:
            while not self._pool.empty():
                conn = self._pool.get()
                conn.close()
            self._in_use_connections.clear()
        print('[DEBUG] All connections closed')