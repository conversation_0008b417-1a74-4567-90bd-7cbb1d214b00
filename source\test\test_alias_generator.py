#!/usr/bin/env python3
"""
Test suite for AliasGenerator utility
Tests advanced alias generation algorithms and validation.
"""

import unittest
import sys
import os

# Add source directory to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from utils.alias_generator import AliasGenerator

# NOTE: All new code should include debug print statements at the start of every function/method.

class TestAliasGenerator(unittest.TestCase):
    """Test cases for AliasGenerator class."""
    
    def setUp(self):
        """Set up test fixtures."""
        print('[DEBUG] setUp called')
        self.generator = AliasGenerator()

    def test_initialization(self):
        """Test AliasGenerator initialization."""
        print('[DEBUG] test_initialization called')
        self.assertIsInstance(self.generator, AliasGenerator)
        self.assertEqual(len(self.generator.existing_aliases), 0)

    def test_set_existing_aliases(self):
        """Test setting existing aliases for uniqueness checking."""
        print('[DEBUG] test_set_existing_aliases called')
        aliases = ['test1', 'test2', 'Test3']  # Mixed case
        self.generator.set_existing_aliases(aliases)
        
        # Should store lowercase versions
        expected = {'test1', 'test2', 'test3'}
        self.assertEqual(self.generator.existing_aliases, expected)
    
    def test_validate_alias_valid_cases(self):
        """Test alias validation with valid inputs."""
        print('[DEBUG] test_validate_alias_valid_cases called')
        valid_aliases = [
            'test',
            'test_123',
            'test-alias',
            'a',  # Single character
            'Test_Case_123',
            'api_endpoint',
            'user123'
        ]

        for alias in valid_aliases:
            with self.subTest(alias=alias):
                self.assertTrue(self.generator.validate_alias(alias))

    def test_validate_alias_invalid_cases(self):
        """Test alias validation with invalid inputs."""
        print('[DEBUG] test_validate_alias_invalid_cases called')
        invalid_aliases = [
            '',  # Empty
            '123test',  # Starts with number
            'test space',  # Contains space
            'test@email',  # Contains special character
            'test.file',  # Contains dot
            '_test',  # Starts with underscore
            '-test',  # Starts with hyphen
            'a' * 51,  # Too long
        ]

        for alias in invalid_aliases:
            with self.subTest(alias=alias):
                self.assertFalse(self.generator.validate_alias(alias))
    
    def test_generate_from_url_content(self):
        """Test alias generation from URL content."""
        print('[DEBUG] test_generate_from_url_content called')
        test_cases = [
            ('https://www.github.com/user/repo', 'github'),
            ('http://stackoverflow.com/questions/123', 'stackoverflow'),
            ('https://api.example.com/v1/users', 'example'),
            ('Visit https://docs.python.org for more info', 'docs'),
        ]

        for content, expected_domain in test_cases:
            with self.subTest(content=content):
                alias = self.generator.generate_from_content(content)
                self.assertIn(expected_domain, alias.lower())

    def test_generate_from_filename_content(self):
        """Test alias generation from filename content."""
        print('[DEBUG] test_generate_from_filename_content called')
        test_cases = [
            ('document.pdf', 'document'),
            ('my_report.docx', 'my_report'),
            ('data_analysis.xlsx', 'data_analysis'),
            ('Check out config.json for settings', 'config'),
        ]

        for content, expected_name in test_cases:
            with self.subTest(content=content):
                alias = self.generator.generate_from_content(content)
                self.assertIn(expected_name, alias.lower())
    
    def test_generate_from_code_content(self):
        """Test alias generation from code content."""
        print('[DEBUG] test_generate_from_code_content called')
        test_cases = [
            ('def calculate_total():', 'calculate'),
            ('function getUserData() {', 'getUserData'),
            ('class DatabaseManager:', 'DatabaseManager'),
            ('api_key = "secret"', 'api_key'),
            ('SELECT * FROM users', 'users'),
        ]

        for content, expected_term in test_cases:
            with self.subTest(content=content):
                alias = self.generator.generate_from_content(content)
                # Should contain the expected term or a meaningful part of it
                self.assertTrue(
                    expected_term.lower() in alias.lower() or
                    any(word in alias.lower() for word in expected_term.lower().split('_'))
                )

    def test_generate_from_meaningful_words(self):
        """Test alias generation from meaningful text content."""
        print('[DEBUG] test_generate_from_meaningful_words called')
        test_cases = [
            ('This is a test document about machine learning algorithms', 'machine_learning'),
            ('Database connection configuration settings', 'database_connection'),
            ('User authentication and authorization system', 'user_authentication'),
            ('Important meeting notes from yesterday', 'important_meeting'),
        ]

        for content, expected_pattern in test_cases:
            with self.subTest(content=content):
                alias = self.generator.generate_from_content(content)
                # Should contain meaningful words, not stop words
                words = alias.lower().split('_')
                meaningful_words = [w for w in words if w not in self.generator.STOP_WORDS]
                self.assertGreater(len(meaningful_words), 0)
    
    def test_generate_from_dates_and_numbers(self):
        """Test alias generation from dates and numbers."""
        print('[DEBUG] test_generate_from_dates_and_numbers called')
        test_cases = [
            ('Meeting scheduled for 2024-01-15', 'date_2024_01_15'),
            ('Version 2.1.3 release notes', 'v_2_1_3'),
            ('Reference ID: 12345', 'id_12345'),
            ('Report from 12/25/2023', 'date'),
        ]

        for content, expected_pattern in test_cases:
            with self.subTest(content=content):
                alias = self.generator.generate_from_content(content)
                # Should contain date or version pattern
                self.assertTrue(
                    'date' in alias.lower() or
                    'v_' in alias.lower() or
                    'id_' in alias.lower()
                )

    def test_uniqueness_enforcement(self):
        """Test that generated aliases are unique."""
        print('[DEBUG] test_uniqueness_enforcement called')
        existing_aliases = ['test', 'test_1', 'test_2']
        self.generator.set_existing_aliases(existing_aliases)

        # Should generate test_3 since test, test_1, test_2 exist
        alias = self.generator._ensure_uniqueness('test')
        self.assertEqual(alias, 'test_3')

    def test_fallback_alias_generation(self):
        """Test fallback alias generation when content analysis fails."""
        print('[DEBUG] test_fallback_alias_generation called')
        empty_content_cases = ['', '   ', '!!!', '123', '...']

        for content in empty_content_cases:
            with self.subTest(content=content):
                alias = self.generator.generate_from_content(content)
                self.assertTrue(alias.startswith('clip_'))
                self.assertTrue(alias[5:].isdigit())  # Should be clip_<number>
    
    def test_suggest_alternatives(self):
        """Test alternative alias suggestions."""
        print('[DEBUG] test_suggest_alternatives called')
        base_alias = 'test'
        alternatives = self.generator.suggest_alternatives(base_alias, 3)

        self.assertEqual(len(alternatives), 3)
        self.assertIn('test_1', alternatives)
        self.assertIn('test_2', alternatives)
        self.assertIn('test_3', alternatives)

    def test_suggest_alternatives_with_existing(self):
        """Test alternative suggestions with existing aliases."""
        print('[DEBUG] test_suggest_alternatives_with_existing called')
        existing_aliases = ['test_1', 'test_new']
        self.generator.set_existing_aliases(existing_aliases)

        alternatives = self.generator.suggest_alternatives('test', 5)

        # Should not include existing aliases
        self.assertNotIn('test_1', alternatives)
        self.assertNotIn('test_new', alternatives)

        # Should include available alternatives
        self.assertIn('test_2', alternatives)
        self.assertIn('test_copy', alternatives)

    def test_alias_cleaning(self):
        """Test alias cleaning functionality."""
        print('[DEBUG] test_alias_cleaning called')
        test_cases = [
            ('test@#$%', 'test____'),
            ('  test  ', 'test'),
            ('123test', 'clip_123test'),
            ('test___multiple___underscores', 'test_multiple_underscores'),
            ('UPPERCASE', 'UPPERCASE'),
        ]

        for input_alias, expected_pattern in test_cases:
            with self.subTest(input_alias=input_alias):
                cleaned = self.generator._clean_alias(input_alias)
                # Should start with letter and be reasonable length
                self.assertTrue(cleaned[0].isalpha())
                self.assertLessEqual(len(cleaned), 20)
    
    def test_technical_terms_recognition(self):
        """Test recognition and prioritization of technical terms."""
        print('[DEBUG] test_technical_terms_recognition called')
        technical_content = [
            'API endpoint configuration',
            'SQL database query',
            'JSON response format',
            'HTTP request headers',
            'XML parsing function',
        ]

        for content in technical_content:
            with self.subTest(content=content):
                alias = self.generator.generate_from_content(content)
                # Should contain or prioritize technical terms
                words = alias.lower().split('_')
                has_technical_term = any(word in self.generator.TECHNICAL_TERMS for word in words)
                self.assertTrue(has_technical_term or len(words) > 1)

    def test_content_length_handling(self):
        """Test handling of various content lengths."""
        print('[DEBUG] test_content_length_handling called')
        # Very short content
        short_alias = self.generator.generate_from_content('hi')
        self.assertGreaterEqual(len(short_alias), 3)

        # Very long content
        long_content = 'This is a very long piece of content that contains many words and should be processed efficiently to generate a meaningful alias without performance issues. ' * 10
        long_alias = self.generator.generate_from_content(long_content, max_length=15)
        self.assertLessEqual(len(long_alias), 15)

        # Medium content
        medium_content = 'Database connection settings for production environment'
        medium_alias = self.generator.generate_from_content(medium_content)
        self.assertGreater(len(medium_alias), 3)
        self.assertLessEqual(len(medium_alias), 20)

    def test_max_length_enforcement(self):
        """Test that generated aliases respect max length parameter."""
        print('[DEBUG] test_max_length_enforcement called')
        content = 'very_long_function_name_that_should_be_truncated'

        for max_len in [5, 10, 15, 20]:
            with self.subTest(max_length=max_len):
                alias = self.generator.generate_from_content(content, max_length=max_len)
                self.assertLessEqual(len(alias), max_len)

if __name__ == '__main__':
    unittest.main()
