import sys
import os
import unittest
import sqlite3
import tempfile

print('Starting test_op_clipsmore_tbl.py')
# Add the project root to sys.path for imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))
from source.DB.op_clipsmore_tbl import ClipsMoreTableOperations, ClipError, ClipsMoreNotFoundError, ClipsMoreValidationError
from source.DB.db_connection import ConnectionPoolManager

# NOTE: All new code should include debug print statements at the start of every function/method.

class TestClipsMoreTableOperations(unittest.TestCase):
    def setUp(self):
        print('Running setUp')
        # Use a temporary file-based SQLite database for reliable testing
        self.temp_db = tempfile.NamedTemporaryFile(suffix='.db', delete=False)
        self.db_path = self.temp_db.name
        self.temp_db.close()  # Close immediately so SQLite can open it on Windows
        # Set up minimal tables for testing
        conn = sqlite3.connect(self.db_path)
        cur = conn.cursor()
        cur.execute('PRAGMA foreign_keys = ON;')
        cur.execute("""
            CREATE TABLE IF NOT EXISTS clips_tbl (
                clip_id INTEGER PRIMARY KEY,
                clip TEXT NOT NULL,
                alias TEXT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        cur.execute("""
            CREATE TABLE IF NOT EXISTS more_bus_tbl (
                more_bus_id INTEGER PRIMARY KEY,
                bus_case TEXT NOT NULL
            )
        """)
        cur.execute("""
            CREATE TABLE IF NOT EXISTS more_comp_tbl (
                more_comp_id INTEGER PRIMARY KEY,
                more_bus_id INTEGER NOT NULL,
                bus_component TEXT,
                FOREIGN KEY (more_bus_id) REFERENCES more_bus_tbl (more_bus_id) ON DELETE CASCADE
            )
        """)
        cur.execute("""
            CREATE TABLE IF NOT EXISTS clipsmore_tbl (
                clip_id INTEGER NOT NULL,
                more_bus_id INTEGER NOT NULL,
                more_comp_id INTEGER,
                FOREIGN KEY (clip_id) REFERENCES clips_tbl (clip_id) ON DELETE CASCADE,
                FOREIGN KEY (more_bus_id) REFERENCES more_bus_tbl (more_bus_id) ON DELETE CASCADE,
                FOREIGN KEY (more_comp_id) REFERENCES more_comp_tbl (more_comp_id) ON DELETE SET NULL,
                CONSTRAINT uq_clip_more UNIQUE (clip_id, more_bus_id, more_comp_id)
            )
        """)
        # Insert actual data from production database for foreign keys
        cur.execute("INSERT INTO clips_tbl (clip_id, clip, alias) VALUES (1, ?, ?)",
                   ("Cybercriminals are getting smarter. Are you?\nA 76-year-old retired lawyer lost his entire life savings...", "cybercriminals"))
        cur.execute("INSERT INTO clips_tbl (clip_id, clip, alias) VALUES (2, ?, ?)", ("Imposter scams", "imposter_scams"))
        cur.execute("INSERT INTO clips_tbl (clip_id, clip, alias) VALUES (3, ?, ?)", ("Romance scams", "romance_scams"))
        cur.execute("INSERT INTO more_bus_tbl (more_bus_id, bus_case) VALUES (1, ?)", ("Cyber Security",))
        cur.execute("INSERT INTO more_comp_tbl (more_comp_id, more_bus_id, bus_component) VALUES (1, 1, ?)", ("Imposter scams",))
        cur.execute("INSERT INTO more_comp_tbl (more_comp_id, more_bus_id, bus_component) VALUES (2, 1, ?)", ("Romance scams",))
        cur.execute("INSERT INTO more_comp_tbl (more_comp_id, more_bus_id, bus_component) VALUES (3, 1, ?)", ("Call-center scams",))
        conn.commit()
        conn.close()
        # Reset the ConnectionPoolManager singleton so it uses the correct db_path
        ConnectionPoolManager._instance = None
        self.pool = ConnectionPoolManager(self.db_path)
        self.clipsmore_ops = ClipsMoreTableOperations(self.db_path)

    def tearDown(self):
        # Clean up the temporary database file
        # Ensure all connections are closed before deleting the file (important on Windows)
        if hasattr(self.pool, 'closeall'):
            self.pool.closeall()
        if hasattr(ConnectionPoolManager, '_instance'):
            ConnectionPoolManager._instance = None
        del self.clipsmore_ops
        del self.pool
        import gc
        gc.collect()  # Force garbage collection to close any lingering file handles
        os.unlink(self.db_path)

    def test_assign_clip_to_business_case(self):
        # Assign clip1 to bus1 without a component
        data = {'clip_id': 1, 'more_bus_id': 1, 'more_comp_id': None}
        entry_id = self.clipsmore_ops.create_entry(data)
        entry = self.clipsmore_ops.read_entry(entry_id)
        self.assertEqual(entry['clip_id'], 1)
        self.assertEqual(entry['more_bus_id'], 1)
        self.assertIsNone(entry['more_comp_id'])

    def test_assign_clip_to_business_case_and_component(self):
        # Assign clip2 to bus1 and comp1
        data = {'clip_id': 2, 'more_bus_id': 1, 'more_comp_id': 1}
        entry_id = self.clipsmore_ops.create_entry(data)
        entry = self.clipsmore_ops.read_entry(entry_id)
        self.assertEqual(entry['clip_id'], 2)
        self.assertEqual(entry['more_bus_id'], 1)
        self.assertEqual(entry['more_comp_id'], 1)

    def test_unique_constraint(self):
        # Assign the same clip to the same bus/component twice should fail
        data = {'clip_id': 1, 'more_bus_id': 1, 'more_comp_id': None}
        self.clipsmore_ops.create_entry(data)
        with self.assertRaises(ClipsMoreValidationError):
            self.clipsmore_ops.create_entry(data)

    def test_delete_assignment(self):
        data = {'clip_id': 2, 'more_bus_id': 1, 'more_comp_id': 1}
        entry_id = self.clipsmore_ops.create_entry(data)
        self.clipsmore_ops.delete_entry(entry_id)
        with self.assertRaises(ClipsMoreNotFoundError):
            self.clipsmore_ops.read_entry(entry_id)

    def test_assign_clip_to_different_business_cases(self):
        # Assign the same clip to two different business cases
        data1 = {'clip_id': 1, 'more_bus_id': 1, 'more_comp_id': None}
        data2 = {'clip_id': 1, 'more_bus_id': 2, 'more_comp_id': None}
        id1 = self.clipsmore_ops.create_entry(data1)
        id2 = self.clipsmore_ops.create_entry(data2)
        self.assertNotEqual(id1, id2)
        entry1 = self.clipsmore_ops.read_entry(id1)
        entry2 = self.clipsmore_ops.read_entry(id2)
        self.assertEqual(entry1['clip_id'], 1)
        self.assertEqual(entry2['clip_id'], 1)
        self.assertEqual(entry1['more_bus_id'], 1)
        self.assertEqual(entry2['more_bus_id'], 2)

if __name__ == "__main__":
    print('Running unittest.main()')
    unittest.main()
