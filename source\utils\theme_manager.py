#!/usr/bin/env python3
"""
ThemeManager - Centralized theme management for ClipsMore application.

This module provides comprehensive theme management including dark/light mode switching,
color scheme management, TTK styling, and widget color updates.

Features:
- Dark and light theme support
- TTK style configuration
- Recursive widget color updates
- Theme state management
- Color scheme definitions

Author: ClipsMore Development Team
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, Any, Optional


class ThemeManager:
    """
    Manages application themes and color schemes.
    
    This class handles all theme-related operations including color definitions,
    theme switching, TTK styling, and widget color updates.
    """
    
    def __init__(self, root: tk.Tk):
        """
        Initialize the ThemeManager.
        
        Args:
            root: The main Tkinter root window
        """
        print('[DEBUG] ThemeManager.__init__ called')
        self.root = root
        self.dark_mode = False
        
        # Initialize color scheme
        self._initialize_colors()
        
        # Apply initial theme
        self.set_theme(self.dark_mode)
    
    def _initialize_colors(self):
        """Initialize color scheme variables."""
        print('[DEBUG] ThemeManager._initialize_colors called')
        # Colors will be set by set_theme method
        self.bg_color = None
        self.fg_color = None
        self.entry_bg = None
        self.entry_fg = None
        self.button_bg = None
        self.button_fg = None
        self.tree_bg = None
        self.tree_fg = None
        self.tree_select = None
    
    def set_theme(self, dark_mode: bool):
        """
        Set the application theme.
        
        Args:
            dark_mode: True for dark theme, False for light theme
        """
        print(f'[DEBUG] ThemeManager.set_theme called with dark_mode={dark_mode}')
        self.dark_mode = dark_mode
        
        if self.dark_mode:
            self._apply_dark_theme()
        else:
            self._apply_light_theme()
        
        # Apply TTK styles
        self.apply_ttk_styles()
    
    def _apply_dark_theme(self):
        """Apply dark theme color scheme."""
        print('[DEBUG] ThemeManager._apply_dark_theme called')
        # Dark theme colors
        self.root.configure(bg="#1e1e1e")
        self.bg_color = "#1e1e1e"
        self.fg_color = "#ffffff"
        self.entry_bg = "#2d2d2d"
        self.entry_fg = "#ffffff"
        self.button_bg = "#404040"
        self.button_fg = "#ffffff"
        self.tree_bg = "#252525"
        self.tree_fg = "#ffffff"
        self.tree_select = "#0078d4"
    
    def _apply_light_theme(self):
        """Apply light theme color scheme."""
        print('[DEBUG] ThemeManager._apply_light_theme called')
        # Light theme colors
        self.root.configure(bg="#f0f0f0")
        self.bg_color = "#f0f0f0"
        self.fg_color = "#000000"
        self.entry_bg = "#ffffff"
        self.entry_fg = "#000000"
        self.button_bg = "#e1e1e1"
        self.button_fg = "#000000"
        self.tree_bg = "#ffffff"
        self.tree_fg = "#000000"
        self.tree_select = "#0078d4"
    
    def apply_ttk_styles(self):
        """Apply TTK widget styles based on current theme."""
        print('[DEBUG] ThemeManager.apply_ttk_styles called')
        style = ttk.Style()
        
        if self.dark_mode:
            style.theme_use('clam')
            style.configure("TNotebook", background=self.bg_color, borderwidth=0)
            style.configure("TNotebook.Tab", background="#404040", foreground=self.fg_color,
                          padding=[12, 8], borderwidth=1)
            style.map("TNotebook.Tab", background=[('selected', '#0078d4')])
            style.configure("TFrame", background=self.bg_color)
            style.configure("Treeview", background=self.tree_bg, foreground=self.tree_fg,
                          fieldbackground=self.tree_bg, borderwidth=0)
            style.configure("Treeview.Heading", background="#404040", foreground=self.fg_color,
                          relief="flat")
            style.map("Treeview", background=[('selected', self.tree_select)])
        else:
            style.theme_use('default')
            style.configure("TNotebook", background=self.bg_color)
            style.configure("TFrame", background=self.bg_color)
            style.configure("Treeview", background=self.tree_bg, foreground=self.tree_fg,
                          fieldbackground=self.tree_bg)
            style.map("Treeview", background=[('selected', self.tree_select)])
    
    def toggle_theme(self) -> bool:
        """
        Toggle between light and dark mode.
        
        Returns:
            bool: New dark_mode state
        """
        print('[DEBUG] ThemeManager.toggle_theme called')
        self.dark_mode = not self.dark_mode
        self.set_theme(self.dark_mode)
        return self.dark_mode
    
    def get_theme_colors(self) -> Dict[str, str]:
        """
        Get current theme color scheme.
        
        Returns:
            Dict containing all theme colors
        """
        print('[DEBUG] ThemeManager.get_theme_colors called')
        return {
            'bg_color': self.bg_color,
            'fg_color': self.fg_color,
            'entry_bg': self.entry_bg,
            'entry_fg': self.entry_fg,
            'button_bg': self.button_bg,
            'button_fg': self.button_fg,
            'tree_bg': self.tree_bg,
            'tree_fg': self.tree_fg,
            'tree_select': self.tree_select,
            'dark_mode': self.dark_mode
        }
    
    def update_widget_colors(self, widget: tk.Widget):
        """
        Recursively update widget colors based on current theme.
        
        Args:
            widget: The parent widget to update (updates all children recursively)
        """
        print('[DEBUG] ThemeManager.update_widget_colors called')
        self._update_widget_colors_recursive(widget)
    
    def _update_widget_colors_recursive(self, parent: tk.Widget):
        """
        Recursively update widget colors.
        
        Args:
            parent: Parent widget to update
        """
        print('[DEBUG] ThemeManager._update_widget_colors_recursive called')
        for widget in parent.winfo_children():
            try:
                if isinstance(widget, tk.Label):
                    widget.config(bg=self.bg_color, fg=self.fg_color)
                elif isinstance(widget, tk.Text):
                    widget.config(bg=self.entry_bg, fg=self.entry_fg, insertbackground=self.entry_fg)
                elif isinstance(widget, tk.Entry):
                    widget.config(bg=self.entry_bg, fg=self.entry_fg, insertbackground=self.entry_fg)
                elif isinstance(widget, tk.Button):
                    widget.config(bg=self.button_bg, fg=self.button_fg, activebackground=self.tree_select,
                                activeforeground=self.fg_color)
                elif isinstance(widget, tk.Frame):
                    widget.config(bg=self.bg_color)
                elif isinstance(widget, tk.Scrollbar):
                    if self.dark_mode:
                        widget.config(bg=self.button_bg, troughcolor=self.bg_color,
                                    activebackground=self.tree_select)
                elif isinstance(widget, tk.Canvas):
                    widget.config(bg=self.bg_color)

                # Recursively update child widgets
                self._update_widget_colors_recursive(widget)
            except tk.TclError:
                # Some widgets might not support certain config options
                pass
    
    def get_tooltip_colors(self) -> tuple[str, str]:
        """
        Get tooltip colors for current theme.
        
        Returns:
            Tuple of (background_color, foreground_color)
        """
        print('[DEBUG] ThemeManager.get_tooltip_colors called')
        if self.dark_mode:
            return "#404040", "#ffffff"
        else:
            return "#ffffcc", "#000000"
    
    def get_markdown_colors(self) -> Dict[str, str]:
        """
        Get markdown styling colors for current theme.
        
        Returns:
            Dict containing markdown element colors
        """
        print('[DEBUG] ThemeManager.get_markdown_colors called')
        if self.dark_mode:
            return {
                'h1': '#64B5F6',
                'h2': '#F06292',
                'h3': '#FFB74D',
                'h4': '#FF8A65',
                'code_bg': '#2D2D2D',
                'code_fg': '#F44336',
                'code_block_bg': '#1E1E1E',
                'code_block_fg': '#E0E0E0',
                'link': '#42A5F5',
                'quote_bg': '#2E2E2E',
                'quote_fg': '#81C784'
            }
        else:
            return {
                'h1': '#2E86AB',
                'h2': '#A23B72',
                'h3': '#F18F01',
                'h4': '#C73E1D',
                'code_bg': '#F5F5F5',
                'code_fg': '#D32F2F',
                'code_block_bg': '#F8F8F8',
                'code_block_fg': '#333333',
                'link': '#1976D2',
                'quote_bg': '#E8F5E8',
                'quote_fg': '#2E7D32'
            }
    
    def is_dark_mode(self) -> bool:
        """
        Check if dark mode is currently active.
        
        Returns:
            bool: True if dark mode is active
        """
        return self.dark_mode
