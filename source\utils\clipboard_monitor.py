#!/usr/bin/env python3
"""
Clipboard Monitor for ClipsMore Application

This module provides background clipboard monitoring functionality that automatically
detects new clipboard content and saves it to the database.
"""

import threading
import time
import tkinter as tk
from typing import Callable, Optional
from source.utils.database_manager import DatabaseManager


class ClipboardMonitor:
    """
    Background clipboard monitoring service that detects new clipboard content
    and automatically saves it to the database.
    """
    
    def __init__(self, root: tk.Tk, database_manager: DatabaseManager, 
                 callback: Optional[Callable] = None, check_interval: float = 1.0):
        """
        Initialize clipboard monitor.
        
        Args:
            root: Tkinter root window for clipboard access
            database_manager: Database manager for saving clips
            callback: Optional callback function called when new clip is detected
            check_interval: Time in seconds between clipboard checks
        """
        print('[DEBUG] ClipboardMonitor.__init__ called')
        
        self.root = root
        self.database_manager = database_manager
        self.callback = callback
        self.check_interval = check_interval
        
        # State tracking
        self.monitoring = False
        self.last_content = ""
        self.monitor_thread = None
        
        # Get initial clipboard content
        try:
            self.last_content = self.root.clipboard_get()
            print(f'[DEBUG] Initial clipboard content: {len(self.last_content)} characters')
        except tk.TclError:
            self.last_content = ""
            print('[DEBUG] No initial clipboard content')
    
    def start_monitoring(self):
        """Start background clipboard monitoring."""
        print('[DEBUG] ClipboardMonitor.start_monitoring called')
        
        if self.monitoring:
            print('[WARNING] Clipboard monitoring already running')
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        print('[DEBUG] Clipboard monitoring started')
    
    def stop_monitoring(self):
        """Stop background clipboard monitoring."""
        print('[DEBUG] ClipboardMonitor.stop_monitoring called')
        
        self.monitoring = False
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=2.0)
        print('[DEBUG] Clipboard monitoring stopped')
    
    def _monitor_loop(self):
        """Main monitoring loop that runs in background thread."""
        print('[DEBUG] ClipboardMonitor._monitor_loop started')

        while self.monitoring:
            try:
                # Schedule clipboard check on main thread using after()
                self.root.after(0, self._check_clipboard_on_main_thread)

                # Wait before next check
                time.sleep(self.check_interval)

            except Exception as e:
                print(f'[ERROR] Error in clipboard monitoring loop: {e}')
                time.sleep(self.check_interval)

        print('[DEBUG] ClipboardMonitor._monitor_loop ended')

    def _check_clipboard_on_main_thread(self):
        """Check clipboard content on main thread."""
        try:
            # Get current clipboard content
            current_content = self.root.clipboard_get()

            # Compare with last known content
            if current_content and current_content != self.last_content:
                print(f'[DEBUG] New clipboard content detected: {len(current_content)} characters')

                # Process new content
                self._on_clipboard_change(current_content)
                self.last_content = current_content

        except tk.TclError:
            # No clipboard content or clipboard access error
            pass
        except Exception as e:
            print(f'[ERROR] Error checking clipboard on main thread: {e}')
    

    
    def _on_clipboard_change(self, content: str):
        """
        Handle new clipboard content detection.
        
        Args:
            content: New clipboard content
        """
        print(f'[DEBUG] ClipboardMonitor._on_clipboard_change called with {len(content)} characters')
        
        try:
            # Filter out very short or empty content
            if not content or len(content.strip()) < 2:
                print('[DEBUG] Skipping very short clipboard content')
                return
            
            # Filter out content that's too long (likely files or large data)
            if len(content) > 10000:
                print('[DEBUG] Skipping very long clipboard content')
                return
            
            # Save to database
            self._save_clip_to_database(content)
            
            # Call callback if provided
            if self.callback:
                try:
                    self.callback(content)
                except Exception as e:
                    print(f'[ERROR] Error in clipboard change callback: {e}')
        
        except Exception as e:
            print(f'[ERROR] Error processing clipboard change: {e}')
    
    def _save_clip_to_database(self, content: str):
        """
        Save new clip content to database if it's not a duplicate.

        Args:
            content: Clip content to save
        """
        print(f'[DEBUG] ClipboardMonitor._save_clip_to_database called')

        try:
            clips_ops = self.database_manager.get_clips_operations()

            # Strip content for consistency
            content_stripped = content.strip()

            # Check for duplicate content first
            existing_clip_id = clips_ops.find_duplicate_clip(content_stripped)
            if existing_clip_id:
                print(f'[DEBUG] Duplicate clip detected - existing clip ID: {existing_clip_id}. Skipping save.')
                return

            # Create clip data
            clip_data = {
                'clip': content_stripped,
                'alias': None  # No alias for auto-captured clips
            }

            # Save to database
            clip_id = clips_ops.create_clip(clip_data)
            print(f'[DEBUG] Auto-saved new unique clip with ID: {clip_id}')

        except Exception as e:
            print(f'[ERROR] Failed to save clip to database: {e}')
    
    def is_monitoring(self) -> bool:
        """
        Check if monitoring is currently active.
        
        Returns:
            True if monitoring is active, False otherwise
        """
        return self.monitoring
    
    def get_status(self) -> dict:
        """
        Get current monitor status.
        
        Returns:
            Dictionary with monitoring status information
        """
        return {
            'monitoring': self.monitoring,
            'last_content_length': len(self.last_content),
            'check_interval': self.check_interval,
            'thread_alive': self.monitor_thread.is_alive() if self.monitor_thread else False
        }
