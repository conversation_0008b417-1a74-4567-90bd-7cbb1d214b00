# 💾 ClipsMore Export, Backup & Import Guide

🏠 [ClipsMore](../../README.md) > 📚 [User Documentation](README.md) > 💾 Export, Backup & Import Guide

## 🚀 Quick Start

ClipsMore provides comprehensive data management capabilities to help you export your clipboard data, create secure backups, and import data from other clipboard managers. This guide will walk you through all available options and best practices.

### **Essential Operations**
- **Export**: Save your clips in various formats (JSON, CSV, HTML, XML)
- **Backup**: Create complete database backups with compression and verification
- **Import**: Bring data from other clipboard managers or previous exports
- **Restore**: Recover from backups when needed

---

## 📤 Export System

### **Supported Export Formats**

#### **JSON Format (.json)**
**Best for:** Complete data preservation, technical users, re-importing to ClipsMore
- ✅ **Complete Structure**: Preserves all metadata, relationships, and hierarchy
- ✅ **UTF-8 Support**: Full Unicode character support
- ✅ **Pretty Printing**: Human-readable formatting option
- ✅ **Metadata Included**: Export statistics, timestamps, and configuration

**Configuration Options:**
```json
{
  "indent": 2,
  "include_metadata": true,
  "hierarchical": false,
  "pretty_print": true
}
```

#### **CSV Format (.csv)**
**Best for:** Spreadsheet analysis, data processing, Excel compatibility
- ✅ **Configurable Columns**: Choose which data fields to include
- ✅ **Multiple Encodings**: UTF-8, UTF-8-BOM, Latin1 support
- ✅ **Custom Delimiters**: Comma, semicolon, tab, or custom separators
- ✅ **Excel Compatible**: Proper formatting for Microsoft Excel

**Available Columns:**
- Transaction ID, Clip ID, Alias, Content, Timestamp
- Business Case, Component, Assignment Date
- Content Length, Content Type, Tags

#### **HTML Format (.html)**
**Best for:** Sharing, presentations, web viewing, printing
- ✅ **Styled Output**: Professional CSS styling with themes
- ✅ **Navigation**: Table of contents and jump links
- ✅ **Theme Options**: Light, dark, and blue themes
- ✅ **Print Friendly**: Optimized for printing and PDF conversion

**Theme Options:**
- **Light Theme**: Clean white background, professional appearance
- **Dark Theme**: Dark background, ideal for screen viewing
- **Blue Theme**: Corporate blue styling, presentation-ready

#### **XML Format (.xml)**
**Best for:** System integration, structured data exchange, archival
- ✅ **Schema Validation**: Well-formed XML with proper structure
- ✅ **Namespace Support**: Proper XML namespacing
- ✅ **Hierarchical Structure**: Maintains business case/component relationships
- ✅ **Metadata Preservation**: Complete data integrity

### **How to Export Data**

#### **Step-by-Step Export Process**

1. **Access Export Function**
   - Navigate to the **More Tab**
   - Click the **Export** button in the toolbar
   - Or use keyboard shortcut **Ctrl+E**

2. **Choose Export Format**
   - Select from JSON, CSV, HTML, or XML
   - Each format shows a description and recommended use cases
   - Preview format-specific options

3. **Configure Selection Criteria**
   - **Date Range**: Export clips from specific time periods
   - **Business Cases**: Export specific business cases only
   - **Components**: Filter by specific components
   - **Content Type**: Filter by text, images, or other content types
   - **Assignment Status**: Include only assigned or unassigned clips

4. **Set Format Options**
   - **JSON**: Indentation, metadata inclusion, hierarchical grouping
   - **CSV**: Column selection, encoding, delimiter preferences
   - **HTML**: Theme selection, navigation options, styling
   - **XML**: Schema validation, namespace preferences

5. **Choose Output Location**
   - Browse to desired folder
   - Enter filename (extension added automatically)
   - Verify write permissions

6. **Execute Export**
   - Review export summary
   - Click **Export** to begin
   - Monitor progress bar
   - Receive completion notification

#### **Export Templates**
Save frequently used export configurations as templates:
- **Quick Export**: Common settings for regular backups
- **Presentation Export**: HTML format with professional styling
- **Data Analysis**: CSV format with all columns for spreadsheet work
- **Archive Export**: JSON format with complete metadata

### **Advanced Export Features**

#### **Batch Export**
Export multiple formats simultaneously:
```
✓ JSON (complete backup)
✓ CSV (for analysis)
✓ HTML (for sharing)
```

#### **Scheduled Exports**
Set up automatic exports:
- **Daily**: Export new clips daily
- **Weekly**: Full export every week
- **Monthly**: Complete archive monthly
- **Custom**: Define your own schedule

#### **Export Validation**
Automatic validation ensures data integrity:
- **File Size Check**: Verify expected output size
- **Format Validation**: Confirm proper format structure
- **Content Verification**: Sample content validation
- **Checksum Generation**: Create file integrity checksums

---

## 💾 Backup System

### **Backup Types**

#### **Full Backup**
Complete database backup including:
- All clipboard entries and metadata
- Business cases and component hierarchy
- User preferences and settings
- Export templates and configurations
- Import history and statistics

#### **Incremental Backup**
Backup only changes since last backup:
- New clipboard entries
- Modified aliases and assignments
- Updated business cases/components
- Changed preferences

#### **Selective Backup**
Backup specific data sets:
- Specific date ranges
- Selected business cases
- Particular components
- Custom filter criteria

### **Backup Features**

#### **Compression**
Automatic backup compression:
- **Algorithm**: GZIP compression for optimal size/speed balance
- **Compression Ratio**: Typically 60-80% size reduction
- **Integrity**: Built-in compression verification
- **Performance**: Minimal impact on backup speed

#### **Verification**
Automatic backup verification:
- **Checksum Validation**: SHA-256 checksums for integrity
- **Structure Verification**: Database structure validation
- **Content Sampling**: Random content verification
- **Restoration Test**: Optional test restoration

#### **Encryption** (Optional)
Secure backup encryption:
- **Algorithm**: AES-256 encryption
- **Password Protection**: User-defined passwords
- **Key Derivation**: PBKDF2 with salt
- **Secure Storage**: Encrypted metadata

### **Creating Backups**

#### **Manual Backup Process**

1. **Access Backup Function**
   - Navigate to **More Tab**
   - Click **Backup** button
   - Or use **Ctrl+B** shortcut

2. **Choose Backup Type**
   - **Full**: Complete database backup
   - **Incremental**: Changes since last backup
   - **Selective**: Custom data selection

3. **Configure Backup Options**
   - **Compression**: Enable/disable compression
   - **Verification**: Enable integrity checking
   - **Encryption**: Optional password protection
   - **Metadata**: Include configuration and preferences

4. **Select Backup Location**
   - Choose backup directory
   - Enter backup filename
   - Verify available disk space

5. **Execute Backup**
   - Review backup summary
   - Start backup process
   - Monitor progress
   - Verify completion

#### **Automatic Backup**
Configure automatic backups:
- **Schedule**: Daily, weekly, monthly, or custom intervals
- **Location**: Default or custom backup directory
- **Retention**: Keep last N backups, delete older ones
- **Notifications**: Email or system notifications on completion

### **Backup Best Practices**

#### **Storage Recommendations**
- **Local Backups**: Fast access, immediate availability
- **External Drive**: Protection against system failure
- **Cloud Storage**: Off-site protection, accessibility
- **Multiple Locations**: 3-2-1 backup strategy recommended

#### **Backup Schedule**
- **Daily**: For active users with frequent clip creation
- **Weekly**: For moderate users with regular usage
- **Monthly**: For light users or archival purposes
- **Before Major Changes**: Before system updates or migrations

#### **Backup Verification**
- **Regular Testing**: Test restore process monthly
- **Integrity Checks**: Verify backup checksums
- **Size Monitoring**: Monitor backup size trends
- **Error Logging**: Review backup logs for issues

---

## 📥 Import System

### **Supported Import Sources**

#### **ClipsMore Exports**
Import from previous ClipsMore exports:
- **JSON Files**: Complete structure and metadata
- **CSV Files**: Flat data with configurable mapping
- **XML Files**: Structured data with hierarchy
- **Backup Files**: Full database restoration

#### **External Clipboard Managers**

**Ditto Clipboard Manager**
- **Database Import**: Direct SQLite database import
- **Format Support**: Text, images, and rich content
- **Metadata Preservation**: Timestamps and categories
- **Duplicate Handling**: Intelligent duplicate detection

**ClipX Clipboard Manager**
- **Configuration Import**: ClipX settings and data
- **History Import**: Complete clipboard history
- **Category Mapping**: Map ClipX categories to business cases
- **Format Conversion**: Convert ClipX formats to ClipsMore

**Windows Clipboard History**
- **System Integration**: Import from Windows 10+ clipboard history
- **Recent Items**: Import recent clipboard entries
- **Format Detection**: Automatic content type detection
- **Privacy Respect**: Honor Windows privacy settings

#### **Generic Formats**
Import from standard file formats:
- **CSV Files**: Spreadsheet data with field mapping
- **JSON Files**: Structured data from other applications
- **Text Files**: Plain text with line-by-line import
- **XML Files**: Structured data with schema validation

### **Import Process**

#### **Step-by-Step Import**

1. **Access Import Function**
   - Navigate to **More Tab**
   - Click **Import** button
   - Or use **Ctrl+I** shortcut

2. **Select Import Source**
   - Choose file to import
   - System automatically detects format
   - Preview import source information

3. **Configure Import Settings**
   - **Duplicate Handling**: Skip, replace, or create new
   - **Field Mapping**: Map source fields to ClipsMore fields
   - **Validation**: Enable data validation and cleaning
   - **Preview**: Preview import results before committing

4. **Map Data Fields**
   - **Content**: Map to clip content field
   - **Timestamp**: Map to creation date
   - **Categories**: Map to business cases/components
   - **Metadata**: Map additional fields

5. **Preview and Validate**
   - Review import summary
   - Check data mapping accuracy
   - Validate data integrity
   - Estimate import time

6. **Execute Import**
   - Start import process
   - Monitor progress
   - Handle any conflicts
   - Verify completion

#### **Import Configuration Options**

**Duplicate Detection**
- **Content Matching**: Exact content comparison
- **Fuzzy Matching**: Similar content detection
- **Timestamp Tolerance**: Allow minor timestamp differences
- **Alias Matching**: Check for existing aliases

**Data Validation**
- **Content Validation**: Check for valid content types
- **Date Validation**: Verify timestamp formats
- **Size Limits**: Enforce content size limits
- **Character Encoding**: Validate text encoding

**Error Handling**
- **Skip Errors**: Continue import, log errors
- **Stop on Error**: Halt import on first error
- **Interactive**: Prompt user for error resolution
- **Rollback**: Undo import on critical errors

### **Import Best Practices**

#### **Pre-Import Preparation**
- **Backup First**: Always backup before importing
- **Clean Source Data**: Remove unnecessary or duplicate entries
- **Verify Format**: Ensure source file is properly formatted
- **Test Import**: Use small sample for testing

#### **Data Mapping**
- **Review Mappings**: Verify field mappings are correct
- **Handle Missing Fields**: Define defaults for missing data
- **Validate Relationships**: Ensure business case/component relationships
- **Preserve Metadata**: Import timestamps and other metadata

#### **Post-Import Verification**
- **Count Verification**: Verify expected number of records imported
- **Spot Checking**: Manually verify sample imported records
- **Relationship Validation**: Check business case/component assignments
- **Performance Testing**: Ensure application performance after import

---

## 🔄 Restore Operations

### **Backup Restoration**

#### **Full Database Restore**
Complete database replacement:
1. **Stop ClipsMore**: Close application completely
2. **Backup Current**: Create backup of current database
3. **Select Restore File**: Choose backup file to restore
4. **Verify Backup**: Validate backup integrity
5. **Restore Database**: Replace current database
6. **Restart Application**: Launch ClipsMore
7. **Verify Restoration**: Check data integrity

#### **Selective Restore**
Restore specific data portions:
- **Date Range Restore**: Restore clips from specific periods
- **Business Case Restore**: Restore specific business cases
- **Configuration Restore**: Restore only settings and preferences
- **Merge Restore**: Merge backup data with current data

### **Recovery Scenarios**

#### **Data Corruption**
- **Symptoms**: Application crashes, data inconsistencies
- **Solution**: Restore from most recent verified backup
- **Prevention**: Regular backup verification and integrity checks

#### **Accidental Deletion**
- **Symptoms**: Missing clips, business cases, or components
- **Solution**: Selective restore or import from backup
- **Prevention**: Confirmation dialogs, undo functionality

#### **System Migration**
- **Scenario**: Moving to new computer or system
- **Solution**: Full backup on old system, restore on new system
- **Considerations**: Path adjustments, permission settings

#### **Version Rollback**
- **Scenario**: Issues with ClipsMore update
- **Solution**: Restore backup from previous version
- **Considerations**: Database schema compatibility

---

## 🛠️ Troubleshooting

### **Common Export Issues**

**Export Fails to Start**
- Check file permissions in target directory
- Verify sufficient disk space
- Ensure target path is valid
- Close any applications using the target file

**Incomplete Export**
- Check for database corruption
- Verify selection criteria
- Review export logs for errors
- Try smaller data sets

**Format-Specific Issues**
- **CSV**: Check encoding settings, delimiter conflicts
- **JSON**: Verify UTF-8 support, check for invalid characters
- **HTML**: Ensure CSS resources are available
- **XML**: Validate schema compliance

### **Common Backup Issues**

**Backup Creation Fails**
- Verify database is not locked by another process
- Check available disk space
- Ensure backup directory is writable
- Review backup logs for specific errors

**Backup Verification Fails**
- Check backup file integrity
- Verify checksum calculations
- Test backup file accessibility
- Review compression settings

**Automatic Backup Issues**
- Check backup schedule configuration
- Verify system permissions for scheduled tasks
- Review backup service logs
- Test manual backup first

### **Common Import Issues**

**Import File Not Recognized**
- Verify file format and extension
- Check file encoding (UTF-8 recommended)
- Ensure file is not corrupted
- Try opening file in text editor

**Data Mapping Errors**
- Review field mapping configuration
- Check for required fields
- Verify data types match expectations
- Test with smaller sample file

**Import Performance Issues**
- Break large imports into smaller batches
- Disable real-time validation during import
- Close other applications during import
- Consider importing during off-peak hours

### **Getting Help**

**Log Files**
- Export logs: `logs/export_YYYYMMDD.log`
- Backup logs: `logs/backup_YYYYMMDD.log`
- Import logs: `logs/import_YYYYMMDD.log`

**Support Information**
- Include log files when reporting issues
- Provide file format and size information
- Describe exact steps that led to the problem
- Include system information (OS, ClipsMore version)

---

## 📚 Quick Reference

### **Export Shortcuts**
- **Ctrl+E**: Open export dialog
- **Ctrl+Shift+E**: Quick export with last settings
- **F5**: Refresh export preview

### **Backup Shortcuts**
- **Ctrl+B**: Create backup
- **Ctrl+Shift+B**: Quick backup with default settings
- **F6**: Verify last backup

### **Import Shortcuts**
- **Ctrl+I**: Open import dialog
- **Ctrl+Shift+I**: Import with last settings
- **F7**: Preview import data

### **File Extensions**
- **.json**: JSON export files
- **.csv**: CSV export files
- **.html**: HTML export files
- **.xml**: XML export files
- **.cmb**: ClipsMore backup files
- **.cmi**: ClipsMore import files

## See Also

- **📖 [User Guide](User_Guide.md)** - Complete user manual with step-by-step instructions
- **⌨️ [Keyboard Shortcuts Guide](Keyboard_Shortcuts_Guide.md)** - Complete keyboard navigation and accessibility features
- **⚡ [Advanced Features Guide](Advanced_Features_Guide.md)** - Power user features and advanced workflows
- **🏗️ [Technical Documentation](../technical/README.md)** - Architecture and implementation details
- **📚 [User Documentation Index](README.md)** - All user guides and references
- **🏠 [Back to ClipsMore](../../README.md)** - Main project overview

---

*This guide covers ClipsMore v2.0+ export, backup, and import features. For additional help, visit the About tab or check the User Guide.*

🏠 **[Back to ClipsMore](../../README.md)** | 📚 **[User Documentation](README.md)**
