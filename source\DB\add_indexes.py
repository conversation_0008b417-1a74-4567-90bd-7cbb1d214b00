#!/usr/bin/env python3
"""
Database Index Creation Script
Adds performance indexes to ClipsMore database for optimal query performance.
"""

import sqlite3
import os
import sys
from datetime import datetime

# NOTE: All new code should include debug print statements at the start of every function/method.

def backup_database(db_path):
    """Create a backup of the database before adding indexes."""
    print('[DEBUG] backup_database called')
    backup_path = f"{db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    try:
        # Copy database file
        import shutil
        shutil.copy2(db_path, backup_path)
        print(f"✅ Database backed up to: {backup_path}")
        return backup_path
    except Exception as e:
        print(f"❌ Failed to backup database: {e}")
        return None

def check_existing_indexes(cursor):
    """Check what indexes already exist."""
    print('[DEBUG] check_existing_indexes called')
    print("🔍 Checking existing indexes...")
    
    cursor.execute("""
        SELECT name, sql FROM sqlite_master 
        WHERE type='index' AND name NOT LIKE 'sqlite_%'
        ORDER BY name
    """)
    
    existing_indexes = cursor.fetchall()
    
    if existing_indexes:
        print("📋 Existing custom indexes:")
        for name, sql in existing_indexes:
            print(f"   - {name}")
    else:
        print("📋 No custom indexes found")
    
    return [name for name, sql in existing_indexes]

def create_performance_indexes(db_path):
    """Create performance indexes on key fields."""
    print('[DEBUG] create_performance_indexes called')
    print(f"🗄️ Adding performance indexes to database: {db_path}")
    
    # Backup database first
    backup_path = backup_database(db_path)
    if not backup_path:
        print("❌ Cannot proceed without backup")
        return False
    
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # Check existing indexes
            existing_indexes = check_existing_indexes(cursor)
            
            # Define indexes to create
            indexes_to_create = [
                # Clips table indexes
                {
                    'name': 'idx_clips_timestamp',
                    'sql': 'CREATE INDEX IF NOT EXISTS idx_clips_timestamp ON clips_tbl(timestamp DESC)',
                    'description': 'Optimize clips ordering by timestamp'
                },
                {
                    'name': 'idx_clips_alias',
                    'sql': 'CREATE INDEX IF NOT EXISTS idx_clips_alias ON clips_tbl(alias)',
                    'description': 'Optimize clips lookup by alias'
                },
                
                # More tables indexes
                {
                    'name': 'idx_more_bus_name',
                    'sql': 'CREATE INDEX IF NOT EXISTS idx_more_bus_name ON more_bus_tbl(bus_case)',
                    'description': 'Optimize business case lookup by name'
                },
                {
                    'name': 'idx_more_comp_bus_id',
                    'sql': 'CREATE INDEX IF NOT EXISTS idx_more_comp_bus_id ON more_comp_tbl(more_bus_id)',
                    'description': 'Optimize component lookup by business case'
                },
                {
                    'name': 'idx_more_comp_name',
                    'sql': 'CREATE INDEX IF NOT EXISTS idx_more_comp_name ON more_comp_tbl(bus_component)',
                    'description': 'Optimize component lookup by name'
                },
                {
                    'name': 'idx_more_comp_composite',
                    'sql': 'CREATE INDEX IF NOT EXISTS idx_more_comp_composite ON more_comp_tbl(more_bus_id, bus_component)',
                    'description': 'Optimize component lookup by business case and name'
                },
                
                # ClipsMore table indexes (most important for performance)
                {
                    'name': 'idx_clipsmore_clip_id',
                    'sql': 'CREATE INDEX IF NOT EXISTS idx_clipsmore_clip_id ON clipsmore_tbl(clip_id)',
                    'description': 'Optimize assignments lookup by clip'
                },
                {
                    'name': 'idx_clipsmore_alias',
                    'sql': 'CREATE INDEX IF NOT EXISTS idx_clipsmore_alias ON clipsmore_tbl(alias)',
                    'description': 'Optimize assignments lookup by alias (unique constraint)'
                },
                {
                    'name': 'idx_clipsmore_bus_id',
                    'sql': 'CREATE INDEX IF NOT EXISTS idx_clipsmore_bus_id ON clipsmore_tbl(more_bus_id)',
                    'description': 'Optimize assignments lookup by business case'
                },
                {
                    'name': 'idx_clipsmore_comp_id',
                    'sql': 'CREATE INDEX IF NOT EXISTS idx_clipsmore_comp_id ON clipsmore_tbl(more_comp_id)',
                    'description': 'Optimize assignments lookup by component'
                },
                {
                    'name': 'idx_clipsmore_tree_pos',
                    'sql': 'CREATE INDEX IF NOT EXISTS idx_clipsmore_tree_pos ON clipsmore_tbl(tree_position)',
                    'description': 'Optimize tree display ordering'
                },
                {
                    'name': 'idx_clipsmore_created',
                    'sql': 'CREATE INDEX IF NOT EXISTS idx_clipsmore_created ON clipsmore_tbl(created_date DESC)',
                    'description': 'Optimize assignments ordering by creation date'
                },
                {
                    'name': 'idx_clipsmore_modified',
                    'sql': 'CREATE INDEX IF NOT EXISTS idx_clipsmore_modified ON clipsmore_tbl(modified_date DESC)',
                    'description': 'Optimize assignments ordering by modification date'
                },
                
                # Composite indexes for common query patterns
                {
                    'name': 'idx_clipsmore_bus_comp',
                    'sql': 'CREATE INDEX IF NOT EXISTS idx_clipsmore_bus_comp ON clipsmore_tbl(more_bus_id, more_comp_id)',
                    'description': 'Optimize assignments lookup by business case and component'
                },
                {
                    'name': 'idx_clipsmore_bus_tree',
                    'sql': 'CREATE INDEX IF NOT EXISTS idx_clipsmore_bus_tree ON clipsmore_tbl(more_bus_id, tree_position)',
                    'description': 'Optimize tree display for business cases'
                },
                {
                    'name': 'idx_clipsmore_comp_tree',
                    'sql': 'CREATE INDEX IF NOT EXISTS idx_clipsmore_comp_tree ON clipsmore_tbl(more_comp_id, tree_position)',
                    'description': 'Optimize tree display for components'
                }
            ]
            
            # Create indexes
            created_count = 0
            skipped_count = 0
            
            print("\n🔨 Creating indexes...")
            
            for index_info in indexes_to_create:
                index_name = index_info['name']
                index_sql = index_info['sql']
                description = index_info['description']
                
                if index_name in existing_indexes:
                    print(f"⏭️  Skipping {index_name} (already exists)")
                    skipped_count += 1
                    continue
                
                try:
                    cursor.execute(index_sql)
                    print(f"✅ Created {index_name}: {description}")
                    created_count += 1
                except Exception as e:
                    print(f"❌ Failed to create {index_name}: {e}")
            
            # Commit changes
            conn.commit()
            
            print(f"\n📊 Index creation summary:")
            print(f"   - Created: {created_count} indexes")
            print(f"   - Skipped: {skipped_count} indexes (already existed)")
            print(f"   - Total: {len(indexes_to_create)} indexes defined")
            
            # Analyze database for query optimization
            print("\n🔍 Analyzing database for query optimization...")
            cursor.execute("ANALYZE")
            conn.commit()
            print("✅ Database analysis complete")
            
            # Show final index status
            print("\n📋 Final index status:")
            final_indexes = check_existing_indexes(cursor)
            print(f"   Total custom indexes: {len(final_indexes)}")
            
            return True
            
    except Exception as e:
        print(f"❌ Failed to create indexes: {e}")
        return False

def verify_indexes(db_path):
    """Verify that indexes were created successfully."""
    print('[DEBUG] verify_indexes called')
    print("\n🔍 Verifying index creation...")
    
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # Get index information
            cursor.execute("""
                SELECT name, tbl_name, sql 
                FROM sqlite_master 
                WHERE type='index' AND name NOT LIKE 'sqlite_%'
                ORDER BY tbl_name, name
            """)
            
            indexes = cursor.fetchall()
            
            if not indexes:
                print("❌ No custom indexes found")
                return False
            
            # Group by table
            tables = {}
            for name, table, sql in indexes:
                if table not in tables:
                    tables[table] = []
                tables[table].append(name)
            
            print("📊 Indexes by table:")
            for table, index_list in tables.items():
                print(f"   {table}: {len(index_list)} indexes")
                for index_name in index_list:
                    print(f"      - {index_name}")
            
            # Test query performance with EXPLAIN QUERY PLAN
            print("\n🚀 Testing query performance...")
            
            test_queries = [
                "SELECT * FROM clips_tbl ORDER BY timestamp DESC LIMIT 10",
                "SELECT * FROM clipsmore_tbl WHERE more_bus_id = 1",
                "SELECT * FROM clipsmore_tbl WHERE alias = 'test'",
                "SELECT * FROM clipsmore_vw WHERE more_bus_id = 1 ORDER BY tree_position"
            ]
            
            for query in test_queries:
                cursor.execute(f"EXPLAIN QUERY PLAN {query}")
                plan = cursor.fetchall()
                uses_index = any("USING INDEX" in str(row) for row in plan)
                status = "✅ Uses index" if uses_index else "⚠️  Table scan"
                print(f"   {status}: {query[:50]}...")
            
            return True
            
    except Exception as e:
        print(f"❌ Index verification failed: {e}")
        return False

def main():
    """Main function to add indexes to ClipsMore database."""
    print('[DEBUG] main called')
    print("🗄️ ClipsMore Database Index Creation")
    print("=" * 50)
    
    # Determine database path
    db_path = "clipsmore_db.db"
    if not os.path.exists(db_path):
        print(f"❌ Database file not found: {db_path}")
        print("   Make sure you're running this script from the DB directory")
        return False
    
    print(f"📁 Database path: {os.path.abspath(db_path)}")
    
    # Check database file size
    file_size = os.path.getsize(db_path)
    print(f"📊 Database size: {file_size:,} bytes ({file_size/1024/1024:.2f} MB)")
    
    # Create indexes
    success = create_performance_indexes(db_path)
    
    if success:
        # Verify indexes
        verify_success = verify_indexes(db_path)
        
        if verify_success:
            print("\n🎉 Index creation completed successfully!")
            print("🚀 Database is now optimized for better performance")
            
            # Show new file size
            new_file_size = os.path.getsize(db_path)
            size_increase = new_file_size - file_size
            print(f"📊 New database size: {new_file_size:,} bytes ({new_file_size/1024/1024:.2f} MB)")
            print(f"📈 Size increase: {size_increase:,} bytes ({size_increase/1024:.1f} KB)")
        else:
            print("\n⚠️ Index creation completed but verification failed")
    else:
        print("\n❌ Index creation failed")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
