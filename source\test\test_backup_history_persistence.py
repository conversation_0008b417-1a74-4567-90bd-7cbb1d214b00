#!/usr/bin/env python3
"""
Test script for backup history persistence across restore operations.
Verifies that backup history is preserved when restoring from backups.
"""

import os
import sys
import tempfile
import shutil
import sqlite3
from datetime import datetime

# Add parent directories to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backup.backup_manager import BackupManager
from backup.restore_manager import RestoreManager
from DB.db_connection import <PERSON>PoolManager


def test_backup_history_persistence():
    """Test that backup history persists across restore operations."""
    print('[TEST] Starting backup history persistence test')
    
    # Create temporary directory for test
    test_dir = tempfile.mkdtemp(prefix='clipmore_history_persistence_test_')
    print(f'[TEST] Using test directory: {test_dir}')
    
    try:
        # Create a temporary database for testing
        temp_db_path = os.path.join(test_dir, 'test_database.db')
        
        # Copy the current database to temp location
        current_db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'DB', 'clipsmore_db.db')
        if not os.path.exists(current_db_path):
            print('[ERROR] Main database not found, cannot run test')
            return False
        
        shutil.copy2(current_db_path, temp_db_path)
        print(f'[TEST] Created test database: {temp_db_path}')
        
        # Phase 1: Create initial backup and record history
        print('\n=== PHASE 1: CREATE INITIAL BACKUP ===')
        
        backup_manager = BackupManager()
        backup_manager.connection_pool = ConnectionPoolManager(temp_db_path)
        
        backup1_path = os.path.join(test_dir, 'backup1.db')
        backup1_success = backup_manager.create_backup(backup1_path, {
            'compression': False,
            'verification': True
        })
        
        if not backup1_success:
            print('[ERROR] Initial backup creation failed')
            return False
        
        print(f'[PHASE 1] ✅ Initial backup created: {backup1_path}')
        
        # Get initial backup history count
        initial_history = backup_manager.get_backup_history()
        initial_count = len(initial_history)
        print(f'[PHASE 1] Initial backup history count: {initial_count}')
        
        # Phase 2: Create additional backups to build up history
        print('\n=== PHASE 2: CREATE ADDITIONAL BACKUPS ===')
        
        backup2_path = os.path.join(test_dir, 'backup2.db')
        backup2_success = backup_manager.create_backup(backup2_path, {
            'compression': False,
            'verification': True
        })
        
        backup3_path = os.path.join(test_dir, 'backup3.db')
        backup3_success = backup_manager.create_backup(backup3_path, {
            'compression': False,
            'verification': True
        })
        
        if not (backup2_success and backup3_success):
            print('[ERROR] Additional backup creation failed')
            return False
        
        # Get updated backup history count
        updated_history = backup_manager.get_backup_history()
        updated_count = len(updated_history)
        print(f'[PHASE 2] ✅ Created additional backups. Total history count: {updated_count}')
        
        if updated_count <= initial_count:
            print('[ERROR] Backup history not growing as expected')
            return False
        
        # Phase 3: Restore from the first backup
        print('\n=== PHASE 3: RESTORE FROM FIRST BACKUP ===')
        
        restore_manager = RestoreManager()
        restore_manager.connection_pool = ConnectionPoolManager(temp_db_path)
        
        # Record the backup history before restore
        pre_restore_history = backup_manager.get_backup_history()
        pre_restore_count = len(pre_restore_history)
        print(f'[PHASE 3] Backup history before restore: {pre_restore_count} records')
        
        # Perform restore
        restore_success = restore_manager.restore_from_backup(backup1_path, {
            'verification': True,
            'backup_current': True,
            'restore_type': 'full'
        })
        
        if not restore_success:
            print('[ERROR] Restore operation failed')
            return False
        
        print('[PHASE 3] ✅ Restore operation completed')
        
        # Phase 4: Verify backup history persistence
        print('\n=== PHASE 4: VERIFY HISTORY PERSISTENCE ===')
        
        # Recreate backup manager with fresh connection to restored database
        post_restore_backup_manager = BackupManager()
        post_restore_backup_manager.connection_pool = ConnectionPoolManager(temp_db_path)
        
        # Get backup history after restore
        post_restore_history = post_restore_backup_manager.get_backup_history()
        post_restore_count = len(post_restore_history)
        print(f'[PHASE 4] Backup history after restore: {post_restore_count} records')
        
        # Verify that backup history was preserved
        if post_restore_count == 0:
            print('[ERROR] All backup history was lost during restore!')
            return False
        
        if post_restore_count < pre_restore_count:
            print(f'[WARNING] Some backup history was lost. Before: {pre_restore_count}, After: {post_restore_count}')
            # This might be expected if some backups were created after the backup point
        else:
            print('[PHASE 4] ✅ Backup history fully preserved')
        
        # Verify that we can still see the original backups
        backup_paths_preserved = [record.get('backup_path', '') for record in post_restore_history]
        
        original_backups_found = 0
        for original_path in [backup1_path, backup2_path, backup3_path]:
            if original_path in backup_paths_preserved:
                original_backups_found += 1
                print(f'[PHASE 4] ✅ Found preserved backup: {os.path.basename(original_path)}')
        
        if original_backups_found == 0:
            print('[ERROR] None of the original backups found in preserved history')
            return False
        
        # Phase 5: Test creating new backup after restore
        print('\n=== PHASE 5: TEST NEW BACKUP AFTER RESTORE ===')
        
        backup4_path = os.path.join(test_dir, 'backup_after_restore.db')
        backup4_success = post_restore_backup_manager.create_backup(backup4_path, {
            'compression': False,
            'verification': True
        })
        
        if not backup4_success:
            print('[ERROR] Creating backup after restore failed')
            return False
        
        # Verify new backup appears in history
        final_history = post_restore_backup_manager.get_backup_history()
        final_count = len(final_history)
        print(f'[PHASE 5] ✅ New backup created. Final history count: {final_count}')
        
        if final_count <= post_restore_count:
            print('[ERROR] New backup not added to history')
            return False
        
        print('\n[TEST] ✅ ALL BACKUP HISTORY PERSISTENCE TESTS PASSED!')
        
        # Print summary
        print('\n📋 TEST SUMMARY:')
        print(f'  • Initial backups created: 3')
        print(f'  • Backup history before restore: {pre_restore_count} records')
        print(f'  • Backup history after restore: {post_restore_count} records')
        print(f'  • Original backups preserved: {original_backups_found}/3')
        print(f'  • Final history count: {final_count} records')
        print('  • New backups can be created after restore: ✅')
        
        return True
        
    except Exception as e:
        print(f'[ERROR] Test failed with exception: {e}')
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # Clean up test directory
        if os.path.exists(test_dir):
            shutil.rmtree(test_dir)
            print(f'\n[CLEANUP] Test directory cleaned up: {test_dir}')


if __name__ == '__main__':
    print('=' * 70)
    print('BACKUP HISTORY PERSISTENCE TEST')
    print('Testing that backup history survives restore operations')
    print('=' * 70)
    
    success = test_backup_history_persistence()
    
    print('=' * 70)
    if success:
        print('🎉 BACKUP HISTORY PERSISTENCE TEST PASSED!')
        print('\nBackup history is now properly preserved across restore operations.')
        print('Users will no longer lose their backup audit trail when restoring.')
        print('\n✨ CRITICAL BUG FIXED ✨')
        sys.exit(0)
    else:
        print('❌ BACKUP HISTORY PERSISTENCE TEST FAILED!')
        print('The backup history preservation fix needs more work.')
        sys.exit(1)
