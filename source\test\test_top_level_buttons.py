#!/usr/bin/env python3
"""
Test script for top-level export and backup buttons.
Verifies that buttons are properly placed in the main toolbar and removed from More tab.
"""

import os
import sys
import tkinter as tk

# Add parent directories to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ui_manager import UIManager


def test_top_level_buttons_exist():
    """Test that export and backup buttons exist in the top frame."""
    print('[TEST] Starting top-level buttons existence test')
    
    try:
        # Create root window
        root = tk.Tk()
        root.withdraw()  # Hide the window
        
        # Create UI manager
        ui_manager = UIManager(root)
        
        # Check that top frame exists
        if not hasattr(ui_manager, 'top_frame') or not ui_manager.top_frame:
            print('[ERROR] Top frame not found')
            return False
        
        print(f'[TEST] Top frame found: {ui_manager.top_frame}')
        
        # Check that export and backup buttons were created
        if not hasattr(ui_manager, 'export_button') or not ui_manager.export_button:
            print('[ERROR] Export button not found in UI manager')
            return False
        
        if not hasattr(ui_manager, 'backup_button') or not ui_manager.backup_button:
            print('[ERROR] Backup button not found in UI manager')
            return False
        
        print('[TEST] ✅ Export and backup buttons found in UI manager')
        
        # Check button properties
        export_text = ui_manager.export_button.cget('text')
        backup_text = ui_manager.backup_button.cget('text')
        
        if '📤' not in export_text or 'Export' not in export_text:
            print(f'[ERROR] Export button has incorrect text: {export_text}')
            return False
        
        if '💾' not in backup_text or 'Backup' not in backup_text:
            print(f'[ERROR] Backup button has incorrect text: {backup_text}')
            return False
        
        print(f'[TEST] ✅ Button texts correct - Export: "{export_text}", Backup: "{backup_text}"')
        
        # Check button colors
        export_bg = ui_manager.export_button.cget('bg')
        backup_bg = ui_manager.backup_button.cget('bg')
        
        if export_bg != '#4caf50':
            print(f'[ERROR] Export button has incorrect background color: {export_bg}')
            return False
        
        if backup_bg != '#2196f3':
            print(f'[ERROR] Backup button has incorrect background color: {backup_bg}')
            return False
        
        print('[TEST] ✅ Button colors correct')
        
        # Clean up
        root.destroy()
        
        print('[TEST] ✅ Top-level buttons existence test passed!')
        return True
        
    except Exception as e:
        print(f'[ERROR] Test failed with exception: {e}')
        import traceback
        traceback.print_exc()
        return False


def test_buttons_removed_from_more_tab():
    """Test that export and backup buttons are removed from More tab."""
    print('\n[TEST] Starting More tab button removal test')
    
    try:
        # Create root window
        root = tk.Tk()
        root.withdraw()
        
        # Create UI manager to initialize More tab
        ui_manager = UIManager(root)
        
        # Get the More tab frame
        more_tab = ui_manager.more_tab
        if not more_tab:
            print('[ERROR] More tab not found')
            return False
        
        print('[TEST] More tab found, checking for removed buttons...')
        
        # Search through all widgets in the More tab for export/backup buttons
        def find_buttons_in_widget(widget, button_texts_found):
            """Recursively search for buttons with export/backup text."""
            try:
                # Check if this widget is a button with export/backup text
                if isinstance(widget, tk.Button):
                    text = widget.cget('text')
                    if ('📤' in text and 'Export' in text) or ('💾' in text and 'Backup' in text):
                        button_texts_found.append(text)
                        print(f'[WARNING] Found button in More tab: {text}')
                
                # Recursively check children
                for child in widget.winfo_children():
                    find_buttons_in_widget(child, button_texts_found)
                    
            except Exception as e:
                # Some widgets might not support certain operations
                pass
        
        button_texts_found = []
        find_buttons_in_widget(more_tab, button_texts_found)
        
        if button_texts_found:
            print(f'[ERROR] Found export/backup buttons in More tab: {button_texts_found}')
            return False
        
        print('[TEST] ✅ No export/backup buttons found in More tab')
        
        # Clean up
        root.destroy()
        
        print('[TEST] ✅ More tab button removal test passed!')
        return True
        
    except Exception as e:
        print(f'[ERROR] Test failed with exception: {e}')
        import traceback
        traceback.print_exc()
        return False


def test_button_functionality():
    """Test that the top-level buttons call the correct functions."""
    print('\n[TEST] Starting button functionality test')

    try:
        # Create root window
        root = tk.Tk()
        root.withdraw()

        # Create UI manager
        ui_manager = UIManager(root)

        # Verify buttons exist
        if not hasattr(ui_manager, 'export_button') or not ui_manager.export_button:
            print('[ERROR] Export button not found in UI manager')
            return False

        if not hasattr(ui_manager, 'backup_button') or not ui_manager.backup_button:
            print('[ERROR] Backup button not found in UI manager')
            return False

        # Test that buttons have the correct commands
        export_command = ui_manager.export_button.cget('command')
        backup_command = ui_manager.backup_button.cget('command')

        # In Tkinter, commands can be stored as strings, so we check if the method names are present
        export_command_str = str(export_command)
        backup_command_str = str(backup_command)

        # Check if the command strings contain the expected method names
        if 'open_export_dialog' not in export_command_str:
            print(f'[ERROR] Export button command does not contain "open_export_dialog": {export_command_str}')
            return False

        if 'open_backup_dialog' not in backup_command_str:
            print(f'[ERROR] Backup button command does not contain "open_backup_dialog": {backup_command_str}')
            return False

        print('[TEST] ✅ Export button command contains "open_export_dialog"')
        print('[TEST] ✅ Backup button command contains "open_backup_dialog"')

        # Test that the actual methods exist and are callable
        if not hasattr(ui_manager, 'open_export_dialog') or not callable(ui_manager.open_export_dialog):
            print('[ERROR] UIManager.open_export_dialog method is not callable')
            return False

        if not hasattr(ui_manager, 'open_backup_dialog') or not callable(ui_manager.open_backup_dialog):
            print('[ERROR] UIManager.open_backup_dialog method is not callable')
            return False

        print('[TEST] ✅ Export button method is callable')
        print('[TEST] ✅ Backup button method is callable')

        # Clean up
        root.destroy()

        print('[TEST] ✅ Button functionality test passed!')
        return True

    except Exception as e:
        print(f'[ERROR] Test failed with exception: {e}')
        import traceback
        traceback.print_exc()
        return False


def test_button_positioning():
    """Test that buttons are positioned correctly in the top frame."""
    print('\n[TEST] Starting button positioning test')
    
    try:
        # Create root window
        root = tk.Tk()
        root.withdraw()
        
        # Create UI manager
        ui_manager = UIManager(root)
        
        # Check that buttons are in the top frame
        top_frame_children = ui_manager.top_frame.winfo_children()
        
        # Look for the export_backup_frame
        export_backup_frame = None
        for child in top_frame_children:
            # Check if this frame contains our buttons
            frame_children = child.winfo_children()
            for frame_child in frame_children:
                if hasattr(frame_child, 'cget'):
                    try:
                        text = frame_child.cget('text')
                        if '📤' in text or '💾' in text:
                            export_backup_frame = child
                            break
                    except:
                        pass
            if export_backup_frame:
                break
        
        if not export_backup_frame:
            print('[ERROR] Export/backup button frame not found in top frame')
            return False
        
        print('[TEST] ✅ Export/backup button frame found in top frame')
        
        # Check that the frame is packed to the left side
        pack_info = export_backup_frame.pack_info()
        if pack_info.get('side') != 'left':
            print(f'[ERROR] Button frame not packed to left side: {pack_info}')
            return False
        
        print('[TEST] ✅ Button frame correctly positioned on left side')
        
        # Clean up
        root.destroy()
        
        print('[TEST] ✅ Button positioning test passed!')
        return True
        
    except Exception as e:
        print(f'[ERROR] Test failed with exception: {e}')
        import traceback
        traceback.print_exc()
        return False


if __name__ == '__main__':
    print('=' * 70)
    print('TOP-LEVEL EXPORT & BACKUP BUTTONS TEST')
    print('Testing UI integration and button placement')
    print('=' * 70)
    
    # Run tests
    test1_passed = test_top_level_buttons_exist()
    test2_passed = test_buttons_removed_from_more_tab()
    test3_passed = test_button_functionality()
    test4_passed = test_button_positioning()
    
    print('=' * 70)
    print('TEST RESULTS:')
    print(f'  Top-Level Buttons Exist: {"PASSED" if test1_passed else "FAILED"}')
    print(f'  Buttons Removed from More Tab: {"PASSED" if test2_passed else "FAILED"}')
    print(f'  Button Functionality: {"PASSED" if test3_passed else "FAILED"}')
    print(f'  Button Positioning: {"PASSED" if test4_passed else "FAILED"}')
    
    if test1_passed and test2_passed and test3_passed and test4_passed:
        print('  OVERALL: ALL TESTS PASSED ✅')
        print('\n🎯 UI ENHANCEMENT SUMMARY:')
        print('  • Export and Backup buttons moved to main toolbar')
        print('  • Buttons removed from More tab for cleaner interface')
        print('  • Consistent styling and positioning implemented')
        print('  • Always accessible regardless of current tab')
        print('  • Improved user experience with top-level access')
        print('\n✨ READY FOR PRODUCTION USE ✨')
        sys.exit(0)
    else:
        print('  OVERALL: SOME TESTS FAILED ❌')
        sys.exit(1)
